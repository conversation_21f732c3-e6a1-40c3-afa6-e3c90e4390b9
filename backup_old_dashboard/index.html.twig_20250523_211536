{% extends 'app/dashboard/page.html.twig' %}

{% set appliedFiltersCount = (filters.tracking_group_id is not empty ? 1 : 0) + filters.custom_filters|length %}

{% block title %}{{ 'profiles.social_profiles'|trans }} | {{ 'app.name'|trans }}{% endblock %}

{% block page_actions %}
    <form method="get" class="d-flex w-100">
        <div class="btn-list me-2">
            <button type="button" class="btn btn-primary d-none d-sm-inline-block" data-bs-toggle="modal" data-bs-target="#add-filters">
                <i class="fa fa-filter icon icon-2"></i> {{ 'profiles.filters'|trans }}
                {% if appliedFiltersCount > 0 %}
                    <span class="badge bg-green-lt ms-2">{{ appliedFiltersCount }}</span>
                {% endif %}
            </button>
            <div class="dropdown">
                <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fa fa-download icon icon-2"></i> {{ 'common.export'|trans }}
                </button>
                <div class="dropdown-menu">
                    <a class="dropdown-item" href="{{ path('app_social_profile_export', app.request.query.all) }}">
                        <i class="fa fa-file-csv me-2"></i> {{ 'common.export_csv'|trans }}
                    </a>
                </div>
            </div>
        </div>

        {% embed '@Tabler/embeds/modal.html.twig' with { id: 'add-filters' } %}
            {% block modal_title %}{{ 'profiles.add_filters'|trans }}{% endblock %}
            {% block modal_size 'modal_sm' %}
            {% block modal_body %}
                <h4>{{ 'profiles.static_filters'|trans }}</h4>
                <select name="group-id" class="form-control form-select mb-2">
                    <option value="">{{ 'profiles.filter_by_tracking_group'|trans }}</option>
                    {% for tg in tracking_groups %}
                        <option value="{{ tg.id }}" {% if filters.tracking_group_id == tg.id %}selected{% endif %}>{{ tg.name }}</option>
                    {% endfor %}
                </select>

                <select name="platform" class="form-control form-select mb-2">
                    <option value="all">{{ 'profiles.all_platforms'|trans }}</option>
                    {% for platform in data.availablePlatforms %}
                        <option value="{{ platform }}" {% if filters.platform == platform %}selected{% endif %}>{{ platform|capitalize }}</option>
                    {% endfor %}
                </select>

                <h4>{{ 'profiles.dynamic_filters'|trans }}</h4>
                <div id="filter-container">
                    {# Filters will be inserted here #}
                </div>
                <button type="button" class="btn btn-sm btn-success" onclick="addFilter()">+ {{ 'profiles.add_filter'|trans }}</button>

                <script>
                    let filterIndex = 0;

                    function addFilter(key = '', operator = 'equals', value = '') {
                        const container = document.getElementById('filter-container');
                        const group = document.createElement('div');
                        group.className = 'mb-2 input-group';
                        const inputId = `filters_${filterIndex}`;

                        group.innerHTML = `
                            <input type="text" name="filters[${filterIndex}][key]" class="form-control" placeholder="{{ 'profiles.field_placeholder'|trans }}" value="${key}">
                            <select name="filters[${filterIndex}][operator]" class="form-select" onchange="toggleValueInput(this)">
                                <option value="equals" ${operator === 'equals' ? 'selected' : ''}>{{ 'profiles.equals'|trans }}</option>
                                <option value="not_equals" ${operator === 'not_equals' ? 'selected' : ''}>{{ 'profiles.not_equals'|trans }}</option>
                                <option value="is_empty" ${operator === 'is_empty' ? 'selected' : ''}>{{ 'profiles.is_empty'|trans }}</option>
                                <option value="is_not_empty" ${operator === 'is_not_empty' ? 'selected' : ''}>{{ 'profiles.is_not_empty'|trans }}</option>
                            </select>
                            <input type="text" name="filters[${filterIndex}][value]" class="form-control ${operator.includes('empty') ? 'd-none' : ''}" placeholder="{{ 'profiles.value_placeholder'|trans }}" value="${value}" ${operator.includes('empty') ? 'disabled' : ''}>
                            <button type="button" class="btn btn-danger" onclick="this.parentElement.remove()">&times;</button>
                        `;
                        container.appendChild(group);
                        filterIndex++;
                    }

                    function toggleValueInput(selectElement) {
                        const valueInput = selectElement.parentElement.querySelector('input[name*="[value]"]');
                        const operator = selectElement.value;

                        if (operator === 'is_empty' || operator === 'is_not_empty') {
                            valueInput.disabled = true;
                            valueInput.value = '';
                            valueInput.classList.add('d-none');
                        } else {
                            valueInput.disabled = false;
                            valueInput.classList.remove('d-none');
                        }
                    }

                    // Load filters from query params
                    window.addEventListener('DOMContentLoaded', () => {
                        {% for filter in filters.custom_filters %}
                            addFilter('{{ filter.key|e('js') }}', '{{ filter.operator|e('js') }}', '{{ filter.value|e('js') }}');
                        {% endfor %}
                    });
                </script>
            {% endblock %}
            {% block modal_footer %}
                <a href="#" class="btn btn-link link-secondary btn-3" data-bs-dismiss="modal">{{ 'common.cancel'|trans }}</a>
                <button type="submit" id="apply-filters" class="btn btn-primary btn-5 ms-auto">
                    <i class="fa fa-filter icon icon-2"></i> {{ 'profiles.apply_filters'|trans }}
                </button>
            {% endblock %}
        {% endembed %}

        <div class="input-icon me-1" style="min-width: 200px;">
            <span class="input-icon-addon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                <path d="M21 21l-6 -6"></path>
              </svg>
            </span>
            <input type="text" name="q" value="{{ filters.search }}" class="form-control" placeholder="{{ 'profiles.search_placeholder'|trans }}" aria-label="Search">
        </div>
        <button type="submit" class="btn btn-primary">{{ 'common.apply'|trans }}</button>
    </form>
{% endblock %}

{% block page_content_before %}
    {% embed '@Tabler/embeds/modal.html.twig' with { id: 'm-cf-cf' } %}
        {% block modal_title %}{{ 'profiles.configure_custom_fields_bulk'|trans }}{% endblock %}
        {% block modal_size 'modal_sm' %}
        {% block modal_body %}
            <div class="modal-body">
                <div class="mb-3">
                    <label for="cf-in-add-cft" class="form-label">{{ 'profiles.add_custom_field_templates'|trans }}</label>
                    <select id="cf-in-add-cft" class="form-control form-select">
                        <option value="" selected>{{ 'profiles.select_custom_field_template'|trans }}</option>
                        {% for cf in data.availableCustomFieldTemplates %}
                            <option data-template="{{ cf.getJsonTemplate }}" value="{{ cf.id }}" >{{ cf.key }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <div id="custom-field-templates-container"></div>
                </div>
                <hr>
                <div class="mb-3">
                    <label for="cf-in-add-ocf" class="form-label">{{ 'profiles.add_org_custom_field'|trans }}</label>
                    <select id="cf-in-add-ocf" class="form-control form-select">
                        <option value="" selected>{{ 'profiles.select_org_custom_field'|trans }}</option>
                        {% for cf in data.availableCustomFields %}
                            <option data-template="{{ cf.getJsonTemplate }}" value="{{ cf.id }}">{{ cf.key }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <div id="org-custom-field-container"></div>
                </div>
            </div>
        {% endblock %}
        {% block modal_footer %}
            <a href="#" class="btn btn-link link-secondary btn-3" data-bs-dismiss="modal">{{ 'common.cancel'|trans }}</a>
            <button type="button" id="f-cf-cf" class="btn btn-primary btn-5 ms-auto">
                <i class="fa fa-gears icon icon-2"></i> {{ 'profiles.configure'|trans }}
            </button>
        {% endblock %}
    {% endembed %}

    <!-- Profile Statistics -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h1 m-0">{{ data.profileStats.total|number_format }}</div>
                                <div class="text-secondary">{{ 'profiles.total_profiles'|trans }}</div>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                {% for platformStat in data.profileStats.by_platform %}
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <div class="h3 m-0">{{ platformStat.count|number_format }}</div>
                                            <div class="text-secondary">{{ platformStat.platform|capitalize }}</div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination and Per Page Controls -->
    <div class="d-flex content w-100 justify-content-between align-items-center mb-3">
        <div class="d-flex align-items-center">
            <div class="form-check me-3">
                <input type="checkbox" id="selectAllProfiles" class="form-check-input">
                <label class="form-check-label" for="selectAllProfiles">{{ 'profiles.select_all'|trans }}</label>
            </div>
            <div class="text-secondary">
                {{ 'profiles.showing'|trans }} {{ ((pagination.current_page - 1) * pagination.items_per_page + 1)|number_format }}
                {{ 'profiles.to'|trans }} {{ min(pagination.current_page * pagination.items_per_page, pagination.total_items)|number_format }}
                {{ 'profiles.of'|trans }} {{ pagination.total_items|number_format }} {{ 'profiles.results'|trans }}
            </div>
        </div>
        <div class="d-flex align-items-center">
            <label class="form-label me-2 mb-0">{{ 'profiles.per_page'|trans }}:</label>
            <select class="form-select form-select-sm me-3" style="width: auto;" onchange="changePerPage(this.value)">
                <option value="20" {% if pagination.items_per_page == 20 %}selected{% endif %}>20</option>
                <option value="50" {% if pagination.items_per_page == 50 %}selected{% endif %}>50</option>
                <option value="100" {% if pagination.items_per_page == 100 %}selected{% endif %}>100</option>
            </select>
            <div class="btn-list flex-nowrap">
                <div class="dropdown">
                    <button class="btn dropdown-toggle align-text-top" data-bs-toggle="dropdown" aria-expanded="false">{{ 'profiles.actions'|trans }}</button>
                    <div class="dropdown-menu dropdown-menu-end" data-popper-placement="bottom-end">
                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#m-cf-cf">{{ 'profiles.configure_custom_fields'|trans }}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block page_content %}
    <div class="row row-cards">
        {% for profile in profiles %}
            <div class="col-md-6 col-xl-3 h-100">
                <label class="form-imagecheck mb-2 w-100">
                    <input name="form_selectedProfiles[]" type="checkbox" value="{{ profile.id }}" class="form-imagecheck-input profile-checkbox">
                    <span class="form-imagecheck-figure">
                        <div class="card">
                            {% set has_cover = ((profile.data.cover.source ?? null) is not null) %}
                            {% set profile_picture = profile.data.profile_picture_url ?? profile.data.picture.data.url ?? '#' %}

                            {% if has_cover %}
                                <div class="card-cover card-cover-blurred text-center" style="background-image: url({{ profile.data.cover.source ?? '#' }})">
                                    <span class="avatar avatar-xl avatar-thumb rounded" style="background-image: url({{ profile_picture }})"></span>
                                </div>
                            {% endif %}
                            <div class="card-body text-center">
                                {% if has_cover == false %}
                                    <div class="mb-3">
                                        <span class="avatar avatar-xl rounded" style="background-image: url({{ profile_picture }})"></span>
                                    </div>
                                {% endif %}
                                <div class="card-title mb-1">{{ profile.data.username ?? profile.data.name ?? profile.metaId }}</div>
                                <div class="text-secondary">{{ profile.platform|capitalize }}</div>
                                <div class="mt-2">
                                    <div class="row">
                                        <div class="col-4">
                                            <div class="text-center">
                                                <div class="h4 m-0">{{ (profile.data.media_count ?? 0)|number_format }}</div>
                                                <div class="text-secondary small">{{ 'profiles.posts'|trans }}</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-center">
                                                <div class="h4 m-0">{{ (profile.data.followers_count ?? 0)|number_format }}</div>
                                                <div class="text-secondary small">{{ 'profiles.followers'|trans }}</div>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="text-center">
                                                <div class="h4 m-0">{{ (profile.data.following_count ?? 0)|number_format }}</div>
                                                <div class="text-secondary small">{{ 'profiles.following'|trans }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <a href="{{ path('app_social_profile_view', { id: profile.id }) }}" class="card-btn">
                                <i class="fa fa-eye me-1"></i> {{ 'profiles.view'|trans }}
                            </a>
                        </div>
                    </span>
                </label>
            </div>
        {% else %}
            <div class="col-12">
                <div class="empty">
                    <div class="empty-img"><img src="{{ asset('static/illustrations/undraw_printing_invoices_5r4r.svg') }}" height="128" alt="">
                    </div>
                    <p class="empty-title">{{ 'profiles.no_profiles'|trans }}</p>
                    <p class="empty-subtitle text-secondary">
                        {{ 'profiles.no_profiles_desc'|trans }}
                    </p>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if pagination.total_pages > 1 %}
        <div class="d-flex justify-content-center mt-4">
            <ul class="pagination">
                {% if pagination.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="{{ path('app_social_profile_index', app.request.query.all|merge({page: pagination.previous_page})) }}">
                            <i class="fa fa-chevron-left"></i> {{ 'profiles.previous'|trans }}
                        </a>
                    </li>
                {% endif %}

                {% set start_page = max(1, pagination.current_page - 2) %}
                {% set end_page = min(pagination.total_pages, pagination.current_page + 2) %}

                {% if start_page > 1 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ path('app_social_profile_index', app.request.query.all|merge({page: 1})) }}">1</a>
                    </li>
                    {% if start_page > 2 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endif %}

                {% for page in start_page..end_page %}
                    <li class="page-item {% if page == pagination.current_page %}active{% endif %}">
                        <a class="page-link" href="{{ path('app_social_profile_index', app.request.query.all|merge({page: page})) }}">{{ page }}</a>
                    </li>
                {% endfor %}

                {% if end_page < pagination.total_pages %}
                    {% if end_page < pagination.total_pages - 1 %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                    <li class="page-item">
                        <a class="page-link" href="{{ path('app_social_profile_index', app.request.query.all|merge({page: pagination.total_pages})) }}">{{ pagination.total_pages }}</a>
                    </li>
                {% endif %}

                {% if pagination.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ path('app_social_profile_index', app.request.query.all|merge({page: pagination.next_page})) }}">
                            {{ 'profiles.next'|trans }} <i class="fa fa-chevron-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </div>
    {% endif %}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Custom field management (same as original)
            let iCustomFieldTemplateSelector = document.getElementById('cf-in-add-cft');
            let iOrganizationCustomFieldSelector = document.getElementById('cf-in-add-ocf');
            let cCustomFieldTemplates = document.getElementById('custom-field-templates-container');
            let cOrganizationCustomFields = document.getElementById('org-custom-field-container');

            // Select all functionality
            const selectAll = document.getElementById('selectAllProfiles');
            const checkboxes = document.querySelectorAll('.profile-checkbox');

            selectAll.addEventListener('change', function(e) {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
            });

            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const checkedCount = document.querySelectorAll('.profile-checkbox:checked').length;
                    if (checkedCount === 0) {
                        selectAll.checked = false;
                        selectAll.indeterminate = false;
                    } else if (checkedCount === checkboxes.length) {
                        selectAll.checked = true;
                        selectAll.indeterminate = false;
                    } else {
                        selectAll.indeterminate = true;
                    }
                });
            });

            // Bulk configure functionality
            document.getElementById('f-cf-cf').addEventListener('click', function() {
                submitAsForm({
                    formAction: '{{ path('app_social_profile_bulk_configure') }}',
                    formMethod: 'POST'
                });
            });
        });

        function changePerPage(limit) {
            const url = new URL(window.location);
            url.searchParams.set('limit', limit);
            url.searchParams.set('page', '1'); // Reset to first page
            window.location.href = url.toString();
        }

        function submitAsForm({
              inputsSelector = 'input, select, textarea',
              formAction = window.location.href,
              formMethod = 'POST',
              extraFields = {},
        } = {}) {
            const form = document.createElement('form');
            form.method = formMethod;
            form.action = formAction;
            form.style.display = 'none';

            const inputs = document.querySelectorAll(inputsSelector);

            inputs.forEach(input => {
                const { name, type, value, checked } = input;

                if (!name) return;

                if ((type === 'checkbox' || type === 'radio') && !checked) return;

                const hidden = document.createElement('input');
                hidden.type = 'hidden';
                hidden.name = name;
                hidden.value = value;

                form.appendChild(hidden);
            });

            for (const [key, val] of Object.entries(extraFields)) {
                const hidden = document.createElement('input');
                hidden.type = 'hidden';
                hidden.name = key;
                hidden.value = val;
                form.appendChild(hidden);
            }

            document.body.appendChild(form);
            form.submit();
        }
    </script>
{% endblock %}
