<?php

/**
 * Returns the importmap for this application.
 *
 * - "path" is a path inside the asset mapper system. Use the
 *     "debug:asset-map" command to see the full list of paths.
 *
 * - "entrypoint" (JavaScript only) set to true for any module that will
 *     be used as an "entrypoint" (and passed to the importmap() Twig function).
 *
 * The "importmap:require" command can be used to add new entries to this file.
 */
return [
    'app' => [
        'path' => './assets/app.js',
        'entrypoint' => true,
    ],
    'landing' => [
        'path' => './assets/landing.js',
        'entrypoint' => true,
    ],
    'dashboard' => [
        'path' => './assets/dashboard.js',
        'entrypoint' => true,
    ],
    'modern_dashboard' => [
        'path' => './assets/modern_dashboard.js',
        'entrypoint' => true,
    ],
    '@hotwired/stimulus' => [
        'version' => '3.2.2',
    ],
    '@symfony/stimulus-bundle' => [
        'path' => './vendor/symfony/stimulus-bundle/assets/dist/loader.js',
    ],
    '@hotwired/turbo' => [
        'version' => '7.3.0',
    ],
    '@symfony/ux-live-component' => [
        'path' => './vendor/symfony/ux-live-component/assets/dist/live_controller.js',
    ],
    'sortablejs' => [
        'version' => '1.15.6',
    ],
    'tailwindcss' => [
        'version' => '4.1.7',
    ],
    'tailwindcss/index.min.css' => [
        'version' => '4.1.7',
        'type' => 'css',
    ],
    'preline' => [
        'version' => '3.0.1',
    ],
    'aos' => [
        'version' => '2.3.4',
    ],
    'aos/dist/aos.css' => [
        'version' => '2.3.4',
        'type' => 'css',
    ],
    'preline/variants.css' => [
        'version' => '3.0.1',
        'type' => 'css',
    ],
];
