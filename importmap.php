<?php

/**
 * Returns the importmap for this application.
 *
 * - "path" is a path inside the asset mapper system. Use the
 *     "debug:asset-map" command to see the full list of paths.
 *
 * - "entrypoint" (JavaScript only) set to true for any module that will
 *     be used as an "entrypoint" (and passed to the importmap() Twig function).
 *
 * The "importmap:require" command can be used to add new entries to this file.
 */
return [
    'app' => [
        'path' => './assets/app.js',
        'entrypoint' => true,
    ],
    'tailwind-app' => [
        'path' => './assets/tailwind-app.js',
        'entrypoint' => true,
    ],
    'bootstrap' => [
        'version' => '5.3.3',
    ],
    '@popperjs/core' => [
        'version' => '2.11.8',
    ],
    'bootstrap/dist/css/bootstrap.min.css' => [
        'version' => '5.3.3',
        'type' => 'css',
    ],
    'jquery' => [
        'version' => '3.6.0',
    ],
    'imagesloaded' => [
        'version' => '4.1.4',
    ],
    'swiper' => [
        'version' => '11.1.4',
    ],
    'jquery.easing' => [
        'version' => '1.4.1',
    ],
    'anime' => [
        'version' => '0.1.2',
    ],
    'ev-emitter' => [
        'version' => '1.1.1',
    ],
    'animate' => [
        'version' => '1.0.0',
    ],
    '@fortawesome/fontawesome-free' => [
        'version' => '5.15.4',
    ],
    'rafl' => [
        'version' => '1.2.2',
    ],
    '@fortawesome/fontawesome-free/css/fontawesome.min.css' => [
        'version' => '5.15.4',
        'type' => 'css',
    ],
    'global' => [
        'version' => '4.3.2',
    ],
];
