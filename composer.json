{"name": "symfony/skeleton", "type": "project", "license": "proprietary", "description": "A minimal Symfony project recommended to create bare bones applications", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.12", "doctrine/doctrine-migrations-bundle": "^3.3", "doctrine/orm": "^3.1", "phpdocumentor/reflection-docblock": "^5.4", "phpstan/phpdoc-parser": "^1.29", "symfony/apache-pack": "^1.0", "symfony/asset": "7.0.*", "symfony/asset-mapper": "7.0.*", "symfony/console": "7.0.*", "symfony/doctrine-messenger": "7.0.*", "symfony/dotenv": "7.0.*", "symfony/expression-language": "7.0.*", "symfony/flex": "^2", "symfony/form": "7.0.*", "symfony/framework-bundle": "7.0.*", "symfony/http-client": "7.0.*", "symfony/intl": "7.0.*", "symfony/mailer": "7.0.*", "symfony/mime": "7.0.*", "symfony/monolog-bundle": "^3.0", "symfony/notifier": "7.0.*", "symfony/process": "7.0.*", "symfony/property-access": "7.0.*", "symfony/property-info": "7.0.*", "symfony/runtime": "7.0.*", "symfony/security-bundle": "7.0.*", "symfony/serializer": "7.0.*", "symfony/string": "7.0.*", "symfony/translation": "7.0.*", "symfony/twig-bundle": "7.0.*", "symfony/validator": "7.0.*", "symfony/web-link": "7.0.*", "symfony/yaml": "7.0.*", "twig/extra-bundle": "^2.12|^3.0", "twig/twig": "^2.12|^3.0"}, "config": {"allow-plugins": {"endroid/installer": true, "php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.0.*"}}, "require-dev": {"phpunit/phpunit": "^9.5", "symfony/browser-kit": "7.0.*", "symfony/css-selector": "7.0.*", "symfony/debug-bundle": "7.0.*", "symfony/maker-bundle": "^1.59", "symfony/phpunit-bridge": "^7.0", "symfony/stopwatch": "7.0.*", "symfony/web-profiler-bundle": "7.0.*"}}