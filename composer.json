{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.3", "ext-ctype": "*", "ext-fileinfo": "*", "ext-iconv": "*", "ext-pdo": "*", "arkounay/ux-collection": "^4.0", "aws/aws-sdk-php-symfony": "^2.8", "beberlei/doctrineextensions": "^1.5", "doctrine/dbal": "^3", "doctrine/doctrine-bundle": "^2.13", "doctrine/doctrine-migrations-bundle": "^3.4", "doctrine/orm": "^3.3", "dunglas/doctrine-json-odm": "^1.4", "kevinpapst/tabler-bundle": "^1.7", "openai-php/symfony": "^0.10.2", "phpdocumentor/reflection-docblock": "^5.6", "phpstan/phpdoc-parser": "^2.1", "scienta/doctrine-json-functions": "^6.3", "swaggest/json-diff": "^3.12", "symfony/apache-pack": "^1.0", "symfony/asset": "7.2.*", "symfony/asset-mapper": "7.2.*", "symfony/brevo-mailer": "7.2.*", "symfony/console": "7.2.*", "symfony/doctrine-messenger": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/flex": "^2", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/http-client": "7.2.*", "symfony/intl": "7.2.*", "symfony/mailer": "7.2.*", "symfony/mime": "7.2.*", "symfony/monolog-bundle": "^3.0", "symfony/notifier": "7.2.*", "symfony/process": "7.2.*", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/serializer": "7.2.*", "symfony/stimulus-bundle": "^2.23", "symfony/string": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/uid": "7.2.*", "symfony/ux-icons": "*", "symfony/ux-live-component": "^2.23", "symfony/ux-turbo": "^2.23", "symfony/validator": "7.2.*", "symfony/web-link": "7.2.*", "symfony/yaml": "7.2.*", "symfonycasts/reset-password-bundle": "^1.23", "symfonycasts/tailwind-bundle": "^0.10.0", "symfonycasts/verify-email-bundle": "^1.17", "twig/extra-bundle": "^3.20", "twig/intl-extra": "^3.20", "twig/string-extra": "^3.20", "twig/twig": "^2.12|^3.0"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "require-dev": {"phpunit/phpunit": "^9.5", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "^1.0", "symfony/phpunit-bridge": "^7.2", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*"}}