# Magnora - Data Intelligence Platform

Magnora is a comprehensive data intelligence platform that collects, transforms, enriches, stores, and serves data from various social media sources. The platform is designed to provide valuable insights through AI analysis and visualization tools.

## Overview

Magnora allows users to track social media profiles by organizing them into groups. The platform currently supports:

- **Facebook Pages**: Track with customizable date ranges
- **Instagram Business Profiles**: Track with configurable maximum post limits

Each tracking group can be set to automatically update every hour with new data or be manually updated as needed. The platform also offers AI analysis capabilities for deeper insights into the collected data.

## Key Features

### Data Collection

- **Multi-platform Support**: Currently supports Facebook and Instagram with plans to expand to other platforms
- **Flexible Tracking Options**: Configure date ranges for Facebook and post limits for Instagram
- **Automatic Updates**: Set tracking groups to update automatically or manually
- **Custom Fields**: Define and track custom fields at the organization, tracking group, and profile levels

### Data Management

- **Profile Management**: View all tracked profiles with filtering and search capabilities
- **Single Profile Visualization**: Detailed view of individual profiles with metrics and content
- **Organization Management**: Multi-user organizations with role-based permissions

### AI Analysis

- **Profile Analysis**: AI-powered analysis of profile data, images, and content
- **Media Analysis**: Analyze posts, images, and videos for deeper insights
- **Text Analysis**: Extract context and insights from textual content

### Credit System

- **Credit-based SAAS Model**: Operations consume credits from the organization's balance
- **Usage Tracking**: Detailed tracking of credit consumption by profiles, posts, and comments

## Planned Features

- **Business Tracking**: Track multiple sources (Facebook, Instagram, etc.) for a single business entity
- **Virtual Merge**: Link multiple profiles to the same business or person
- **Additional Data Sources**: YouTube, news sites, hashtags, and more
- **Search Metrics**: Track search performance and visibility
- **News Citations**: Monitor when profiles are mentioned in news articles
- **Advanced Analytics**: Dashboards and visualization tools for comprehensive data analysis

## Technical Stack

- **Framework**: Symfony 7.2
- **Database**: PostgreSQL
- **Frontend**: Twig templates with Tabler UI
- **Storage**: AWS S3 for profile data storage
- **AI Integration**: OpenAI API for analysis
- **Authentication**: Custom user and organization management

## Getting Started

### Prerequisites

- PHP 8.3 or higher
- Composer
- PostgreSQL
- AWS S3 credentials
- OpenAI API key

### Installation

1. Clone the repository
2. Install dependencies:
   ```
   composer install
   ```
3. Configure environment variables in `.env.local`:
   ```
   DATABASE_URL=postgresql://user:password@localhost:5432/magnora
   AWS_KEY=your_aws_key
   AWS_SECRET=your_aws_secret
   AWS_REGION=your_aws_region
   AWS_BUCKET=your_aws_bucket
   OPENAI_API_KEY=your_openai_api_key
   ```
4. Create the database:
   ```
   php bin/console doctrine:database:create
   php bin/console doctrine:migrations:migrate
   ```
5. Start the development server:
   ```
   symfony server:start
   ```

## Usage

1. Create an organization
2. Add users to the organization
3. Create tracking groups for Facebook pages or Instagram business profiles
4. Configure tracking parameters (date ranges, post limits, etc.)
5. Start tracking and analyzing profiles

## License

Proprietary - All rights reserved
