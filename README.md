# PostCHAT - Smart Sales Platform

A modern platform for borderless, stock-free, and staff-free sales. Built on the concept of dropshipping and the empowerment of social networks.

## Overview

PostCHAT is a native/web app that connects to the Terra platform, enabling businesses to sell products without maintaining inventory. The platform automates the ordering and shipping process, integrates with social networks for targeted marketing, and provides intelligent data analysis for sales forecasting.

## Features

- **Zero Inventory**: Suppliers store, package, and ship products directly to your customers
- **Zero Staff**: Automated platform handles most processes without additional employees
- **Zero Risk**: Only purchase products after customers have ordered and paid
- **Guaranteed Profit**: Benefit from the price difference between supplier and customer prices

## Technology Stack

- **Frontend**: Next.js with TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: React Icons
- **UI Components**: Custom components with Headless UI

## Getting Started

First, install the dependencies:

```bash
npm install
```

Then, run the development server:

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Project Structure

- `/src/components`: Reusable UI components
- `/src/app`: Next.js app router pages
- `/public/assets`: Static assets (images, icons, etc.)

## Design Principles

- **Responsive**: Optimized for all screen sizes
- **SEO-friendly**: Proper metadata and semantic HTML
- **Accessible**: Following web accessibility best practices
- **Performance**: Optimized for fast loading and smooth interactions

## Branding

- **Primary Color**: Red (#ff0000)
- **Logo**: "Post" in white on red background, "CHAT" in red

## Connect with PostCHAT

- **Website**: [www.post-chat.com](https://www.post-chat.com)
- **WebChat**: [Terra WebChat](https://my.terra.marketing/webchat/?p=1788388)
- **App**: Android app available (prototype)
