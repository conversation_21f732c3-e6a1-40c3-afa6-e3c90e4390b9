{% extends '@Tabler/security/registration.html.twig' %}

{% block title %}{{ 'auth.register.title'|trans }}{% endblock %}
{% block logo_login %}{{ 'app.name'|trans }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .sf-toolbar {
            display: none !important;
        }

        .card-body {
            padding: 2rem;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .form-label {
            font-weight: 500;
        }

        .btn-primary {
            padding: 0.5rem 1.5rem;
        }
    </style>
{% endblock %}

{% block registration_title %}{{ 'auth.register.title'|trans }}{% endblock %}
{% block registration_subtitle %}{{ 'auth.register.subtitle'|trans }}{% endblock %}

{% block registration_form %}
    {{ form_start(form) }}
        {{ form_row(form.name, {
            label: 'common.name'|trans
        }) }}
        {{ form_row(form.email, {
            label: 'common.email'|trans
        }) }}
        {{ form_row(form.organization, {
            label: 'common.organization'|trans
        }) }}
        {{ form_row(form.plainPassword, {
            label: 'common.password'|trans
        }) }}
        {{ form_row(form.agreeTerms, {
            label: 'auth.register.terms'|trans
        }) }}

        <div class="form-footer">
            <button type="submit" class="btn btn-primary w-100">{{ 'auth.register.button'|trans }}</button>
        </div>
    {{ form_end(form) }}
{% endblock %}

{% block registration_login %}
    <div class="text-center text-secondary mt-3">
        {{ 'auth.register.already_account'|trans }} <a href="{{ path('app_login') }}" tabindex="-1">{{ 'auth.register.sign_in'|trans }}</a>
    </div>
{% endblock %}