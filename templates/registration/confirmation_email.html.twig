<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{ 'auth.verify_email.title'|trans }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #206bc4;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #206bc4;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">{{ 'app.name'|trans }}</div>
    </div>

    <h1>{{ 'Welcome to'|trans }} {{ 'app.name'|trans }}!</h1>

    <p>{{ 'Thank you for registering. Please confirm your email address by clicking the button below:'|trans }}</p>

    <div style="text-align: center;">
        <a class="button" href="{{ signedUrl|raw }}">{{ 'Confirm my Email'|trans }}</a>
    </div>

    <p>{{ 'If the button doesn\'t work, copy and paste this URL into your browser:'|trans }}</p>

    <p><a href="{{ signedUrl|raw }}">{{ signedUrl|raw }}</a></p>

    <p>{{ 'This link will expire in'|trans }} {{ expiresAtMessageKey|trans(expiresAtMessageData, 'VerifyEmailBundle') }}.</p>

    <p>{{ 'Once your email is confirmed, you can start using all features of'|trans }} {{ 'app.name'|trans }}.</p>

    <div class="footer">
        <p>&copy; {{ 'now'|date('Y') }} {{ 'app.name'|trans }}. {{ 'All rights reserved.'|trans }}</p>
    </div>
</body>
</html>