{% apply spaceless %}
<!DOCTYPE html>
<html prefix="og: https://ogp.me/ns#" lang="{{ app.locale }}">
    <head>
        <title>{% block title %}PostCHAT - {{ 'home.meta.title_suffix'|trans }}{% endblock %}</title>
        <link rel="shortcut icon" type="image/x-icon" href="{{ asset('img/postchat-favicon.png') }}">

        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="title" content="{{ block('title') }}">
        <meta name="description" content="{{ 'home.meta.description'|trans }}">
        
	    <link rel="canonical" href="https://post-chat.com">
	
        <meta property="og:title" content="{{ block('title') }}">
        <meta property="og:type" content="website">

        <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "Organization",
                "name": "PostCHAT",
                "brand": "PostCHAT",
                "keywords": "delivery, logistics, seller, e-commerce, sales, marketing, automation, ai, chatbot",
                "logo": "{{ absolute_url(asset('img/logo/postchat-logo-512-min.png')) }}",
                "description": "{{ 'home.meta.description'|trans }}"
            }
        </script>

        {% block stylesheets %}
            <style>
                #switch-language {
                    font-size: 20px;
                    font-weight: var(--tg-fw-bold);
                    text-transform: capitalize;
                    color: var(--tg-heading-color);
                    margin: 25px 10px;
                    display: block;
                    line-height: 1;
                    position: relative;
                    z-index: 1;
                }
            </style>

            {% if app.request.locale == 'tr' %}
                <style>
                    .cta__content .title {
                        font-size: 32px !important;
                    }
                </style>
            {% endif %}
        {% endblock %}

        {% block javascripts %}
            {% block importmap %}{{ importmap('app') }}{% endblock %}

            {% block webchat %}
                <script src="https://chat.terra.marketing/webchat.js"></script>
                <script defer>
                    let webchat = new WebChat({
                        id: '1788388',
                        style: {
                            color: '#ff0000'
                        }
                    });
                    webchat.start();
                </script>
            {% endblock %}
        {% endblock %}

        {% block tags %}
            {#<!-- Google tag (gtag.js) -->
            <script async src="https://www.googletagmanager.com/gtag/js?id=G-BL39YMHLHG"></script>
            <script>
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());

                gtag('config', 'G-BL39YMHLHG');
            </script>#}
        {% endblock %}
    </head>
    <body>
        {% block body %}
        {% endblock %}
    </body>
</html>
{% endapply %}
