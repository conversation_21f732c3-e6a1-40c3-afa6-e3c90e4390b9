{% extends '@Tabler/security/login.html.twig' %}

{% block title %}{{ 'auth.login.title'|trans }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .sf-toolbar {
            display: none !important;
        }

        .card-body {
            padding: 2rem;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }

        .form-label {
            font-weight: 500;
        }

        .btn-primary {
            padding: 0.5rem 1.5rem;
        }
    </style>
{% endblock %}

{% block logo_login %}{{ 'app.name'|trans }}{% endblock %}
{% block login_social_auth %}{% endblock %}

{% block login_title %}{{ 'auth.login.title'|trans }}{% endblock %}
{% block login_subtitle %}{{ 'auth.login.subtitle'|trans }}{% endblock %}

{% block login_username_label %}{{ 'common.email'|trans }}{% endblock %}
{% block login_password_label %}{{ 'common.password'|trans }}{% endblock %}
{% block login_remember_me_label %}{{ 'auth.login.remember_me'|trans }}{% endblock %}
{% block login_forgot_password_link %}{{ 'auth.login.forgot_password'|trans }}{% endblock %}

{% block login_button %}{{ 'auth.login.button'|trans }}{% endblock %}

{% block login_register %}
    <div class="text-center text-secondary mt-3">
        {{ 'auth.login.no_account'|trans }} <a href="{{ path('app_register') }}" tabindex="-1">{{ 'auth.login.sign_up'|trans }}</a>
    </div>
{% endblock %}
