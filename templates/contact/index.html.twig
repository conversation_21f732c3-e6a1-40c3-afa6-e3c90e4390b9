{% extends 'base.html.twig' %}
{% trans_default_domain 'contact' %}

{% block title %}{{ 'contact.page.title'|trans({'{name}': guest.firstName ~ ' ' ~ guest.lastName}) }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <style>
        body {
            color: #ffffff !important;
            background: url("{{ asset('img/contact/card-bg.jpg') }}") center;
            background-size: cover;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            text-align: center;
        }

        .contact {
            background: rgba(0, 0, 0, 0.75);
            border-radius: 16px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(1.25px);
            -webkit-backdrop-filter: blur(1.25px);
            border: 1px solid rgba(0, 0, 0, 0.25);
        }

        .info .name,
        .info .company {
            color: #ffffff !important;
        }

        .info .name {
            font-size: 2.5rem;
            font-weight: 700;
        }

        .info .company {
            font-size: 2rem;
            font-weight: 600;
        }

        .socials .fa, .socials .fab {
            min-width: 20px;
        }

        .socials .btn {
            display: block;
            font-size: 1.5rem;
            font-weight: 300;
        }

        .btn-viber {
            color: #ffffff !important;
            background-color: indigo !important;
        }

        .btn-whatsapp {
            background-color: green !important;
        }

        /* Responsive adjustments */
        @media (max-width: 767.98px) {
            .info .name {
                font-size: 2rem;
            }

            .info .company {
                font-size: 1.5rem;
            }

            .socials .btn {
                font-size: 1.2rem;
            }
        }

        @media (max-width: 575.98px) {
            .contact {
                border-radius: 10px;
            }

            .info .name {
                font-size: 1.8rem;
            }

            .info .company {
                font-size: 1.3rem;
            }
        }
    </style>
{% endblock %}
{% block webchat %}{% endblock %}

{% block body %}
    <div class="container">
        <div class="row min-vh-100 d-flex justify-content-center align-items-center">
            <div class="col-12 col-md-8 col-lg-6">
                <div class="contact py-4 py-md-3">
                    <div class="info mb-5">
                        <h1 class="name">{{ guest.firstName ~ ' ' ~ guest.lastName }}</h1>
                        <h2 class="company">{{ guest.businessName }}</h2>
                    </div>
                    <div class="socials px-3">
                        <a href="{{ path('app_contact_vcard') }}" class="btn btn-primary mb-3"><i class="fa fa-user pe-1"></i> {{ 'contact.page.save_contact'|trans }}</a>
                        <a href="tel:{{ guest.phone }}" class="btn btn-success mb-3"><i class="fa fa-phone pe-1"></i> {{ guest.phone }}</a>
                        <a href="https://wa.me/{{ guest.phone|replace({'+': '', ' ': '', '-': ''}) }}" class="btn btn-success mb-3" target="_blank"><i class="fab fa-whatsapp pe-1"></i> {{ 'contact.page.whatsapp_chat'|trans }}</a>
                        {% if guest.email %}
                            <a href="mailto:{{ guest.email }}" class="btn btn-danger mb-3"><i class="fa fa-envelope pe-1"></i> {{ guest.email }}</a>
                        {% endif %}
                        {% if guest.website %}
                            <a href="{{ guest.website }}" class="btn btn-info mb-3" target="_blank"><i class="fa fa-globe pe-1"></i> {{ 'contact.page.website'|trans }}</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
