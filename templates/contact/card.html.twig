{% extends 'base.html.twig' %}
{% trans_default_domain 'contact' %}

{% block title %}{{ 'contact.card.title'|trans }}{% endblock %}

{% set v2 = false %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap" rel="stylesheet">
    <style>
        body {
            overflow: hidden !important;
        }
        
        p {
            color: #ffffff !important;
        }

        .access-card.v1 {
            position: relative;
            border-radius: 24px;
            overflow: hidden;
            padding: 1.5rem;
            background: #4f16c2;
            background: -moz-linear-gradient(180deg, rgba(79,22,194,1) 0%, rgba(46,0,138,1) 100%);
            background: -webkit-linear-gradient(180deg, rgba(79,22,194,1) 0%, rgba(46,0,138,1) 100%);
            background: linear-gradient(180deg, rgba(79,22,194,1) 0%, rgba(46,0,138,1) 100%);
            filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#4f16c2",endColorstr="#2e008a",GradientType=1);
            color: #ffffff;
            min-height: 600px;
        }

        .access-card.v1 .bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("{{ asset('img/contact/card-bg.jpg') }}") no-repeat center left;
            background-size: cover;
            opacity: 0.1;
            z-index: 1;
        }

        .access-card.v1 .header, .access-card.v1 .body {
            position: relative;
            z-index: 99;
        }

        .access-card.v1 .header .space .inner {
            width: 100px;
            height: 25px;
            background: #ffffff;
            border-radius: 50px;
            margin-bottom: 1.5rem;
        }

        .access-card.v1 .header .logo {
            padding: 0.5rem;
            filter: grayscale(1) invert(1) contrast(1) brightness(2);
        }

        .access-card.v1 .body h1 {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .access-card.v1 .body {
            margin-top: 80px;
            text-align: center;
        }

        .access-card.v1 .header .vip {
            margin-left: -1.5rem;
            margin-right: -1.5rem;
            background: red;
            padding: 1rem;
            font-size: 1.5rem;
            font-weight: 900;
            text-transform: none;
            text-align: center;
        }

        .access-card.v1 .header .vip h1 {
            color: #ffffff !important;
        }

            .access-card.v1 .body .info {
            margin-top: 1.5rem;
        }

        .access-card.v1 .body .info .guest-name {
            font-size: 1.25rem;
            font-weight: 700;
            text-transform: uppercase;
        }

        .access-card.v1 .body .info .business-name {
            font-size: 1rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .access-card.v1 .body .info .guest-seat span {
            color: #000000;
            background: #ffffff;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            border-radius: 50px;
        }

        .v2-pre {
            flex-wrap: wrap;
        }

        .access-card.v2 {
            position: relative;
        }

        .access-card.v2 .bg {
            position: relative;
            z-index: 1;
        }

        .access-card.v2 > div {
            position: absolute;
            z-index: 9;
        }

        .access-card.v2 .top-cut {
            top: 26px;
        }

        .access-card.v2 .top-cut .inner {
            width: 101px;
            height: 27px;
            background: #ffffff;
            border-radius: 50px;
            margin-bottom: 1.5rem;
        }

        .access-card.v2 .qr-code {
            top: 295px;
        }

        .access-card.v2 .info.names {
            top: 495px;
            color: #ffffff;
        }

        .access-card.v2 .info.seat {
            top: 566px;
        }

        .access-card.v2 .info p {
            text-align: center;
        }

        .access-card.v2 .info .guest-name {
            font-size: 1.1rem;
            font-weight: 900;
            text-transform: uppercase;
            letter-spacing: 0.05rem;
            filter: drop-shadow(0px 0px 1px #00000075);
        }

        .access-card.v2 .info .business-name {
            font-size: 1rem;
            font-weight: 500;
            text-transform: uppercase;
            filter: drop-shadow(0px 0px 1px #00000075);
        }

        .access-card.v2 .info .guest-seat {
            color: #000000;
            font-size: 0.75rem;
            font-weight: 600;
        }

        /* Responsive adjustments */
        @media (max-width: 767.98px) {
            .access-card.v2 .qr-code {
                top: 250px;
            }

            .access-card.v2 .info.names {
                top: 450px;
            }

            .access-card.v2 .info.seat {
                top: 520px;
            }

            .access-card.v2 .bg img {
                width: 100%;
                height: auto;
            }
        }

        @media (max-width: 575.98px) {
            .access-card.v2 .qr-code {
                top: 200px;
            }

            .access-card.v2 .qr-code img {
                width: 150px;
                height: 150px;
            }

            .access-card.v2 .info.names {
                top: 370px;
            }

            .access-card.v2 .info.seat {
                top: 430px;
            }
        }
    </style>
{% endblock %}
{% block webchat %}{% endblock %}

{% block body %}
    <div class="container-fluid {% if v2 is same as(true) %}p-0{% endif %}">
        {% if v2 is same as(true) %}
            <div class="v2-pre d-flex min-vw-100 min-vh-100 justify-content-center align-content-center">
                <div class="access-card v2">
                    <div class="bg">
                        <img src="{{ asset('img/contact/card-bg.jpg') }}" width="447px" height="673px" alt="">
                    </div>
                    <div class="top-cut w-100 d-flex justify-content-center"><div class="inner"></div></div>
                    <div class="qr-code d-flex w-100 justify-content-center">
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=180x180&data={{ absolute_url(path('app_contact')) }}" width="180px" height="180px" alt="">
                    </div>
                    <div class="info names d-grid w-100 justify-content-center">
                        <p class="guest-name mb-0">{{ guest.firstName }} {{ guest.lastName }}</p>
                        <p class="business-name">{{ guest.businessName }}</p>
                    </div>
                    <div class="info seat d-grid w-100 justify-content-center">
                        <p class="guest-seat"><span>{{ 'contact.card.section'|trans }} {{ guest.seat.section }} | {{ 'contact.card.row'|trans }} {{ row_letter }} | {{ 'contact.card.seat'|trans }} {{ guest.seat.number }}</span></p>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="row d-flex min-vh-100 justify-content-center align-content-center">
                <div class="col-12 col-md-6 col-lg-4">
                    <div class="access-card v1">
                        <div class="bg"></div>
                        <div class="header">
                            <div class="space w-100 d-flex justify-content-center"><div class="inner"></div></div>
                            {#<div class="w-100 d-flex justify-content-center">
                                <img src="https://post.terra.marketing/static/brand/favicon-min.png" width="80px" height="auto" class="logo" alt="">
                                <img src="https://terra.marketing/assets/img/logo.svg" width="100px" height="auto" class="logo" alt="">
                            </div>#}
                            <div class="vip my-4">
                                <h1 class="text-center">{{ 'contact.card.event_title'|trans }}</h1>
                            </div>
                        </div>
                        <div class="body">
                            <div class="qr-code d-flex w-100 justify-content-center">
                                <img src="{{ asset('img/contact/qr.jpg') }}" width="180px" height="180px" alt="">
                            </div>
                            <div class="info">
                                <p class="guest-name mb-0">{{ guest.firstName }} {{ guest.lastName }}</p>
                                <p class="business-name">{{ guest.businessName }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}
