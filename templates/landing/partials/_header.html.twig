<header id="header" class="fixed w-full z-50 transition-all duration-300 bg-transparent">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4 md:justify-start md:space-x-10">
            <div class="flex justify-start lg:w-0 lg:flex-1">
                <a href="{{ path('app_landing') }}" class="flex items-center">
                    <img class="h-8 w-auto sm:h-10 hidden dark:block" src="{{ asset('images/logo-white.svg') }}" alt="{{ 'app.name'|trans }}">
                    <img class="h-8 w-auto sm:h-10 block dark:hidden" src="{{ asset('images/logo.svg') }}" alt="{{ 'app.name'|trans }}">
                </a>
            </div>

            <div class="-mr-2 -my-2 md:hidden">
                <button type="button" class="bg-white dark:bg-transparent rounded-md p-2 inline-flex items-center justify-center text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500" aria-expanded="false" data-hs-overlay="#mobile-menu">
                    <span class="sr-only">Open menu</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>

            <nav class="hidden md:flex space-x-10">
                <a href="#features" class="nav-link-light text-base font-medium text-white hover:text-gray-200">{{ 'common.features'|trans }}</a>
                <a href="#use-cases" class="nav-link-light text-base font-medium text-white hover:text-gray-200">{{ 'common.use_cases'|trans }}</a>
                <a href="#how-it-works" class="nav-link-light text-base font-medium text-white hover:text-gray-200">{{ 'landing.how_it_works.title'|trans }}</a>
                <a href="#pricing" class="nav-link-light text-base font-medium text-white hover:text-gray-200">{{ 'common.pricing'|trans }}</a>
                <a href="#faq" class="nav-link-light text-base font-medium text-white hover:text-gray-200">FAQ</a>
            </nav>

            <div class="hidden md:flex items-center justify-end md:flex-1 lg:w-0">
                <a href="{{ path('app_login') }}" class="btn-login whitespace-nowrap text-base font-medium text-white hover:text-gray-200 border border-white border-opacity-30 rounded-full px-4 py-2 hover:bg-white hover:bg-opacity-10">
                    {{ 'common.login'|trans }}
                </a>
                <a href="{{ path('app_register') }}" class="ml-4 whitespace-nowrap inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-full shadow-sm text-base font-medium text-primary-700 bg-white hover:bg-gray-100">
                    {{ 'common.register'|trans }}
                </a>
            </div>
        </div>
    </div>

    <!-- Mobile menu -->
    <div id="mobile-menu" class="hs-overlay hidden w-full h-full fixed top-0 left-0 z-[60] overflow-x-hidden overflow-y-auto bg-white dark:bg-gray-900">
        <div class="hs-overlay-open:mt-0 mt-0 transition-all sm:max-w-lg sm:w-full m-3 sm:mx-auto">
            <div class="flex items-center justify-between p-4">
                <a href="{{ path('app_landing') }}" class="flex items-center">
                    <img class="h-8 w-auto" src="{{ asset('images/logo.svg') }}" alt="{{ 'app.name'|trans }}">
                </a>
                <button type="button" class="hs-overlay-close inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500" data-hs-overlay="#mobile-menu">
                    <span class="sr-only">Close menu</span>
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="px-4 pt-2 pb-8">
                <div class="space-y-6">
                    <a href="#features" class="block text-base font-medium text-gray-900 hover:text-primary-600">{{ 'common.features'|trans }}</a>
                    <a href="#use-cases" class="block text-base font-medium text-gray-900 hover:text-primary-600">{{ 'common.use_cases'|trans }}</a>
                    <a href="#how-it-works" class="block text-base font-medium text-gray-900 hover:text-primary-600">{{ 'landing.how_it_works.title'|trans }}</a>
                    <a href="#pricing" class="block text-base font-medium text-gray-900 hover:text-primary-600">{{ 'common.pricing'|trans }}</a>
                    <a href="#faq" class="block text-base font-medium text-gray-900 hover:text-primary-600">FAQ</a>

                    <div class="space-y-4 pt-4 border-t border-gray-200">
                        <a href="{{ path('app_login') }}" class="block text-base font-medium text-gray-900 hover:text-primary-600">
                            {{ 'common.login'|trans }}
                        </a>
                        <a href="{{ path('app_register') }}" class="w-full flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-600 hover:bg-primary-700">
                            {{ 'common.register'|trans }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
