<section id="features" class="bg-gray-50 py-16 md:py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <p class="text-base font-semibold text-primary-600 tracking-wide uppercase" data-aos="fade-up">{{ 'landing.features.subtitle'|trans }}</p>
            <h2 class="mt-2 text-3xl font-extrabold text-gray-900 tracking-tight sm:text-4xl" data-aos="fade-up" data-aos-delay="100">
                {{ 'landing.features.title'|trans }}
            </h2>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto" data-aos="fade-up" data-aos-delay="200">
                {{ 'landing.features.description'|trans }}
            </p>
        </div>

        <div class="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-4">
            <!-- Feature 1 -->
            <div class="relative bg-white p-6 rounded-2xl shadow-xl flex flex-col items-center text-center" data-aos="fade-up" data-aos-delay="300">
                <div class="absolute -top-10 rounded-full p-4 bg-primary-50 border border-primary-100">
                    <img src="{{ asset('images/icons/feature-1.svg') }}" alt="Global Reach" class="w-16 h-16">
                </div>
                <h3 class="mt-8 text-xl font-medium text-gray-900">{{ 'landing.features.items.0.title'|trans }}</h3>
                <p class="mt-2 text-base text-gray-500">{{ 'landing.features.items.0.description'|trans }}</p>
            </div>

            <!-- Feature 2 -->
            <div class="relative bg-white p-6 rounded-2xl shadow-xl flex flex-col items-center text-center" data-aos="fade-up" data-aos-delay="400">
                <div class="absolute -top-10 rounded-full p-4 bg-primary-50 border border-primary-100">
                    <img src="{{ asset('images/icons/feature-2.svg') }}" alt="AI-Powered" class="w-16 h-16">
                </div>
                <h3 class="mt-8 text-xl font-medium text-gray-900">{{ 'landing.features.items.1.title'|trans }}</h3>
                <p class="mt-2 text-base text-gray-500">{{ 'landing.features.items.1.description'|trans }}</p>
            </div>

            <!-- Feature 3 -->
            <div class="relative bg-white p-6 rounded-2xl shadow-xl flex flex-col items-center text-center" data-aos="fade-up" data-aos-delay="500">
                <div class="absolute -top-10 rounded-full p-4 bg-primary-50 border border-primary-100">
                    <img src="{{ asset('images/icons/feature-3.svg') }}" alt="Real-time Tracking" class="w-16 h-16">
                </div>
                <h3 class="mt-8 text-xl font-medium text-gray-900">{{ 'landing.features.items.2.title'|trans }}</h3>
                <p class="mt-2 text-base text-gray-500">{{ 'landing.features.items.2.description'|trans }}</p>
            </div>

            <!-- Feature 4 -->
            <div class="relative bg-white p-6 rounded-2xl shadow-xl flex flex-col items-center text-center" data-aos="fade-up" data-aos-delay="600">
                <div class="absolute -top-10 rounded-full p-4 bg-primary-50 border border-primary-100">
                    <img src="{{ asset('images/icons/feature-4.svg') }}" alt="Comprehensive Reports" class="w-16 h-16">
                </div>
                <h3 class="mt-8 text-xl font-medium text-gray-900">{{ 'landing.features.items.3.title'|trans }}</h3>
                <p class="mt-2 text-base text-gray-500">{{ 'landing.features.items.3.description'|trans }}</p>
            </div>
        </div>

        <!-- Feature Highlight -->
        <div class="mt-24 bg-white rounded-2xl shadow-xl overflow-hidden lg:grid lg:grid-cols-2 lg:gap-4" data-aos="fade-up">
            <div class="pt-10 pb-12 px-6 sm:pt-16 sm:px-16 lg:py-16 lg:pr-0 xl:py-20 xl:px-20">
                <div class="lg:self-center">
                    <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                        <span class="block">{{ 'landing.features.highlight.title'|trans }}</span>
                    </h2>
                    <p class="mt-4 text-lg leading-6 text-gray-500">
                        {{ 'landing.features.highlight.description'|trans }}
                    </p>
                    <a href="{{ path('app_register') }}" class="mt-8 bg-primary-600 border border-transparent rounded-md shadow px-5 py-3 inline-flex items-center text-base font-medium text-white hover:bg-primary-700">
                        {{ 'landing.features.highlight.cta'|trans }}
                    </a>
                </div>
            </div>
            <div class="relative -mt-6 aspect-w-5 aspect-h-3 md:aspect-w-2 md:aspect-h-1">
                <img class="transform translate-x-6 translate-y-6 rounded-md object-cover object-left-top sm:translate-x-16 lg:translate-y-20" src="{{ asset('images/dashboard-illustration.svg') }}" alt="App screenshot">
            </div>
        </div>
    </div>
</section>
