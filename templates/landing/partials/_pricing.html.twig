<section id="pricing" class="bg-gray-50 py-16 md:py-24 relative overflow-hidden">
    <!-- Background elements -->
    <div class="absolute top-0 right-0 -mt-16 -mr-16 w-64 h-64 bg-primary-100 rounded-full mix-blend-multiply filter blur-3xl opacity-70"></div>
    <div class="absolute bottom-0 left-0 -mb-16 -ml-16 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-70"></div>
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
            <p class="text-base font-semibold text-primary-600 tracking-wide uppercase" data-aos="fade-up">{{ 'pricing.subtitle'|trans(domain: 'landing') }}</p>
            <h2 class="mt-2 text-3xl font-extrabold text-gray-900 tracking-tight sm:text-4xl" data-aos="fade-up" data-aos-delay="100">
                {{ 'pricing.title'|trans(domain: 'landing') }}
            </h2>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto" data-aos="fade-up" data-aos-delay="200">
                {{ 'pricing.description'|trans(domain: 'landing') }}
            </p>
        </div>

        <!-- Toggle between monthly and yearly pricing -->
        <div class="mt-12 flex justify-center" data-aos="fade-up" data-aos-delay="300">
            <div class="relative bg-white rounded-full p-1 flex">
                <button type="button" class="relative py-2 px-6 border-transparent text-sm font-medium rounded-full text-primary-700 bg-primary-100 whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    {{ 'pricing.monthly'|trans(domain: 'landing') }}
                </button>
                <button type="button" class="ml-1 py-2 px-6 border-transparent text-sm font-medium rounded-full text-gray-700 whitespace-nowrap focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    {{ 'pricing.yearly'|trans(domain: 'landing') }}
                </button>
            </div>
        </div>

        <div class="mt-12 space-y-12 lg:space-y-0 lg:grid lg:grid-cols-3 lg:gap-x-8">
            <!-- Starter Plan -->
            <div class="relative p-8 bg-white border border-gray-200 rounded-2xl shadow-sm flex flex-col" data-aos="fade-up" data-aos-delay="400">
                <div class="flex-1">
                    <h3 class="text-xl font-semibold text-gray-900">{{ 'pricing.plans.0.name'|trans(domain: 'landing') }}</h3>
                    <p class="mt-4 flex items-baseline text-gray-900">
                        <span class="text-5xl font-extrabold tracking-tight">{{ 'pricing.plans.0.price'|trans(domain: 'landing') }}</span>
                        <span class="ml-1 text-xl font-semibold">{{ 'pricing.plans.0.period'|trans(domain: 'landing') }}</span>
                    </p>
                    <p class="mt-6 text-gray-500">{{ 'pricing.plans.0.description'|trans(domain: 'landing') }}</p>

                    <!-- Feature list -->
                    <ul role="list" class="mt-6 space-y-6">
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.0.features.0'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.0.features.1'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.0.features.2'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.0.features.3'|trans(domain: 'landing') }}</span>
                        </li>
                    </ul>
                </div>

                <a href="{{ path('app_register') }}" class="mt-8 block w-full bg-primary-50 border border-primary-100 rounded-md py-3 text-sm font-semibold text-primary-700 text-center hover:bg-primary-100">
                    {{ 'pricing.plans.0.cta'|trans(domain: 'landing') }}
                </a>
            </div>

            <!-- Professional Plan (Popular) -->
            <div class="relative p-8 bg-white border border-primary-500 rounded-2xl shadow-xl flex flex-col" data-aos="fade-up" data-aos-delay="500">
                <div class="absolute top-0 inset-x-0 transform translate-y-px">
                    <div class="flex justify-center transform -translate-y-1/2">
                        <span class="inline-flex rounded-full bg-primary-600 px-4 py-1 text-sm font-semibold tracking-wider uppercase text-white">
                            {{ 'pricing.plans.1.popular'|trans(domain: 'landing') }}
                        </span>
                    </div>
                </div>
                <div class="flex-1">
                    <h3 class="text-xl font-semibold text-gray-900">{{ 'pricing.plans.1.name'|trans(domain: 'landing') }}</h3>
                    <p class="mt-4 flex items-baseline text-gray-900">
                        <span class="text-5xl font-extrabold tracking-tight">{{ 'pricing.plans.1.price'|trans(domain: 'landing') }}</span>
                        <span class="ml-1 text-xl font-semibold">{{ 'pricing.plans.1.period'|trans(domain: 'landing') }}</span>
                    </p>
                    <p class="mt-6 text-gray-500">{{ 'pricing.plans.1.description'|trans(domain: 'landing') }}</p>

                    <!-- Feature list -->
                    <ul role="list" class="mt-6 space-y-6">
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.1.features.0'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.1.features.1'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.1.features.2'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.1.features.3'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.1.features.4'|trans(domain: 'landing') }}</span>
                        </li>
                    </ul>
                </div>

                <a href="{{ path('app_register') }}" class="mt-8 block w-full bg-primary-600 border border-transparent rounded-md py-3 text-sm font-semibold text-white text-center hover:bg-primary-700">
                    {{ 'pricing.plans.1.cta'|trans(domain: 'landing') }}
                </a>
            </div>

            <!-- Enterprise Plan -->
            <div class="relative p-8 bg-white border border-gray-200 rounded-2xl shadow-sm flex flex-col" data-aos="fade-up" data-aos-delay="600">
                <div class="flex-1">
                    <h3 class="text-xl font-semibold text-gray-900">{{ 'pricing.plans.2.name'|trans(domain: 'landing') }}</h3>
                    <p class="mt-4 flex items-baseline text-gray-900">
                        <span class="text-5xl font-extrabold tracking-tight">{{ 'pricing.plans.2.price'|trans(domain: 'landing') }}</span>
                        <span class="ml-1 text-xl font-semibold">{{ 'pricing.plans.2.period'|trans(domain: 'landing') }}</span>
                    </p>
                    <p class="mt-6 text-gray-500">{{ 'pricing.plans.2.description'|trans(domain: 'landing') }}</p>

                    <!-- Feature list -->
                    <ul role="list" class="mt-6 space-y-6">
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.2.features.0'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.2.features.1'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.2.features.2'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.2.features.3'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.2.features.4'|trans(domain: 'landing') }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 w-6 h-6 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                            <span class="ml-3 text-gray-500">{{ 'pricing.plans.2.features.5'|trans(domain: 'landing') }}</span>
                        </li>
                    </ul>
                </div>

                <a href="#" class="mt-8 block w-full bg-primary-50 border border-primary-100 rounded-md py-3 text-sm font-semibold text-primary-700 text-center hover:bg-primary-100">
                    {{ 'pricing.plans.2.cta'|trans(domain: 'landing') }}
                </a>
            </div>
        </div>
    </div>
</section>
