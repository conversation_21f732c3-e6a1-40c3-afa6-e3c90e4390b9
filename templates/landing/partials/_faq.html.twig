<section id="faq" class="bg-white py-16 md:py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <p class="text-base font-semibold text-primary-600 tracking-wide uppercase" data-aos="fade-up">{{ 'landing.faq.subtitle'|trans }}</p>
            <h2 class="mt-2 text-3xl font-extrabold text-gray-900 tracking-tight sm:text-4xl" data-aos="fade-up" data-aos-delay="100">
                {{ 'landing.faq.title'|trans }}
            </h2>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto" data-aos="fade-up" data-aos-delay="200">
                {{ 'landing.faq.description'|trans }}
            </p>
        </div>

        <div class="mt-12 max-w-3xl mx-auto divide-y-2 divide-gray-200">
            <div class="hs-accordion-group">
                <!-- FAQ Item 1 -->
                <div class="hs-accordion active" id="faq-1" data-aos="fade-up" data-aos-delay="300">
                    <button class="hs-accordion-toggle group pb-3 pt-6 inline-flex items-center justify-between gap-x-3 w-full text-left text-gray-800 transition hover:text-gray-500">
                        <h3 class="font-semibold text-lg text-gray-900">{{ 'landing.faq.items.0.question'|trans }}</h3>
                        <span class="hs-accordion-active:hidden block w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 5v14M5 12h14"/>
                            </svg>
                        </span>
                        <span class="hs-accordion-active:block hidden w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"/>
                            </svg>
                        </span>
                    </button>
                    <div id="faq-collapse-1" class="hs-accordion-content w-full overflow-hidden transition-[height] duration-300">
                        <p class="text-gray-600 pb-6">{{ 'landing.faq.items.0.answer'|trans }}</p>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="hs-accordion" id="faq-2" data-aos="fade-up" data-aos-delay="400">
                    <button class="hs-accordion-toggle group pb-3 pt-6 inline-flex items-center justify-between gap-x-3 w-full text-left text-gray-800 transition hover:text-gray-500">
                        <h3 class="font-semibold text-lg text-gray-900">{{ 'landing.faq.items.1.question'|trans }}</h3>
                        <span class="hs-accordion-active:hidden block w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 5v14M5 12h14"/>
                            </svg>
                        </span>
                        <span class="hs-accordion-active:block hidden w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"/>
                            </svg>
                        </span>
                    </button>
                    <div id="faq-collapse-2" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300">
                        <p class="text-gray-600 pb-6">{{ 'landing.faq.items.1.answer'|trans }}</p>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="hs-accordion" id="faq-3" data-aos="fade-up" data-aos-delay="500">
                    <button class="hs-accordion-toggle group pb-3 pt-6 inline-flex items-center justify-between gap-x-3 w-full text-left text-gray-800 transition hover:text-gray-500">
                        <h3 class="font-semibold text-lg text-gray-900">{{ 'landing.faq.items.2.question'|trans }}</h3>
                        <span class="hs-accordion-active:hidden block w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 5v14M5 12h14"/>
                            </svg>
                        </span>
                        <span class="hs-accordion-active:block hidden w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"/>
                            </svg>
                        </span>
                    </button>
                    <div id="faq-collapse-3" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300">
                        <p class="text-gray-600 pb-6">{{ 'landing.faq.items.2.answer'|trans }}</p>
                    </div>
                </div>

                <!-- FAQ Item 4 -->
                <div class="hs-accordion" id="faq-4" data-aos="fade-up" data-aos-delay="600">
                    <button class="hs-accordion-toggle group pb-3 pt-6 inline-flex items-center justify-between gap-x-3 w-full text-left text-gray-800 transition hover:text-gray-500">
                        <h3 class="font-semibold text-lg text-gray-900">{{ 'landing.faq.items.3.question'|trans }}</h3>
                        <span class="hs-accordion-active:hidden block w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 5v14M5 12h14"/>
                            </svg>
                        </span>
                        <span class="hs-accordion-active:block hidden w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"/>
                            </svg>
                        </span>
                    </button>
                    <div id="faq-collapse-4" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300">
                        <p class="text-gray-600 pb-6">{{ 'landing.faq.items.3.answer'|trans }}</p>
                    </div>
                </div>

                <!-- FAQ Item 5 -->
                <div class="hs-accordion" id="faq-5" data-aos="fade-up" data-aos-delay="700">
                    <button class="hs-accordion-toggle group pb-3 pt-6 inline-flex items-center justify-between gap-x-3 w-full text-left text-gray-800 transition hover:text-gray-500">
                        <h3 class="font-semibold text-lg text-gray-900">{{ 'landing.faq.items.4.question'|trans }}</h3>
                        <span class="hs-accordion-active:hidden block w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 5v14M5 12h14"/>
                            </svg>
                        </span>
                        <span class="hs-accordion-active:block hidden w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"/>
                            </svg>
                        </span>
                    </button>
                    <div id="faq-collapse-5" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300">
                        <p class="text-gray-600 pb-6">{{ 'landing.faq.items.4.answer'|trans }}</p>
                    </div>
                </div>

                <!-- FAQ Item 6 -->
                <div class="hs-accordion" id="faq-6" data-aos="fade-up" data-aos-delay="800">
                    <button class="hs-accordion-toggle group pb-3 pt-6 inline-flex items-center justify-between gap-x-3 w-full text-left text-gray-800 transition hover:text-gray-500">
                        <h3 class="font-semibold text-lg text-gray-900">{{ 'landing.faq.items.5.question'|trans }}</h3>
                        <span class="hs-accordion-active:hidden block w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 5v14M5 12h14"/>
                            </svg>
                        </span>
                        <span class="hs-accordion-active:block hidden w-7 h-7 border border-gray-200 rounded-full">
                            <svg class="w-7 h-7 text-gray-600" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M5 12h14"/>
                            </svg>
                        </span>
                    </button>
                    <div id="faq-collapse-6" class="hs-accordion-content hidden w-full overflow-hidden transition-[height] duration-300">
                        <p class="text-gray-600 pb-6">{{ 'landing.faq.items.5.answer'|trans }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
