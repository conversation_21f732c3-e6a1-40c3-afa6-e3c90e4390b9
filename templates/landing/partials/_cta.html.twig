<section class="relative cta-section">
    <!-- Background gradient -->
    <div class="absolute inset-0 bg-gradient-to-r from-primary-600 to-purple-600"></div>

    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 cta-content">
        <div class="bg-white rounded-2xl shadow-xl overflow-hidden">
            <div class="pt-10 pb-12 px-6 sm:pt-16 sm:px-16 lg:py-16 lg:pr-0 xl:py-20 xl:px-20 lg:flex lg:items-center lg:justify-between">
                <div data-aos="fade-right">
                    <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
                        <span class="block">{{ 'landing.cta.title'|trans }}</span>
                    </h2>
                    <p class="mt-4 text-lg leading-6 text-gray-500">
                        {{ 'landing.cta.subtitle'|trans }}
                    </p>
                    <div class="mt-8 flex">
                        <div class="inline-flex rounded-md shadow">
                            <a href="{{ path('app_register') }}" class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700">
                                {{ 'landing.cta.button'|trans }}
                            </a>
                        </div>
                        <div class="ml-3 inline-flex">
                            <a href="#" class="inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-primary-100 hover:bg-primary-200">
                                {{ 'landing.cta.secondary'|trans }}
                            </a>
                        </div>
                    </div>
                </div>
                <div class="mt-8 lg:mt-0 lg:flex-shrink-0" data-aos="fade-left">
                    <div class="relative mx-auto w-full rounded-lg shadow-lg lg:max-w-md">
                        <div class="relative block w-full bg-white rounded-lg overflow-hidden">
                            <img class="w-full" src="{{ asset('images/dashboard-illustration.svg') }}" alt="Dashboard">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <button type="button" class="flex items-center justify-center h-16 w-16 rounded-full bg-primary-600 text-white">
                                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
