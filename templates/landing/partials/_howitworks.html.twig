<section id="how-it-works" class="bg-gray-50 py-16 md:py-24 relative overflow-hidden">
    <!-- Background elements -->
    <div class="absolute top-0 right-0 -mt-16 -mr-16 w-64 h-64 bg-primary-100 rounded-full mix-blend-multiply filter blur-3xl opacity-70"></div>
    <div class="absolute bottom-0 left-0 -mb-16 -ml-16 w-64 h-64 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-70"></div>
    
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center">
            <p class="text-base font-semibold text-primary-600 tracking-wide uppercase" data-aos="fade-up">{{ 'landing.how_it_works.subtitle'|trans }}</p>
            <h2 class="mt-2 text-3xl font-extrabold text-gray-900 tracking-tight sm:text-4xl" data-aos="fade-up" data-aos-delay="100">
                {{ 'landing.how_it_works.title'|trans }}
            </h2>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto" data-aos="fade-up" data-aos-delay="200">
                {{ 'landing.how_it_works.description'|trans }}
            </p>
        </div>

        <div class="mt-20">
            <div class="lg:grid lg:grid-cols-12 lg:gap-8 items-center">
                <div class="relative z-10 lg:col-span-6 lg:text-left">
                    <div class="relative">
                        <div class="space-y-12">
                            <!-- Step 1 -->
                            <div class="flex" data-aos="fade-right" data-aos-delay="300">
                                <div class="flex-shrink-0">
                                    <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-600 text-white">
                                        <span class="text-lg font-bold">1</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-xl font-medium text-gray-900">{{ 'landing.how_it_works.steps.0.title'|trans }}</h3>
                                    <p class="mt-2 text-base text-gray-500">{{ 'landing.how_it_works.steps.0.description'|trans }}</p>
                                </div>
                            </div>

                            <!-- Step 2 -->
                            <div class="flex" data-aos="fade-right" data-aos-delay="400">
                                <div class="flex-shrink-0">
                                    <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-600 text-white">
                                        <span class="text-lg font-bold">2</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-xl font-medium text-gray-900">{{ 'landing.how_it_works.steps.1.title'|trans }}</h3>
                                    <p class="mt-2 text-base text-gray-500">{{ 'landing.how_it_works.steps.1.description'|trans }}</p>
                                </div>
                            </div>

                            <!-- Step 3 -->
                            <div class="flex" data-aos="fade-right" data-aos-delay="500">
                                <div class="flex-shrink-0">
                                    <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-600 text-white">
                                        <span class="text-lg font-bold">3</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-xl font-medium text-gray-900">{{ 'landing.how_it_works.steps.2.title'|trans }}</h3>
                                    <p class="mt-2 text-base text-gray-500">{{ 'landing.how_it_works.steps.2.description'|trans }}</p>
                                </div>
                            </div>

                            <!-- Step 4 -->
                            <div class="flex" data-aos="fade-right" data-aos-delay="600">
                                <div class="flex-shrink-0">
                                    <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-600 text-white">
                                        <span class="text-lg font-bold">4</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <h3 class="text-xl font-medium text-gray-900">{{ 'landing.how_it_works.steps.3.title'|trans }}</h3>
                                    <p class="mt-2 text-base text-gray-500">{{ 'landing.how_it_works.steps.3.description'|trans }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-10 -mx-4 relative lg:mt-0 lg:col-span-6" data-aos="fade-left" data-aos-delay="400">
                    <div class="relative mx-auto w-full rounded-lg shadow-lg lg:max-w-md">
                        <div class="relative block w-full bg-white rounded-lg overflow-hidden">
                            <img class="w-full" src="{{ asset('images/dashboard-illustration.svg') }}" alt="Dashboard">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <button type="button" class="flex items-center justify-center h-16 w-16 rounded-full bg-primary-600 text-white">
                                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
