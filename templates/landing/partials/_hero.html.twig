<section class="hero-section relative overflow-hidden bg-gradient-to-r from-primary-600 to-purple-600 pt-32 pb-24 md:pt-48 md:pb-32 flex items-center">
    <!-- Background pattern -->
    <div class="absolute inset-0 z-0 opacity-20" style="background-image: url('{{ asset('images/hero-pattern.svg') }}'); background-size: cover;"></div>

    <!-- Animated blobs -->
    <div class="absolute top-0 right-0 -mt-16 -mr-16 w-64 h-64 bg-purple-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob"></div>
    <div class="absolute top-0 left-0 -mt-16 -ml-16 w-72 h-72 bg-primary-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000"></div>
    <div class="absolute bottom-0 right-1/4 w-80 h-80 bg-indigo-500 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-4000"></div>

    <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:grid lg:grid-cols-12 lg:gap-8">
            <div class="sm:text-center md:max-w-2xl md:mx-auto lg:col-span-6 lg:text-left lg:flex lg:flex-col lg:justify-center">
                <h1 data-aos="fade-up" data-aos-delay="100" class="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl lg:text-5xl xl:text-6xl">
                    <span class="block">{{ 'landing.hero.title'|trans }}</span>
                    <span class="block text-indigo-200">{{ 'landing.hero.subtitle_highlight'|trans }}</span>
                </h1>
                <p data-aos="fade-up" data-aos-delay="200" class="mt-3 text-base text-indigo-100 sm:mt-5 sm:text-xl lg:text-lg xl:text-xl">
                    {{ 'landing.hero.subtitle'|trans }}
                </p>
                <div data-aos="fade-up" data-aos-delay="300" class="mt-8 sm:mt-10 sm:flex sm:justify-center lg:justify-start">
                    <div class="rounded-md shadow">
                        <a href="{{ path('app_register') }}" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-primary-700 bg-white hover:bg-gray-50 md:py-4 md:text-lg md:px-10">
                            {{ 'landing.hero.cta'|trans }}
                        </a>
                    </div>
                    <div class="mt-3 sm:mt-0 sm:ml-3">
                        <a href="#features" class="w-full flex items-center justify-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-700 bg-opacity-60 hover:bg-opacity-70 md:py-4 md:text-lg md:px-10">
                            {{ 'common.learn_more'|trans }}
                        </a>
                    </div>
                </div>

                <!-- Stats -->
                <div data-aos="fade-up" data-aos-delay="400" class="mt-12 grid grid-cols-3 gap-5 sm:gap-8">
                    <div class="text-center">
                        <p class="text-3xl font-extrabold text-white counter" data-target="10000">0</p>
                        <p class="text-sm text-indigo-100">{{ 'landing.hero.stats.users'|trans }}</p>
                    </div>
                    <div class="text-center">
                        <p class="text-3xl font-extrabold text-white counter" data-target="500000">0</p>
                        <p class="text-sm text-indigo-100">{{ 'landing.hero.stats.profiles'|trans }}</p>
                    </div>
                    <div class="text-center">
                        <p class="text-3xl font-extrabold text-white counter" data-target="99">0</p>
                        <p class="text-sm text-indigo-100">{{ 'landing.hero.stats.accuracy'|trans }}</p>
                    </div>
                </div>
            </div>

            <div data-aos="fade-left" data-aos-delay="300" class="mt-12 relative sm:max-w-lg sm:mx-auto lg:mt-0 lg:max-w-none lg:mx-0 lg:col-span-6 lg:flex lg:items-center">
                <div class="relative mx-auto w-full rounded-lg shadow-lg lg:max-w-md">
                    <div class="relative block w-full bg-white rounded-lg overflow-hidden">
                        <img class="w-full" src="{{ asset('images/dashboard-illustration.svg') }}" alt="Dashboard">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <button type="button" class="flex items-center justify-center h-16 w-16 rounded-full bg-primary-600 text-white">
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Wave divider -->
    <div class="absolute bottom-0 left-0 right-0">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" class="w-full h-auto">
            <path fill="#ffffff" fill-opacity="1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,149.3C960,160,1056,160,1152,138.7C1248,117,1344,75,1392,53.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
        </svg>
    </div>
</section>
