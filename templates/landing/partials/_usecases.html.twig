<section id="use-cases" class="bg-white py-16 md:py-24">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <p class="text-base font-semibold text-primary-600 tracking-wide uppercase" data-aos="fade-up">{{ 'landing.use_cases.subtitle'|trans }}</p>
            <h2 class="mt-2 text-3xl font-extrabold text-gray-900 tracking-tight sm:text-4xl" data-aos="fade-up" data-aos-delay="100">
                {{ 'landing.use_cases.title'|trans }}
            </h2>
            <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto" data-aos="fade-up" data-aos-delay="200">
                {{ 'landing.use_cases.description'|trans }}
            </p>
        </div>

        <div class="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <!-- Business Use Case -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="300">
                <div class="p-8">
                    <div class="flex items-center">
                        <img src="{{ asset('images/icons/business.svg') }}" alt="Business" class="w-16 h-16">
                        <h3 class="ml-4 text-xl font-medium text-gray-900">{{ 'landing.use_cases.items.0.title'|trans }}</h3>
                    </div>
                    <p class="mt-4 text-base text-gray-500">{{ 'landing.use_cases.items.0.description'|trans }}</p>
                    <ul class="mt-6 space-y-3">
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.0.features.0'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.0.features.1'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.0.features.2'|trans }}</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Competitors Use Case -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="400">
                <div class="p-8">
                    <div class="flex items-center">
                        <img src="{{ asset('images/icons/competitors.svg') }}" alt="Competitors" class="w-16 h-16">
                        <h3 class="ml-4 text-xl font-medium text-gray-900">{{ 'landing.use_cases.items.1.title'|trans }}</h3>
                    </div>
                    <p class="mt-4 text-base text-gray-500">{{ 'landing.use_cases.items.1.description'|trans }}</p>
                    <ul class="mt-6 space-y-3">
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.1.features.0'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.1.features.1'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.1.features.2'|trans }}</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Brands Use Case -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="500">
                <div class="p-8">
                    <div class="flex items-center">
                        <img src="{{ asset('images/icons/brands.svg') }}" alt="Brands" class="w-16 h-16">
                        <h3 class="ml-4 text-xl font-medium text-gray-900">{{ 'landing.use_cases.items.2.title'|trans }}</h3>
                    </div>
                    <p class="mt-4 text-base text-gray-500">{{ 'landing.use_cases.items.2.description'|trans }}</p>
                    <ul class="mt-6 space-y-3">
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.2.features.0'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.2.features.1'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.2.features.2'|trans }}</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Influencers Use Case -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="600">
                <div class="p-8">
                    <div class="flex items-center">
                        <img src="{{ asset('images/icons/influencers.svg') }}" alt="Influencers" class="w-16 h-16">
                        <h3 class="ml-4 text-xl font-medium text-gray-900">{{ 'landing.use_cases.items.3.title'|trans }}</h3>
                    </div>
                    <p class="mt-4 text-base text-gray-500">{{ 'landing.use_cases.items.3.description'|trans }}</p>
                    <ul class="mt-6 space-y-3">
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.3.features.0'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.3.features.1'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-orange-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.3.features.2'|trans }}</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Politics Use Case -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="700">
                <div class="p-8">
                    <div class="flex items-center">
                        <img src="{{ asset('images/icons/politics.svg') }}" alt="Politics" class="w-16 h-16">
                        <h3 class="ml-4 text-xl font-medium text-gray-900">{{ 'landing.use_cases.items.4.title'|trans }}</h3>
                    </div>
                    <p class="mt-4 text-base text-gray-500">{{ 'landing.use_cases.items.4.description'|trans }}</p>
                    <ul class="mt-6 space-y-3">
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-purple-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.4.features.0'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-purple-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.4.features.1'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-purple-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.4.features.2'|trans }}</span>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Research Use Case -->
            <div class="bg-white rounded-2xl shadow-xl overflow-hidden transform transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl" data-aos="fade-up" data-aos-delay="800">
                <div class="p-8">
                    <div class="flex items-center">
                        <img src="{{ asset('images/icons/research.svg') }}" alt="Research" class="w-16 h-16">
                        <h3 class="ml-4 text-xl font-medium text-gray-900">{{ 'landing.use_cases.items.5.title'|trans }}</h3>
                    </div>
                    <p class="mt-4 text-base text-gray-500">{{ 'landing.use_cases.items.5.description'|trans }}</p>
                    <ul class="mt-6 space-y-3">
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-cyan-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.5.features.0'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-cyan-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.5.features.1'|trans }}</span>
                        </li>
                        <li class="flex">
                            <svg class="flex-shrink-0 h-5 w-5 text-cyan-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            <span class="ml-2 text-gray-500">{{ 'landing.use_cases.items.5.features.2'|trans }}</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>
