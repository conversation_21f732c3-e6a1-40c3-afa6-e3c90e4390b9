<!DOCTYPE html>
<html lang="{{ app.request.locale }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ 'app.name'|trans }} - {{ 'app.tagline'|trans }}{% endblock %}</title>
    <meta name="description" content="Magnora - Social Media Intelligence Platform for tracking and analyzing Facebook and Instagram profiles with AI-powered insights.">
    <link rel="icon" href="{{ asset('images/favicon.svg') }}">

    {% block stylesheets %}
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Lexend:wght@400;500;600;700&display=swap" rel="stylesheet">
        <script src="{{ asset('landing.js') }}"></script>
        {{ tailwind_entries('landing') }}
    {% endblock %}
</head>
<body class="antialiased bg-white">
    {% include 'landing/partials/_header.html.twig' %}

    <main>
        {% include 'landing/partials/_hero.html.twig' %}
        {% include 'landing/partials/_clients.html.twig' %}
        {% include 'landing/partials/_features.html.twig' %}
        {% include 'landing/partials/_usecases.html.twig' %}
        {% include 'landing/partials/_howitworks.html.twig' %}
        {% include 'landing/partials/_testimonials.html.twig' %}
        {% include 'landing/partials/_pricing.html.twig' %}
        {% include 'landing/partials/_faq.html.twig' %}
        {% include 'landing/partials/_cta.html.twig' %}
    </main>

    {% include 'landing/partials/_footer.html.twig' %}

    {% block javascripts %}
        <script>
            // Initialize Preline
            document.addEventListener('DOMContentLoaded', function () {
                // Initialize Preline UI components
                if (typeof HSStaticMethods !== 'undefined') {
                    HSStaticMethods.autoInit();
                }

                // Initialize AOS animations
                if (typeof AOS !== 'undefined') {
                    AOS.init({
                        duration: 800,
                        easing: 'ease-in-out',
                        once: true,
                        mirror: false
                    });
                }

                // Navbar scroll behavior
                const header = document.querySelector('#header');
                if (header) {
                    window.addEventListener('scroll', () => {
                        if (window.scrollY > 50) {
                            header.classList.add('bg-white', 'shadow-md');
                            header.classList.remove('bg-transparent');
                        } else {
                            header.classList.remove('bg-white', 'shadow-md');
                            header.classList.add('bg-transparent');
                        }
                    });
                }

                // Counter animation
                const counters = document.querySelectorAll('.counter');
                counters.forEach(counter => {
                    const target = parseInt(counter.getAttribute('data-target'));
                    const duration = 2000; // 2 seconds
                    const step = target / (duration / 16); // 60fps
                    let current = 0;

                    const updateCounter = () => {
                        current += step;
                        if (current < target) {
                            counter.textContent = Math.ceil(current).toLocaleString();
                            requestAnimationFrame(updateCounter);
                        } else {
                            counter.textContent = target.toLocaleString();
                        }
                    };

                    const observer = new IntersectionObserver(entries => {
                        if (entries[0].isIntersecting) {
                            updateCounter();
                            observer.disconnect();
                        }
                    }, { threshold: 0.5 });

                    observer.observe(counter);
                });
            });
        </script>
    {% endblock %}
</body>
</html>
