<!-- Testimonials Section -->
<section class="py-5 testimonial-section">
    <div class="container-xl py-5">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold">{{ 'landing.testimonials.title'|trans }}</h2>
            <p class="lead">{{ 'landing.testimonials.subtitle'|trans }}</p>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="testimonial-card">
                    <div class="testimonial-quote">"</div>
                    <p class="fs-5 mb-4 position-relative">{{ 'landing.testimonials.items.0.quote'|trans }}</p>
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-lg me-3 bg-primary-lt">
                            <span class="avatar-letters">{{ 'landing.testimonials.items.0.author'|trans|slice(0, 1) }}</span>
                        </div>
                        <div>
                            <h5 class="mb-0">{{ 'landing.testimonials.items.0.author'|trans }}</h5>
                            <p class="text-secondary mb-0">{{ 'landing.testimonials.items.0.position'|trans }}, {{ 'landing.testimonials.items.0.company'|trans }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="testimonial-card">
                    <div class="testimonial-quote">"</div>
                    <p class="fs-5 mb-4 position-relative">{{ 'landing.testimonials.items.1.quote'|trans }}</p>
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-lg me-3 bg-success-lt">
                            <span class="avatar-letters">{{ 'landing.testimonials.items.1.author'|trans|slice(0, 1) }}</span>
                        </div>
                        <div>
                            <h5 class="mb-0">{{ 'landing.testimonials.items.1.author'|trans }}</h5>
                            <p class="text-secondary mb-0">{{ 'landing.testimonials.items.1.position'|trans }}, {{ 'landing.testimonials.items.1.company'|trans }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="testimonial-card">
                    <div class="testimonial-quote">"</div>
                    <p class="fs-5 mb-4 position-relative">{{ 'landing.testimonials.items.2.quote'|trans }}</p>
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-lg me-3 bg-warning-lt">
                            <span class="avatar-letters">{{ 'landing.testimonials.items.2.author'|trans|slice(0, 1) }}</span>
                        </div>
                        <div>
                            <h5 class="mb-0">{{ 'landing.testimonials.items.2.author'|trans }}</h5>
                            <p class="text-secondary mb-0">{{ 'landing.testimonials.items.2.position'|trans }}, {{ 'landing.testimonials.items.2.company'|trans }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
