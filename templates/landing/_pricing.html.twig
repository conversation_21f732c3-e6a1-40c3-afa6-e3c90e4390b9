<!-- Pricing Section -->
<section class="py-5 pricing-section" id="pricing">
    <div class="container-xl py-5">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold">{{ 'landing.pricing.title'|trans }}</h2>
            <p class="lead">{{ 'landing.pricing.subtitle'|trans }}</p>
            <div class="btn-group mt-3">
                <button type="button" class="btn btn-outline-primary active">{{ 'landing.pricing.monthly'|trans }}</button>
                <button type="button" class="btn btn-outline-primary">{{ 'landing.pricing.yearly'|trans }}</button>
            </div>
        </div>
        <div class="row g-4">
            <!-- Starter Plan -->
            <div class="col-md-4">
                <div class="card pricing-card h-100">
                    <div class="card-body text-center p-4">
                        <h3 class="card-title mb-3">{{ 'landing.pricing.plans.0.name'|trans }}</h3>
                        <div class="my-4">
                            <span class="display-5 fw-bold">{{ 'landing.pricing.plans.0.price'|trans }}</span>
                            <span class="text-secondary">{{ 'landing.pricing.plans.0.period'|trans }}</span>
                        </div>
                        <p class="text-secondary mb-4">{{ 'landing.pricing.plans.0.description'|trans }}</p>
                        <div class="mb-4">
                            {% for feature in ['landing.pricing.plans.0.features.0', 'landing.pricing.plans.0.features.1', 'landing.pricing.plans.0.features.2', 'landing.pricing.plans.0.features.3'] %}
                                <div class="pricing-feature">
                                    <i class="ti ti-check text-success"></i>
                                    {{ feature|trans }}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent p-4 pt-0">
                        <a href="{{ path('app_register') }}" class="btn btn-outline-primary w-100">
                            {{ 'landing.pricing.plans.0.cta'|trans }}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Professional Plan -->
            <div class="col-md-4">
                <div class="card pricing-card h-100 popular">
                    <div class="ribbon ribbon-top ribbon-bookmark bg-green">
                        <span>{{ 'landing.pricing.plans.1.popular'|trans }}</span>
                    </div>
                    <div class="card-body text-center p-4">
                        <h3 class="card-title mb-3">{{ 'landing.pricing.plans.1.name'|trans }}</h3>
                        <div class="my-4">
                            <span class="display-5 fw-bold">{{ 'landing.pricing.plans.1.price'|trans }}</span>
                            <span class="text-secondary">{{ 'landing.pricing.plans.1.period'|trans }}</span>
                        </div>
                        <p class="text-secondary mb-4">{{ 'landing.pricing.plans.1.description'|trans }}</p>
                        <div class="mb-4">
                            {% for feature in ['landing.pricing.plans.1.features.0', 'landing.pricing.plans.1.features.1', 'landing.pricing.plans.1.features.2', 'landing.pricing.plans.1.features.3', 'landing.pricing.plans.1.features.4'] %}
                                <div class="pricing-feature">
                                    <i class="ti ti-check text-success"></i>
                                    {{ feature|trans }}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent p-4 pt-0">
                        <a href="{{ path('app_register') }}" class="btn btn-primary w-100">
                            {{ 'landing.pricing.plans.1.cta'|trans }}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Enterprise Plan -->
            <div class="col-md-4">
                <div class="card pricing-card h-100">
                    <div class="card-body text-center p-4">
                        <h3 class="card-title mb-3">{{ 'landing.pricing.plans.2.name'|trans }}</h3>
                        <div class="my-4">
                            <span class="display-5 fw-bold">{{ 'landing.pricing.plans.2.price'|trans }}</span>
                            <span class="text-secondary">{{ 'landing.pricing.plans.2.period'|trans }}</span>
                        </div>
                        <p class="text-secondary mb-4">{{ 'landing.pricing.plans.2.description'|trans }}</p>
                        <div class="mb-4">
                            {% for feature in ['landing.pricing.plans.2.features.0', 'landing.pricing.plans.2.features.1', 'landing.pricing.plans.2.features.2', 'landing.pricing.plans.2.features.3', 'landing.pricing.plans.2.features.4', 'landing.pricing.plans.2.features.5'] %}
                                <div class="pricing-feature">
                                    <i class="ti ti-check text-success"></i>
                                    {{ feature|trans }}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent p-4 pt-0">
                        <a href="#" class="btn btn-outline-primary w-100">
                            {{ 'landing.pricing.plans.2.cta'|trans }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
