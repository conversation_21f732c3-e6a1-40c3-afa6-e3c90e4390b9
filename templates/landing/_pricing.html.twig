<!-- Pricing Section -->
<section class="py-5 pricing-section" id="pricing">
    <div class="container-xl py-5">
        <div class="text-center mb-5">
            <h2 class="display-5 fw-bold">{{ 'pricing.title'|trans(domain: 'landing') }}</h2>
            <p class="lead">{{ 'pricing.subtitle'|trans(domain: 'landing') }}</p>
            <div class="btn-group mt-3">
                <button type="button" class="btn btn-outline-primary active">{{ 'pricing.monthly'|trans(domain: 'landing') }}</button>
                <button type="button" class="btn btn-outline-primary">{{ 'pricing.yearly'|trans(domain: 'landing') }}</button>
            </div>
        </div>
        <div class="row g-4">
            <!-- Starter Plan -->
            <div class="col-md-4">
                <div class="card pricing-card h-100">
                    <div class="card-body text-center p-4">
                        <h3 class="card-title mb-3">{{ 'pricing.plans.0.name'|trans(domain: 'landing') }}</h3>
                        <div class="my-4">
                            <span class="display-5 fw-bold">{{ 'pricing.plans.0.price'|trans(domain: 'landing') }}</span>
                            <span class="text-secondary">{{ 'pricing.plans.0.period'|trans(domain: 'landing') }}</span>
                        </div>
                        <p class="text-secondary mb-4">{{ 'pricing.plans.0.description'|trans(domain: 'landing') }}</p>
                        <div class="mb-4">
                            {% for feature in ['pricing.plans.0.features.0', 'pricing.plans.0.features.1', 'pricing.plans.0.features.2', 'pricing.plans.0.features.3'] %}
                                <div class="pricing-feature">
                                    <i class="ti ti-check text-success"></i>
                                    {{ feature|trans(domain: 'landing') }}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent p-4 pt-0">
                        <a href="{{ path('app_register') }}" class="btn btn-outline-primary w-100">
                            {{ 'pricing.plans.0.cta'|trans(domain: 'landing') }}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Professional Plan -->
            <div class="col-md-4">
                <div class="card pricing-card h-100 popular">
                    <div class="ribbon ribbon-top ribbon-bookmark bg-green">
                        <span>{{ 'pricing.plans.1.popular'|trans(domain: 'landing') }}</span>
                    </div>
                    <div class="card-body text-center p-4">
                        <h3 class="card-title mb-3">{{ 'pricing.plans.1.name'|trans(domain: 'landing') }}</h3>
                        <div class="my-4">
                            <span class="display-5 fw-bold">{{ 'pricing.plans.1.price'|trans(domain: 'landing') }}</span>
                            <span class="text-secondary">{{ 'pricing.plans.1.period'|trans(domain: 'landing') }}</span>
                        </div>
                        <p class="text-secondary mb-4">{{ 'pricing.plans.1.description'|trans(domain: 'landing') }}</p>
                        <div class="mb-4">
                            {% for feature in ['pricing.plans.1.features.0', 'pricing.plans.1.features.1', 'pricing.plans.1.features.2', 'pricing.plans.1.features.3', 'pricing.plans.1.features.4'] %}
                                <div class="pricing-feature">
                                    <i class="ti ti-check text-success"></i>
                                    {{ feature|trans(domain: 'landing') }}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent p-4 pt-0">
                        <a href="{{ path('app_register') }}" class="btn btn-primary w-100">
                            {{ 'pricing.plans.1.cta'|trans(domain: 'landing') }}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Enterprise Plan -->
            <div class="col-md-4">
                <div class="card pricing-card h-100">
                    <div class="card-body text-center p-4">
                        <h3 class="card-title mb-3">{{ 'pricing.plans.2.name'|trans(domain: 'landing') }}</h3>
                        <div class="my-4">
                            <span class="display-5 fw-bold">{{ 'pricing.plans.2.price'|trans(domain: 'landing') }}</span>
                            <span class="text-secondary">{{ 'pricing.plans.2.period'|trans(domain: 'landing') }}</span>
                        </div>
                        <p class="text-secondary mb-4">{{ 'pricing.plans.2.description'|trans(domain: 'landing') }}</p>
                        <div class="mb-4">
                            {% for feature in ['pricing.plans.2.features.0', 'pricing.plans.2.features.1', 'pricing.plans.2.features.2', 'pricing.plans.2.features.3', 'pricing.plans.2.features.4', 'pricing.plans.2.features.5'] %}
                                <div class="pricing-feature">
                                    <i class="ti ti-check text-success"></i>
                                    {{ feature|trans(domain: 'landing') }}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent p-4 pt-0">
                        <a href="#" class="btn btn-outline-primary w-100">
                            {{ 'pricing.plans.2.cta'|trans(domain: 'landing') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
