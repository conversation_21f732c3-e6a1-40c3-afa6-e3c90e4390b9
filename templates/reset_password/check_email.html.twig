{% extends '@Tabler/security/password-reset.html.twig' %}

{% block title %}{{ 'auth.forgot_password.check_email'|trans }}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .sf-toolbar {
            display: none !important;
        }

        .card-body {
            padding: 2rem;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        }
    </style>
{% endblock %}

{% block logo_login %}{{ 'app.name'|trans }}{% endblock %}

{% block password_reset_title %}{{ 'auth.forgot_password.check_email'|trans }}{% endblock %}
{% block password_reset_subtitle %}{% endblock %}

{% block password_reset_form %}
    <div class="text-center mb-4">
        <div class="mb-3">
            <span class="avatar avatar-xl rounded-circle bg-success-lt">
                <i class="ti ti-mail fs-1"></i>
            </span>
        </div>
        <p class="text-secondary mb-4">
            {{ 'auth.forgot_password.check_email'|trans }}
            <br>
            {{ 'This link will expire in'|trans }} {{ resetToken.expirationMessageKey|trans(resetToken.expirationMessageData, 'ResetPasswordBundle') }}.
        </p>
        <p class="text-secondary">
            {{ 'If you don\'t receive an email please check your spam folder or'|trans }} <a href="{{ path('app_forgot_password_request') }}">{{ 'try again'|trans }}</a>.
        </p>
    </div>
{% endblock %}

{% block password_reset_back %}
    <div class="text-center text-secondary mt-3">
        <a href="{{ path('app_login') }}">{{ 'auth.forgot_password.back_to_login'|trans }}</a>
    </div>
{% endblock %}