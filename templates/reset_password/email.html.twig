<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{ 'auth.reset_password.title'|trans }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #206bc4;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #206bc4;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">{{ 'app.name'|trans }}</div>
    </div>

    <h1>{{ 'Hello!'|trans }}</h1>

    <p>{{ 'You requested a password reset for your account. Please click the button below to set a new password:'|trans }}</p>

    <div style="text-align: center;">
        <a class="button" href="{{ url('app_reset_password', {token: resetToken.token}) }}">{{ 'Reset Password'|trans }}</a>
    </div>

    <p>{{ 'If the button doesn\'t work, copy and paste this URL into your browser:'|trans }}</p>

    <p><a href="{{ url('app_reset_password', {token: resetToken.token}) }}">{{ url('app_reset_password', {token: resetToken.token}) }}</a></p>

    <p>{{ 'This link will expire in'|trans }} {{ resetToken.expirationMessageKey|trans(resetToken.expirationMessageData, 'ResetPasswordBundle') }}.</p>

    <p>{{ 'If you did not request a password reset, please ignore this email.'|trans }}</p>

    <div class="footer">
        <p>&copy; {{ 'now'|date('Y') }} {{ 'app.name'|trans }}. {{ 'All rights reserved.'|trans }}</p>
    </div>
</body>
</html>