<div id="social-tracking-group-cards-controller" {{ attributes }}>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="btn-group" role="group" aria-label="Filter tracking groups">
            <input type="radio" class="btn-check" name="filter-options" id="filter-all" autocomplete="off"
                {{ this.filter == 'all' ? 'checked' : '' }}
                data-action="live#action" data-action-name="changeFilter" data-filter-param="all">
            <label class="btn btn-outline-primary" for="filter-all">All</label>

            <input type="radio" class="btn-check" name="filter-options" id="filter-active" autocomplete="off"
                {{ this.filter == 'active' ? 'checked' : '' }}
                data-action="live#action" data-action-name="changeFilter" data-filter-param="active">
            <label class="btn btn-outline-primary" for="filter-active">Active</label>

            <input type="radio" class="btn-check" name="filter-options" id="filter-completed" autocomplete="off"
                {{ this.filter == 'completed' ? 'checked' : '' }}
                data-action="live#action" data-action-name="changeFilter" data-filter-param="completed">
            <label class="btn btn-outline-primary" for="filter-completed">Completed</label>

            <input type="radio" class="btn-check" name="filter-options" id="filter-paused" autocomplete="off"
                {{ this.filter == 'paused' ? 'checked' : '' }}
                data-action="live#action" data-action-name="changeFilter" data-filter-param="paused">
            <label class="btn btn-outline-primary" for="filter-paused">Paused</label>
        </div>

        <div class="d-flex gap-2">
            <div class="input-icon">
                <input type="text" class="form-control" placeholder="Search…" aria-label="Search in website">
                <span class="input-icon-addon">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                        <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                        <path d="M21 21l-6 -6"></path>
                    </svg>
                </span>
            </div>
            <button type="button" class="btn btn-primary d-none d-sm-inline-block" data-bs-toggle="modal" data-bs-target="#add-tracking-group">
                <i class="fa fa-plus icon icon-2"></i> Add new tracking group
            </button>
        </div>
    </div>

    {% if this.list|length > 0 %}
        <div class="row row-cards">
            {% for item in this.list %}
                <div class="col-md-6 col-lg-4">
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <div class="avatar avatar-sm bg-primary-lt me-2">{{ item.name|first|upper }}</div>
                                <h3 class="card-title">{{ item.name }}</h3>
                            </div>
                            <div class="card-actions">
                                <div class="dropdown">
                                    <a href="#" class="btn-action dropdown-toggle" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                            <path d="M12 12m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                            <path d="M12 19m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                            <path d="M12 5m-1 0a1 1 0 1 0 2 0a1 1 0 1 0 -2 0"></path>
                                        </svg>
                                    </a>
                                    <div class="dropdown-menu dropdown-menu-end">
                                        <a class="dropdown-item" href="{{ path('app_social_tracking_group_view', { id: item.id }) }}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-eye me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"></path>
                                                <path d="M21 12c-2.4 4 -5.4 6 -9 6c-3.6 0 -6.6 -2 -9 -6c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6"></path>
                                            </svg>
                                            View Details
                                        </a>
                                        <a class="dropdown-item" href="{{ path('app_social_tracking_group_edit', { id: item.id }) }}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-tabler icon-tabler-edit me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                                <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"></path>
                                                <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"></path>
                                                <path d="M16 5l3 3"></path>
                                            </svg>
                                            Edit
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-2">
                                <span class="badge bg-{{ item.dataSource == 'meta:instagram' ? 'purple' : 'blue' }}-lt me-1">
                                    {{ item.dataSourceForDisplay }}
                                </span>
                                <span class="status {{ item.statusClass }}">
                                    <span class="status-dot {% if item.statusFinished != true %}status-dot-animated{% endif %}"></span>
                                    {{ item.statusLabel }}
                                </span>
                            </div>

                            {% set percentage = item.status.totalProfiles > 0 ? ((item.status.progress / item.status.totalProfiles) * 100)|round : 0 %}

                            {% if item.statusFinished != true %}
                                <div class="d-flex align-items-center mb-1">
                                    <div>Progress</div>
                                    <div class="ms-auto">
                                        <span class="text-muted">{{ item.status.progress }}/{{ item.status.totalProfiles }}</span>
                                    </div>
                                </div>
                                <div class="progress mb-3">
                                    <div class="progress-bar {{ item.progressBarClass }} progress-bar-striped progress-bar-animated" style="width: {{ percentage }}%" role="progressbar" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100" aria-label="{{ percentage }}% Complete">
                                        <span class="visually-hidden">{{ percentage }}% Complete</span>
                                    </div>
                                </div>
                            {% else %}
                                <div class="d-flex align-items-center mb-3">
                                    <div>Completed</div>
                                    <div class="ms-auto">
                                        <span class="text-success">{{ item.status.progress }}/{{ item.status.totalProfiles }}</span>
                                    </div>
                                </div>
                            {% endif %}

                            <div class="d-flex align-items-center">
                                <div class="text-muted">Created: {{ item.createdAt|date('M d, Y') }}</div>
                                <div class="ms-auto d-flex gap-2">
                                    {% if item.status.status.value == 'finished' %}
                                        <a href="{{ path('app_social_tracking_group_queue', { id: item.id }) }}" class="btn btn-primary btn-sm" title="Restart">
                                            <i class="fa fa-arrows-rotate"></i>
                                        </a>
                                    {% elseif item.status.status.value == 'waiting_next_update' %}
                                        <a href="{{ path('app_social_tracking_group_force_update', { id: item.id }) }}" class="btn btn-primary btn-sm" title="Update Now">
                                            <i class="fa fa-arrows-rotate"></i>
                                        </a>
                                    {% elseif item.status.status.value == 'processing' or item.status.status.value == 'queued' or item.status.status.value == 'update_requested' %}
                                        <a href="{{ path('app_social_tracking_group_pause', { id: item.id }) }}" class="btn btn-warning btn-sm" title="Pause">
                                            <i class="fa fa-pause"></i>
                                        </a>
                                    {% elseif item.status.status.value == 'paused' %}
                                        <a href="{{ path('app_social_tracking_group_queue', { id: item.id }) }}" class="btn btn-success btn-sm" title="Resume">
                                            <i class="fa fa-play"></i>
                                        </a>
                                    {% endif %}

                                    <a href="{{ path('app_social_tracking_group_view', { id: item.id }) }}" class="btn btn-primary btn-sm">
                                        Manage
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="empty">
            <div class="empty-img">
                <img src="{{ asset('images/cat.webp') }}" height="128" alt="Empty state">
            </div>
            <p class="empty-title">No Tracking Groups Found</p>
            <p class="empty-subtitle text-secondary">
                {% if this.filter != 'all' %}
                    No tracking groups match the selected filter.
                {% else %}
                    Start by creating your first tracking group.
                {% endif %}
            </p>
            <div class="empty-action">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#add-tracking-group">
                    <i class="fa fa-plus icon icon-2 me-1"></i> Add new tracking group
                </button>
            </div>
        </div>
    {% endif %}
</div>
