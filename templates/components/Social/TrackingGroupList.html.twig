<div id="social-tracking-group-status-controller" {{ attributes }}>
    {% if this.list|length > 0 %}
        <div class="table-responsive">
        <table class="table table-selectable card-table table-vcenter text-nowrap datatable">
            <thead>
            <tr>
                {#<th class="w-1"><input class="form-check-input m-0 align-middle" type="checkbox" aria-label="Select all invoices"></th>#}
                <th class="w-1">
                    Id
                </th>
                <th>Name</th>
                <th>Data Source</th>
                <th>Status</th>
                <th>Created</th>
                <th>Updated</th>
                <th></th>
            </tr>
            </thead>
            <tbody>
            {% for item in this.list %}
                <tr>
                    {#<td><input class="form-check-input m-0 align-middle table-selectable-check" type="checkbox" aria-label="Select invoice"></td>#}
                    <td><span class="text-secondary">{{ item.id }}</span></td>
                    <td><a href="{{ path('app_social_tracking_group_edit', { id: item.id }) }}" class="text-reset" tabindex="-1">{{ item.name }}</a></td>
                    <td>{{ item.dataSourceForDisplay }}</td>
                    <td>
                        {% set percentage = item.status.totalProfiles > 0 ? ((item.status.progress / item.status.totalProfiles) * 100)|round : 0 %}
                        <div class="mb-1">
                            <span class="status {{ item.statusClass }}">
                              <span class="status-dot {% if item.statusFinished != true %}status-dot-animated{% endif %}"></span>
                              {{ item.statusLabel }} ({{ item.status.progress }} / {{ item.status.totalProfiles }})
                            </span>
                        </div>
                        {% if item.statusFinished != true %}
                            <div class="progress">
                                <div class="progress-bar {{ item.progressBarClass }} progress-bar-striped progress-bar-animated" style="width: {{ percentage }}%"></div>
                            </div>
                        {% endif %}
                    </td>
                    <td>{{ item.createdAt|date('Y-m-d H:i') }}</td>
                    <td>{{ item.updatedAt|date('Y-m-d H:i') }}</td>
                    <td class="d-flex text-end">
                        {% set btnClass = '' %}
                        {% set btnIcon = '' %}
                        {% set btnLink = '#' %}

                        {% if item.status.status.value == 'finished' %}
                            {% set btnClass = 'btn-primary' %}
                            {% set btnIcon = 'fa-arrows-rotate' %}
                            {% set btnLink = path('app_social_tracking_group_queue', { id: item.id }) %}
                        {% elseif item.status.status.value == 'waiting_next_update' %}
                            {% set btnClass = 'btn-primary' %}
                            {% set btnIcon = 'fa-arrows-rotate' %}
                            {% set btnLink = path('app_social_tracking_group_force_update', { id: item.id }) %}
                        {% elseif item.status.status.value == 'processing' or item.status.status.value == 'queued' or item.status.status.value == 'update_requested' %}
                            {% set btnClass = 'btn-warning' %}
                            {% set btnIcon = 'fa-pause' %}
                            {% set btnLink = path('app_social_tracking_group_pause', { id: item.id }) %}
                        {% elseif item.status.status.value == 'paused' %}
                            {% set btnClass = 'btn-success' %}
                            {% set btnIcon = 'fa-play' %}
                            {% set btnLink = path('app_social_tracking_group_queue', { id: item.id }) %}
                        {% endif %}

                        {% if item.status.status.value != 'pause_requested' %}
                            <a href="{{ btnLink }}" class="btn {{ btnClass }} align-text-top me-1" style="width: 40px;">
                                <i class="fa {{ btnIcon }}"></i>
                            </a>
                        {% endif %}

                        <div class="btn-list flex-nowrap">
                            <div class="dropdown">
                                <button class="btn dropdown-toggle align-text-top" data-bs-toggle="dropdown" aria-expanded="false">
                                    Actions
                                </button>
                                <div class="dropdown-menu">
                                    <a class="dropdown-item" href="{{ path('app_social_tracking_group_view', { id: item.id }) }}"><i class="fa fa-gear me-2"></i> Manage</a>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
        <div class="page page-center">
            <div class="container-tight py-4">
                <div class="empty">
                    <div class="empty-img">
                        <img class="img" src="{{ asset('images/cat.webp') }}" alt="Empty">
                    </div>
                    <p class="empty-title">No Tracking Groups Yet</p>
                    <p class="empty-subtitle text-secondary">The Tracking Groups you add will show up here.</p>
                </div>
            </div>
        </div>
    {% endif %}
</div>