{% set status = status|default('unknown') %}

{% set statusColors = {
    'unknown': {
        'border': 'border-gray-500',
        'bg': 'bg-gray-50 dark:bg-gray-900/20',
        'text': 'text-gray-700 dark:text-gray-300',
        'badge': 'bg-gray-100 dark:bg-gray-900',
        'icon': 'bg-gray-500',
        'progress': 'bg-gray-600 dark:bg-gray-500',
        'animate': '',
        'glow': '',
        'card': 'ring-1 ring-gray-500/50'
    },
    'running': {
        'border': 'border-blue-500',
        'bg': 'bg-blue-50 dark:bg-blue-900/20',
        'text': 'text-blue-700 dark:text-blue-300',
        'badge': 'bg-blue-100 dark:bg-blue-900',
        'icon': 'bg-blue-500',
        'progress': 'bg-blue-600 dark:bg-blue-500',
        'animate': 'animate-pulse',
        'glow': 'shadow-[0_0_15px_rgba(59,130,246,0.5)]',
        'card': 'ring-1 ring-blue-500/50'
    },
    'waiting_next_update': {
        'border': 'border-green-500',
        'bg': 'bg-green-50 dark:bg-green-900/20',
        'text': 'text-green-700 dark:text-green-300',
        'badge': 'bg-green-100 dark:bg-green-900',
        'icon': 'bg-green-500',
        'progress': 'bg-green-600 dark:bg-green-500',
        'animate': '',
        'glow': '',
        'card': 'ring-1 ring-green-500/50'
    },
    'finished': {
        'border': 'border-green-500',
        'bg': 'bg-green-50 dark:bg-green-900/20',
        'text': 'text-green-700 dark:text-green-300',
        'badge': 'bg-green-100 dark:bg-green-900',
        'icon': 'bg-green-500',
        'progress': 'bg-green-600 dark:bg-green-500',
        'animate': '',
        'glow': '',
        'card': 'ring-1 ring-green-500/50'
    },
    'paused': {
        'border': 'border-yellow-500',
        'bg': 'bg-yellow-50 dark:bg-yellow-900/20',
        'text': 'text-yellow-700 dark:text-yellow-300',
        'badge': 'bg-yellow-100 dark:bg-yellow-900',
        'icon': 'bg-yellow-500',
        'progress': 'bg-yellow-600 dark:bg-yellow-500',
        'animate': '',
        'glow': '',
        'card': 'ring-1 ring-yellow-500/50'
    },
    'pause_requested': {
        'border': 'border-yellow-500',
        'bg': 'bg-yellow-50 dark:bg-yellow-900/20',
        'text': 'text-yellow-700 dark:text-yellow-300',
        'badge': 'bg-yellow-100 dark:bg-yellow-900',
        'icon': 'bg-yellow-500',
        'progress': 'bg-yellow-600 dark:bg-yellow-500',
        'animate': 'animate-pulse',
        'glow': 'shadow-[0_0_15px_rgba(234,179,8,0.5)]',
        'card': 'ring-1 ring-yellow-500/50'
    },
    'queued': {
        'border': 'border-purple-500',
        'bg': 'bg-purple-50 dark:bg-purple-900/20',
        'text': 'text-purple-700 dark:text-purple-300',
        'badge': 'bg-purple-100 dark:bg-purple-900',
        'icon': 'bg-purple-500',
        'progress': 'bg-purple-600 dark:bg-purple-500',
        'animate': '',
        'glow': '',
        'card': 'ring-1 ring-purple-500/50'
    },
    'error': {
        'border': 'border-red-500',
        'bg': 'bg-red-50 dark:bg-red-900/20',
        'text': 'text-red-700 dark:text-red-300',
        'badge': 'bg-red-100 dark:bg-red-900',
        'icon': 'bg-red-500',
        'progress': 'bg-red-600 dark:bg-red-500',
        'animate': '',
        'glow': '',
        'card': 'ring-1 ring-red-500/50'
    }
} %}

{% set statusIcons = {
    'running': 'fa-play-circle',
    'waiting_next_update': 'fa-clock',
    'finished': 'fa-check-circle',
    'paused': 'fa-pause-circle',
    'pause_requested': 'fa-hand-paper',
    'queued': 'fa-hourglass-half',
    'error': 'fa-exclamation-triangle'
} %}

{% set statusDescriptions = {
    'running': 'tracking_group.status.running'|trans,
    'waiting_next_update': 'tracking_group.status.waiting_next_update'|trans,
    'finished': 'tracking_group.status.finished'|trans,
    'paused': 'tracking_group.status.paused'|trans,
    'pause_requested': 'tracking_group.status.pause_requested'|trans,
    'queued': 'tracking_group.status.queued'|trans,
    'error': 'tracking_group.status.error'|trans
} %}

{% set colors = statusColors[status] ?? statusColors['unknown'] %}
{% set statusIcon = statusIcons[status] ?? 'fa-question' %}
{% set statusDescription = statusDescriptions[status] ?? 'tracking_group.status.unknown'|trans %}

<div class="flex items-center">
    <div class="flex-shrink-0 w-2 h-2 rounded-full {{ colors.icon }} mr-2"></div>
    <span class="px-2.5 py-1 rounded-full text-xs font-medium {{ colors.badge }} {{ colors.text }}">
        {{ status|replace({'_': ' '})|title }}
    </span>
</div>
<span class="text-xs text-gray-500 dark:text-gray-400 mt-1 italic">
    {{ statusDescription }}
</span>
