<div {{ attributes }}>
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Recent Activity</h3>
        </div>
        <div class="card-body">
            {% if this.recentActivity|length > 0 %}
                <div class="divide-y">
                    {% for activity in this.recentActivity %}
                        <div class="row py-3 align-items-center">
                            <div class="col-auto">
                                <span class="avatar bg-primary-lt">{{ activity.trackingGroup.name|first|upper }}</span>
                            </div>
                            <div class="col">
                                <div class="text-truncate">
                                    <strong>{{ activity.trackingGroup.name }}</strong> tracking progress updated
                                </div>
                                <div class="text-secondary">
                                    {{ activity.progress }} / {{ activity.totalProfiles }} profiles processed
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="text-secondary">
                                    {{ activity.createdAt|date('M d, H:i') }}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty">
                    <div class="empty-img">
                        <img src="{{ asset('images/cat.webp') }}" height="128" alt="Empty state">
                    </div>
                    <p class="empty-title">No Recent Activity</p>
                    <p class="empty-subtitle text-secondary">
                        Activity will appear here as tracking groups are processed.
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
