<div {{ attributes }}>
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">Tracking Activity</h3>
        </div>
        <div class="card-body">
            <div id="activity-chart" style="height: 300px;"></div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const chartData = {{ this.chartData|json_encode|raw }};

            if (!chartData.labels || chartData.labels.length === 0) {
                document.getElementById('activity-chart').innerHTML = '<div class="text-center py-5"><p class="text-muted">No activity data available</p></div>';
                return;
            }

            // Check if ApexCharts is available
            if (typeof window.ApexCharts === 'undefined') {
                console.error('ApexCharts is not loaded');
                document.getElementById('activity-chart').innerHTML = '<div class="text-center py-5"><p class="text-muted">Chart library not available</p></div>';
                return;
            }

            const chart = new window.ApexCharts(document.getElementById('activity-chart'), {
                chart: {
                    type: 'area',
                    fontFamily: 'inherit',
                    height: 300,
                    parentHeightOffset: 0,
                    toolbar: {
                        show: false,
                    },
                    animations: {
                        enabled: true
                    },
                    stacked: false,
                },
                dataLabels: {
                    enabled: false,
                },
                fill: {
                    opacity: 0.16,
                    type: 'solid'
                },
                stroke: {
                    width: 2,
                    lineCap: 'round',
                    curve: 'smooth',
                },
                series: [{
                    name: 'Total Groups',
                    data: chartData.total
                }, {
                    name: 'Active',
                    data: chartData.active
                }, {
                    name: 'Completed',
                    data: chartData.completed
                }],
                grid: {
                    padding: {
                        top: -20,
                        right: 0,
                        left: -4,
                        bottom: -4
                    },
                    strokeDashArray: 4,
                },
                xaxis: {
                    labels: {
                        padding: 0,
                    },
                    tooltip: {
                        enabled: false
                    },
                    categories: chartData.labels,
                },
                yaxis: {
                    labels: {
                        padding: 4
                    },
                },
                colors: ['#206bc4', '#f59f00', '#2fb344'],
                legend: {
                    show: true,
                    position: 'bottom',
                    offsetY: 12,
                },
                tooltip: {
                    theme: 'dark'
                }
            });

            chart.render();
        });
    </script>
</div>
