<header>
    <div id="sticky-header" class="tg-header__area transparent-header">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="mobile-nav-toggler"><i class="flaticon-menu-1"></i></div>
                    <div class="tgmenu__wrap">
                        <nav class="tgmenu__nav">
                            <div class="logo">
                                <a href="{{ path('app_main_index') }}" class="light-logo">
                                    <img src="{{ asset('img/logo/postchat-logo-512-min.png') }}" width="120px" alt="PostChat">
                                </a>
                                <a href="{{ path('app_main_index') }}" class="dark-logo">
                                    <img src="{{ asset('img/logo/postchat-logo-512-min.png') }}" width="120px" alt="PostChat">
                                </a>
                            </div>
                            <div class="tgmenu__navbar-wrap tgmenu__main-menu d-none d-lg-flex">
                                <ul class="navigation">
                                    <li class="active menu-item-has-children"><a href="#home" class="section-link">{{ 'header.menu.home'|trans }}</a></li>
                                    <li><a href="#about" class="section-link">{{ 'header.menu.about'|trans }}</a></li>
                                    <li><a href="#features" class="section-link">{{ 'header.menu.features'|trans }}</a></li>
                                    <li><a href="#faq" class="section-link">{{ 'header.menu.faq'|trans }}</a></li>
                                    <li>
                                        <select id="switch-language" class="form-control form-select">
                                            <option value="en" {{ app.request.locale == 'en' ? 'selected' : '' }}>English</option>
                                            <option value="tr" {{ app.request.locale == 'tr' ? 'selected' : '' }}>Türkçe</option>
                                        </select>
                                    </li>
                                </ul>
                            </div>
                            <script>
                                document.addEventListener('DOMContentLoaded', function () {
                                    document.getElementById('switch-language').addEventListener('change', function () {
                                        const selectedLang = this.value;
                                        const url = new URL(window.location.href);
                                        url.searchParams.set('lang', selectedLang);
                                        window.location.href = url.toString();
                                    });
                                });
                            </script>
                            <div class="tgmenu__action">
                                <ul class="list-wrap">
                                    <li class="header-social"></li>
                                    <li class="header-social d-none d-md-block me-2">
                                        <a href="{{ path('app_download') }}" class="btn btn-dark btn-sm position-relative text-white">
                                            <i class="fab fa-android me-1"></i> {{ 'home.app.button'|trans }}
                                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning text-dark" style="font-size: 0.6rem;">{{ 'home.app.prototype_warning'|trans }}</span>
                                        </a>
                                    </li>
                                    <li class="header-btn"><button class="btn gradient-btn" data-bs-toggle="modal" data-bs-target="#connectModal">{{ 'header.menu.button'|trans }}</button></li>
                                </ul>
                            </div>
                        </nav>
                    </div>

                    <!-- Mobile Menu  -->
                    <div class="tgmobile__menu">
                        <nav class="tgmobile__menu-box">
                            <div class="close-btn"><i class="flaticon-close-1"></i></div>
                            <div class="nav-logo">
                                <a href="{{ path('app_main_index') }}" class="light-logo">
                                    <img src="{{ asset('img/logo/postchat-logo-512-min.png') }}" alt="Terra Hotels">
                                </a>
                                <a href="{{ path('app_main_index') }}" class="dark-logo">
                                    <img src="{{ asset('img/logo/postchat-logo-512-min.png') }}" alt="Terra Hotels">
                                </a>
                            </div>
                            <div class="tgmobile__menu-outer">
                                <!--Here Menu Will Come Automatically Via JavaScript / Same Menu as in Header-->
                            </div>
                            <div class="social-links">
                                <ul class="list-wrap">
                                    <li>
                                        <a href="{{ path('app_download') }}" class="text-white position-relative">
                                            <i class="fab fa-android"></i> {{ 'home.app.button'|trans }}
                                            <span class="badge bg-warning text-dark ms-2" style="font-size: 0.6rem;">{{ 'home.app.prototype_warning'|trans }}</span>
                                        </a>
                                    </li>
                                    <li><a href="https://my.terra.marketing/webchat/?p=1788388" target="_blank"><i class="fa fa-globe"></i> WebChat</a></li>
                                    {#<li><a href="#" target="_blank"><i class="fab fa-whatsapp"></i></a></li>#}
                                </ul>
                            </div>
                        </nav>
                    </div>
                    <div class="tgmobile__menu-backdrop"></div>
                    <!-- End Mobile Menu -->
                </div>
            </div>
        </div>
    </div>

    <!-- Connect Wallet Modal -->
    <div class="connect__modal">
        <div class="modal fade" id="connectModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal__wrapper">
                        <div class="modal__header">
                            <h2 class="title">{{ 'header.modal.title'|trans }}</h2>
                            <button data-bs-dismiss="modal" aria-label="Close">
                                <i class="flaticon-close-1"></i>
                            </button>
                        </div>
                        <div class="modal__body text-center">
                            <p>{{ 'header.modal.text'|trans }}</p>
                            <div class="connect__section">
                                <ul class="list-wrap">
                                    <li><a href="https://my.terra.marketing/webchat/?p=1788388" target="_blank"><i class="fa fa-globe"></i> WebChat</a></li>
                                </ul>
                            </div>
                            <p class="privacy-text">{{ 'header.modal.terms'|trans({'privacy_url': 'https://privacy-policy.terra.marketing'})|raw }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Connect Wallet Modal -->
</header>