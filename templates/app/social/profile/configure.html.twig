{% extends 'app/dashboard/page.html.twig' %}

{% block title %}{{ 'profiles.configure'|trans }} - {{ profile.data.username ?? profile.data.name ?? profile.metaId }}{% endblock %}

{% block page_actions %}
    <div class="btn-list">
        <a href="{{ path('app_social_profile_view', {id: profile.id}) }}" class="btn btn-outline-primary">
            <i class="fa fa-arrow-left me-2"></i> {{ 'common.back'|trans }}
        </a>
    </div>
{% endblock %}

{% block page_content %}
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ 'profiles.configure_profile'|trans }}</h3>
                </div>
                <div class="card-body">
                    <!-- Profile Summary -->
                    <div class="row mb-4">
                        <div class="col-auto">
                            {% set profile_picture = profile.data.profile_picture_url ?? profile.data.picture.data.url ?? asset('static/avatars/000m.jpg') %}
                            <span class="avatar avatar-lg rounded" style="background-image: url({{ profile_picture }})"></span>
                        </div>
                        <div class="col">
                            <h4 class="mb-1">{{ profile.data.username ?? profile.data.name ?? profile.metaId }}</h4>
                            <div class="text-secondary">{{ profile.platform|capitalize }} • {{ profile.metaId }}</div>
                        </div>
                    </div>

                    <!-- Configuration Form -->
                    {{ form_start(form) }}
                    
                    <div class="mb-3">
                        <h5>{{ 'profiles.custom_fields'|trans }}</h5>
                        <p class="text-secondary">{{ 'profiles.custom_fields_desc'|trans }}</p>
                        
                        {{ form_widget(form.customFields) }}
                    </div>

                    <div class="mb-3">
                        <h5>{{ 'profiles.organization_custom_fields'|trans }}</h5>
                        <p class="text-secondary">{{ 'profiles.organization_custom_fields_desc'|trans }}</p>
                        
                        {{ form_widget(form.organizationCustomFields) }}
                    </div>

                    <div class="card-footer">
                        <div class="d-flex">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save me-2"></i> {{ 'common.save'|trans }}
                            </button>
                            <a href="{{ path('app_social_profile_view', {id: profile.id}) }}" class="btn btn-link ms-auto">
                                {{ 'common.cancel'|trans }}
                            </a>
                        </div>
                    </div>
                    
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
{% endblock %}
