{% extends 'app/dashboard/page.html.twig' %}

{% block title %}{{ profile.data.username ?? profile.data.name ?? profile.metaId }} | {{ 'profiles.social_profiles'|trans }}{% endblock %}

{% block page_actions %}
    <div class="btn-list">
        <a href="{{ path('app_social_profile_index') }}" class="btn btn-outline-primary">
            <i class="fa fa-arrow-left me-2"></i> {{ 'common.back'|trans }}
        </a>
        <a href="{{ path('app_social_profile_update', {id: profile.id}) }}" class="btn btn-primary">
            <i class="fa fa-sync me-2"></i> {{ 'profiles.update_profile'|trans }}
        </a>
        <a href="{{ path('app_social_profile_configure', {id: profile.id}) }}" class="btn btn-outline-secondary">
            <i class="fa fa-cog me-2"></i> {{ 'profiles.configure'|trans }}
        </a>
    </div>
{% endblock %}

{% block page_content %}
    <div class="row">
        <!-- Profile Information -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    {% set profile_picture = profile.data.profile_picture_url ?? profile.data.picture.data.url ?? asset('static/avatars/000m.jpg') %}
                    <div class="mb-3">
                        <span class="avatar avatar-xl rounded" style="background-image: url({{ profile_picture }})"></span>
                    </div>
                    <h3 class="card-title">{{ profile.data.username ?? profile.data.name ?? profile.metaId }}</h3>
                    <p class="text-secondary">{{ profile.platform|capitalize }}</p>
                    
                    {% if profile.data.bio %}
                        <div class="mt-3">
                            <p class="text-secondary">{{ profile.data.bio|nl2br }}</p>
                        </div>
                    {% endif %}

                    <div class="row mt-4">
                        <div class="col-4">
                            <div class="text-center">
                                <div class="h3 m-0">{{ (profile.data.media_count ?? 0)|number_format }}</div>
                                <div class="text-secondary small">{{ 'profiles.posts'|trans }}</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <div class="h3 m-0">{{ (profile.data.followers_count ?? 0)|number_format }}</div>
                                <div class="text-secondary small">{{ 'profiles.followers'|trans }}</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <div class="h3 m-0">{{ (profile.data.following_count ?? 0)|number_format }}</div>
                                <div class="text-secondary small">{{ 'profiles.following'|trans }}</div>
                            </div>
                        </div>
                    </div>

                    {% if profile.data.external_url %}
                        <div class="mt-3">
                            <a href="{{ profile.data.external_url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                <i class="fa fa-external-link-alt me-1"></i> {{ 'profiles.visit_profile'|trans }}
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Custom Fields Configuration -->
            {% if customFieldsForm is defined %}
                <div class="card mt-3">
                    <div class="card-header">
                        <h3 class="card-title">{{ 'profiles.custom_fields'|trans }}</h3>
                    </div>
                    <div class="card-body">
                        {{ form_start(customFieldsForm) }}
                        {{ form_widget(customFieldsForm) }}
                        <div class="mt-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save me-2"></i> {{ 'common.save'|trans }}
                            </button>
                        </div>
                        {{ form_end(customFieldsForm) }}
                    </div>
                </div>
            {% endif %}

            <!-- Profile Metadata -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">{{ 'profiles.metadata'|trans }}</h3>
                </div>
                <div class="card-body">
                    <dl class="row">
                        <dt class="col-5">{{ 'profiles.platform'|trans }}:</dt>
                        <dd class="col-7">{{ profile.platform|capitalize }}</dd>
                        
                        <dt class="col-5">{{ 'profiles.meta_id'|trans }}:</dt>
                        <dd class="col-7"><code>{{ profile.metaId }}</code></dd>
                        
                        <dt class="col-5">{{ 'profiles.username'|trans }}:</dt>
                        <dd class="col-7">{{ profile.username ?? 'N/A' }}</dd>
                        
                        <dt class="col-5">{{ 'profiles.created_at'|trans }}:</dt>
                        <dd class="col-7">{{ profile.createdAt ? profile.createdAt|date('Y-m-d H:i:s') : 'N/A' }}</dd>
                        
                        <dt class="col-5">{{ 'profiles.updated_at'|trans }}:</dt>
                        <dd class="col-7">{{ profile.updatedAt ? profile.updatedAt|date('Y-m-d H:i:s') : 'N/A' }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Posts -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ 'profiles.recent_posts'|trans }}</h3>
                </div>
                <div class="card-body">
                    <div id="posts-container" class="row row-cards">
                        <!-- Posts will be loaded here -->
                    </div>
                    <div class="text-center mt-3">
                        <button id="load-more-posts" class="btn btn-outline-primary" data-offset="0">
                            <i class="fa fa-plus me-2"></i> {{ 'profiles.load_more_posts'|trans }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const postsContainer = document.getElementById('posts-container');
            const loadMoreBtn = document.getElementById('load-more-posts');
            let isLoading = false;

            function loadPosts(offset = 0) {
                if (isLoading) return;
                
                isLoading = true;
                loadMoreBtn.disabled = true;
                loadMoreBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-2"></i> {{ 'common.loading'|trans }}';

                fetch(`{{ path('app_social_profile_posts', {id: profile.id}) }}?offset=${offset}&limit=12`)
                    .then(response => response.json())
                    .then(posts => {
                        if (posts.length === 0) {
                            loadMoreBtn.style.display = 'none';
                            if (offset === 0) {
                                postsContainer.innerHTML = `
                                    <div class="col-12">
                                        <div class="empty">
                                            <div class="empty-img">
                                                <img src="{{ asset('static/illustrations/undraw_no_data_qbuo.svg') }}" height="128" alt="">
                                            </div>
                                            <p class="empty-title">{{ 'profiles.no_posts'|trans }}</p>
                                            <p class="empty-subtitle text-secondary">{{ 'profiles.no_posts_desc'|trans }}</p>
                                        </div>
                                    </div>
                                `;
                            }
                            return;
                        }

                        posts.forEach(post => {
                            const postElement = createPostElement(post);
                            postsContainer.appendChild(postElement);
                        });

                        const newOffset = offset + posts.length;
                        loadMoreBtn.setAttribute('data-offset', newOffset);
                        
                        if (posts.length < 12) {
                            loadMoreBtn.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Error loading posts:', error);
                        // Show error message
                    })
                    .finally(() => {
                        isLoading = false;
                        loadMoreBtn.disabled = false;
                        loadMoreBtn.innerHTML = '<i class="fa fa-plus me-2"></i> {{ 'profiles.load_more_posts'|trans }}';
                    });
            }

            function createPostElement(post) {
                const div = document.createElement('div');
                div.className = 'col-md-6 col-lg-4';
                
                const timestamp = post.data.timestamp || post.data.created_time;
                const date = timestamp ? new Date(timestamp * 1000).toLocaleDateString() : '';
                const caption = post.data.caption || post.data.message || '';
                const mediaUrl = post.data.media_url || post.data.full_picture || '';
                const likeCount = post.data.like_count || 0;
                const commentCount = post.data.comments_count || 0;

                div.innerHTML = `
                    <div class="card">
                        ${mediaUrl ? `<img src="${mediaUrl}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="Post media">` : ''}
                        <div class="card-body">
                            ${caption ? `<p class="card-text">${caption.substring(0, 100)}${caption.length > 100 ? '...' : ''}</p>` : ''}
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">${date}</small>
                                <div class="btn-group btn-group-sm">
                                    <span class="badge bg-blue-lt me-1">
                                        <i class="fa fa-heart me-1"></i> ${likeCount.toLocaleString()}
                                    </span>
                                    <span class="badge bg-green-lt">
                                        <i class="fa fa-comment me-1"></i> ${commentCount.toLocaleString()}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                return div;
            }

            loadMoreBtn.addEventListener('click', function() {
                const offset = parseInt(this.getAttribute('data-offset'));
                loadPosts(offset);
            });

            // Load initial posts
            loadPosts(0);
        });
    </script>
{% endblock %}
