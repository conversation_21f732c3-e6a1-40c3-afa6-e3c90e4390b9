{% extends 'app/dashboard/page.html.twig' %}

{% block title %}Profile Tracking | Magnora{% endblock %}

{% block page_content %}
    <div class="row row-deck row-cards">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Tracking Groups</h3>
                </div>
                {{ component('Social:TrackingGroupList') }}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener("click", function (event) {
            let target = event.target;
            if (target.classList.contains('btn-delete-row')) {
                confirmDelete(target.getAttribute('data-href'));
            }
        });

        function confirmDelete(link) {
            let result = confirm("Are you sure you want to delete this? It's irreversible!");
            if (result) {
                // If confirmed, allow the link to navigate
                window.location.replace(link);
                return true;
            } else {
                // If canceled, prevent the link from being followed
                return false;
            }
        }
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            setInterval(() => {
                document.getElementById('social-tracking-group-status-controller').__component.render();
            }, 10000); // Refresh every 10 seconds
        });
    </script>
{% endblock %}