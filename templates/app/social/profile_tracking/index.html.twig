{% extends 'app/modern_dashboard/base.html.twig' %}

{% block title %}Tracking Groups | Magnora{% endblock %}
{% block page_title %}Tracking Groups{% endblock %}

{% block content %}
    <!-- Tracking Groups List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">All Tracking Groups</h2>
            <div class="flex flex-col sm:flex-row gap-3">
                <div class="relative">
                    <input type="text" id="search" placeholder="Search groups..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <select id="filter-status" class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="all">All Status</option>
                        <option value="running">Running</option>
                        <option value="waiting_next_update">Waiting</option>
                        <option value="finished">Finished</option>
                        <option value="paused">Paused</option>
                    </select>

                    <!-- View toggle -->
                    <div class="flex rounded-md shadow-sm" role="group">
                        <button type="button" id="table-view-btn" class="px-3 py-2 text-sm font-medium rounded-l-md bg-primary-600 text-white hover:bg-primary-700 focus:z-10 focus:ring-2 focus:ring-primary-500">
                            <i class="fas fa-table"></i>
                        </button>
                        <button type="button" id="card-view-btn" class="px-3 py-2 text-sm font-medium rounded-r-md bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 focus:z-10 focus:ring-2 focus:ring-primary-500">
                            <i class="fas fa-th-large"></i>
                        </button>
                    </div>

                    <a href="{{ path('app_modern_tracking_group_create') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-plus mr-2"></i> Add Group
                    </a>
                </div>
            </div>
        </div>

        <!-- Table View -->
        <div id="table-view" class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Data Source</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Progress</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Created</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Updated</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {% if trackingGroups|length > 0 %}
                        {% for group in trackingGroups %}
                            {% set percentage = group.status.totalProfiles > 0 ? ((group.status.progress / group.status.totalProfiles) * 100)|round : 0 %}
                            {% set statusClass = 'text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700' %}

                            {% if group.status.status.value == 'processing' %}
                                {% set statusClass = 'text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900' %}
                            {% elseif group.status.status.value == 'waiting_next_update' %}
                                {% set statusClass = 'text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900' %}
                            {% elseif group.status.status.value == 'finished' %}
                                {% set statusClass = 'text-purple-700 dark:text-purple-300 bg-purple-100 dark:bg-purple-900' %}
                            {% elseif group.status.status.value == 'paused' %}
                                {% set statusClass = 'text-yellow-700 dark:text-yellow-300 bg-yellow-100 dark:bg-yellow-900' %}
                            {% endif %}

                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors duration-150" data-status="{{ group.status.status.value }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-600 dark:text-primary-400">
                                            {{ group.name|first|upper }}
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white">{{ group.name }}</div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">ID: {{ group.id }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900 dark:text-white">{{ group.dataSource }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ group.listType }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% include 'components/Modern/TrackingGroupStatusBadge.html.twig' with {'status': group.status.status.value} %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-2 w-24">
                                            <div class="bg-primary-600 h-2.5 rounded-full {% if group.status.status.value == 'processing' %}animate-pulse{% endif %}" style="width: {{ percentage }}%"></div>
                                        </div>
                                        <span class="text-sm text-gray-500 dark:text-gray-400">{{ group.status.progress }} / {{ group.status.totalProfiles }}</span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ group.createdAt|date('M d, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ group.updatedAt|date('M d, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-2">
                                        <button class="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if group.status.status.value == 'processing' %}
                                            <button class="p-2 rounded-md text-yellow-500 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-200 hover:bg-yellow-100 dark:hover:bg-yellow-900" title="Pause">
                                                <i class="fas fa-pause"></i>
                                            </button>
                                        {% elseif group.status.status.value == 'paused' %}
                                            <button class="p-2 rounded-md text-green-500 hover:text-green-700 dark:text-green-400 dark:hover:text-green-200 hover:bg-green-100 dark:hover:bg-green-900" title="Resume">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        {% endif %}
                                        <button class="p-2 rounded-md text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-200 hover:bg-red-100 dark:hover:bg-red-900" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="7" class="px-6 py-10 text-center text-sm text-gray-500 dark:text-gray-400">
                                <div class="flex flex-col items-center">
                                    <svg class="h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No tracking groups</h3>
                                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating a new tracking group.</p>
                                    <div class="mt-6">
                                        <a href="{{ path('app_modern_tracking_group_create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                            <i class="fas fa-plus mr-2"></i> {{ 'tracking_group.create_new'|trans }}
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>

        <!-- Card View -->
        <div id="card-view" class="hidden p-6">
            {% if trackingGroups|length > 0 %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for group in trackingGroups %}
                        {% set percentage = group.status.totalProfiles > 0 ? ((group.status.progress / group.status.totalProfiles) * 100)|round : 0 %}

                        {% set statusColor = {
                            'processing': {
                                'border': 'border-blue-500',
                                'bg': 'bg-blue-50 dark:bg-blue-900/20',
                                'text': 'text-blue-700 dark:text-blue-300',
                                'badge': 'bg-blue-100 dark:bg-blue-900',
                                'icon': 'bg-blue-500',
                                'progress': 'bg-blue-600 dark:bg-blue-500',
                                'animate': 'animate-pulse',
                                'glow': 'shadow-[0_0_15px_rgba(59,130,246,0.5)]',
                                'card': 'ring-1 ring-blue-500/50'
                            },
                            'waiting_next_update': {
                                'border': 'border-green-500',
                                'bg': 'bg-green-50 dark:bg-green-900/20',
                                'text': 'text-green-700 dark:text-green-300',
                                'badge': 'bg-green-100 dark:bg-green-900',
                                'icon': 'bg-green-500',
                                'progress': 'bg-green-600 dark:bg-green-500',
                                'animate': '',
                                'glow': '',
                                'card': 'ring-1 ring-green-500/50'
                            },
                            'finished': {
                                'border': 'border-purple-500',
                                'bg': 'bg-purple-50 dark:bg-purple-900/20',
                                'text': 'text-purple-700 dark:text-purple-300',
                                'badge': 'bg-purple-100 dark:bg-purple-900',
                                'icon': 'bg-purple-500',
                                'progress': 'bg-purple-600 dark:bg-purple-500',
                                'animate': '',
                                'glow': '',
                                'card': 'ring-1 ring-purple-500/50'
                            },
                            'paused': {
                                'border': 'border-yellow-500',
                                'bg': 'bg-yellow-50 dark:bg-yellow-900/20',
                                'text': 'text-yellow-700 dark:text-yellow-300',
                                'badge': 'bg-yellow-100 dark:bg-yellow-900',
                                'icon': 'bg-yellow-500',
                                'progress': 'bg-yellow-600 dark:bg-yellow-500',
                                'animate': '',
                                'glow': '',
                                'card': 'ring-1 ring-yellow-500/50'
                            },
                            'pause_requested': {
                                'border': 'border-amber-500',
                                'bg': 'bg-amber-50 dark:bg-amber-900/20',
                                'text': 'text-amber-700 dark:text-amber-300',
                                'badge': 'bg-amber-100 dark:bg-amber-900',
                                'icon': 'bg-amber-500',
                                'progress': 'bg-amber-600 dark:bg-amber-500',
                                'animate': 'animate-pulse',
                                'glow': 'shadow-[0_0_15px_rgba(245,158,11,0.5)]',
                                'card': 'ring-1 ring-amber-500/50'
                            },
                            'queued': {
                                'border': 'border-indigo-500',
                                'bg': 'bg-indigo-50 dark:bg-indigo-900/20',
                                'text': 'text-indigo-700 dark:text-indigo-300',
                                'badge': 'bg-indigo-100 dark:bg-indigo-900',
                                'icon': 'bg-indigo-500',
                                'progress': 'bg-indigo-600 dark:bg-indigo-500',
                                'animate': 'animate-pulse',
                                'glow': '',
                                'card': 'ring-1 ring-indigo-500/50'
                            },
                            'error': {
                                'border': 'border-red-500',
                                'bg': 'bg-red-50 dark:bg-red-900/20',
                                'text': 'text-red-700 dark:text-red-300',
                                'badge': 'bg-red-100 dark:bg-red-900',
                                'icon': 'bg-red-500',
                                'progress': 'bg-red-600 dark:bg-red-500',
                                'animate': '',
                                'glow': 'shadow-[0_0_15px_rgba(239,68,68,0.5)]',
                                'card': 'ring-1 ring-red-500/50'
                            }
                        } %}

                        {% set status = group.status.status.value %}
                        {% set colors = statusColor[status] ?? {
                            'border': 'border-gray-300',
                            'bg': 'bg-gray-50 dark:bg-gray-800',
                            'text': 'text-gray-700 dark:text-gray-300',
                            'badge': 'bg-gray-100 dark:bg-gray-700',
                            'icon': 'bg-gray-500',
                            'progress': 'bg-gray-600 dark:bg-gray-500',
                            'animate': '',
                            'glow': '',
                            'card': ''
                        } %}

                        {% set statusIcons = {
                            'processing': 'fa-play-circle',
                            'waiting_next_update': 'fa-clock',
                            'finished': 'fa-check-circle',
                            'paused': 'fa-pause-circle',
                            'pause_requested': 'fa-hand-paper',
                            'queued': 'fa-hourglass-half',
                            'error': 'fa-exclamation-triangle'
                        } %}

                        {% set statusIcon = statusIcons[status] ?? 'fa-question' %}

                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-200 hover:shadow-md hover:translate-y-[-2px] {{ colors.card }} {{ colors.glow }}" data-status="{{ status }}">
                            <!-- Card Header with Status -->
                            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 {{ colors.bg }} flex justify-between items-center">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 rounded-full {{ colors.badge }} flex items-center justify-center {{ colors.text }}">
                                        <i class="fas {{ statusIcon }} {{ colors.animate }}"></i>
                                    </div>
                                    <h3 class="text-base font-medium text-gray-900 dark:text-white truncate max-w-[150px]" title="{{ group.name }}">{{ group.name }}</h3>
                                </div>
                                <div class="flex flex-col items-end">
                                    {% include 'components/Modern/TrackingGroupStatusBadge.html.twig' with {'status': status} %}
                                </div>
                            </div>

                            <!-- Card Body -->
                            <div class="p-4">
                                <!-- Key Stats -->
                                <div class="grid grid-cols-2 gap-3 mb-4">
                                    <div class="bg-gray-50 dark:bg-gray-700/50 rounded p-2 text-center">
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Profiles</div>
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ group.status.totalProfiles }}</div>
                                    </div>
                                    <div class="bg-gray-50 dark:bg-gray-700/50 rounded p-2 text-center">
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Source</div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-white truncate" title="{{ group.dataSource }}">{{ group.dataSource }}</div>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <div class="mb-4">
                                    <div class="flex justify-between text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        <span>Progress</span>
                                        <span>{{ percentage }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="{{ colors.progress }} h-2 rounded-full {{ colors.animate }}" style="width: {{ percentage }}%"></div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <span>{{ group.status.progress }} processed</span>
                                        <span>{{ group.status.totalProfiles }} total</span>
                                    </div>
                                </div>

                                <!-- Metadata -->
                                <div class="text-xs text-gray-500 dark:text-gray-400 mb-4">
                                    <div class="flex justify-between mb-1">
                                        <span>Created:</span>
                                        <span>{{ group.createdAt|date('M d, Y') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Updated:</span>
                                        <span>{{ group.updatedAt|date('M d, Y H:i') }}</span>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex justify-between border-t border-gray-200 dark:border-gray-700 pt-3">
                                    <a href="{{ path('app_social_tracking_group_view', {id: group.id}) }}" class="text-xs flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                        <i class="fas fa-eye mr-1"></i> View Details
                                    </a>

                                    <div class="flex space-x-1">
                                        {% if status == 'processing' %}
                                            <a href="{{ path('app_social_tracking_group_pause', {id: group.id}) }}" class="text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-700 hover:bg-yellow-200 dark:bg-yellow-900/50 dark:text-yellow-300 dark:hover:bg-yellow-900">
                                                <i class="fas fa-pause mr-1"></i> Pause
                                            </a>
                                        {% elseif status == 'paused' %}
                                            <a href="{{ path('app_social_tracking_group_queue', {id: group.id}) }}" class="text-xs px-2 py-1 rounded bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/50 dark:text-green-300 dark:hover:bg-green-900">
                                                <i class="fas fa-play mr-1"></i> Resume
                                            </a>
                                        {% endif %}

                                        <button type="button" class="group relative text-xs px-2 py-1 rounded bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                                            <i class="fas fa-ellipsis-h"></i>
                                            <div class="hidden group-hover:block absolute right-0 bottom-8 w-32 bg-white dark:bg-gray-800 rounded shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-10">
                                                <a href="{{ path('app_social_tracking_group_edit', {id: group.id}) }}" class="block px-3 py-1 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                    <i class="fas fa-edit mr-2"></i> Edit
                                                </a>
                                                <a href="{{ path('app_social_tracking_group_force_update', {id: group.id}) }}" class="block px-3 py-1 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                    <i class="fas fa-sync mr-2"></i> Update
                                                </a>
                                                <a href="{{ path('app_social_tracking_group_delete', {id: group.id}) }}" class="block px-3 py-1 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30">
                                                    <i class="fas fa-trash mr-2"></i> Delete
                                                </a>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No tracking groups</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Get started by creating a new tracking group.</p>
                    <div class="mt-6">
                        <a href="{{ path('app_modern_tracking_group_create') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-2"></i> {{ 'tracking_group.create_new'|trans }}
                        </a>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                    Previous
                </a>
                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                    Next
                </a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                        Showing <span class="font-medium">1</span> to <span class="font-medium">{{ trackingGroups|length }}</span> of <span class="font-medium">{{ trackingGroups|length }}</span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <span class="sr-only">Previous</span>
                            <i class="fas fa-chevron-left h-5 w-5"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            1
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <span class="sr-only">Next</span>
                            <i class="fas fa-chevron-right h-5 w-5"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // View toggle functionality
            const tableViewBtn = document.getElementById('table-view-btn');
            const cardViewBtn = document.getElementById('card-view-btn');
            const tableView = document.getElementById('table-view');
            const cardView = document.getElementById('card-view');

            tableViewBtn.addEventListener('click', function() {
                tableView.classList.remove('hidden');
                cardView.classList.add('hidden');
                tableViewBtn.classList.remove('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
                tableViewBtn.classList.add('bg-primary-600', 'text-white');
                cardViewBtn.classList.remove('bg-primary-600', 'text-white');
                cardViewBtn.classList.add('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');

                // Save preference to localStorage
                localStorage.setItem('tracking-groups-view', 'table');
            });

            cardViewBtn.addEventListener('click', function() {
                tableView.classList.add('hidden');
                cardView.classList.remove('hidden');
                cardViewBtn.classList.remove('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');
                cardViewBtn.classList.add('bg-primary-600', 'text-white');
                tableViewBtn.classList.remove('bg-primary-600', 'text-white');
                tableViewBtn.classList.add('bg-gray-200', 'text-gray-700', 'dark:bg-gray-700', 'dark:text-gray-300');

                // Save preference to localStorage
                localStorage.setItem('tracking-groups-view', 'card');
            });

            // Load saved preference
            const savedView = localStorage.getItem('tracking-groups-view');
            if (savedView === 'card') {
                cardViewBtn.click();
            }

            // Filter functionality
            const filterStatus = document.getElementById('filter-status');
            const searchInput = document.getElementById('search');
            const tableRows = document.querySelectorAll('tbody tr');
            const cardItems = document.querySelectorAll('#card-view [data-status]');

            function filterItems() {
                const statusFilter = filterStatus.value;
                const searchTerm = searchInput.value.toLowerCase();

                // Filter table rows
                tableRows.forEach(row => {
                    const rowStatus = row.getAttribute('data-status');
                    const rowText = row.textContent.toLowerCase();
                    const statusMatch = statusFilter === 'all' || rowStatus === statusFilter;
                    const searchMatch = searchTerm === '' || rowText.includes(searchTerm);

                    if (statusMatch && searchMatch) {
                        row.classList.remove('hidden');
                    } else {
                        row.classList.add('hidden');
                    }
                });

                // Filter card items
                cardItems.forEach(card => {
                    const cardStatus = card.getAttribute('data-status');
                    const cardText = card.textContent.toLowerCase();
                    const statusMatch = statusFilter === 'all' || cardStatus === statusFilter;
                    const searchMatch = searchTerm === '' || cardText.includes(searchTerm);

                    if (statusMatch && searchMatch) {
                        card.classList.remove('hidden');
                    } else {
                        card.classList.add('hidden');
                    }
                });
            }

            filterStatus.addEventListener('change', filterItems);
            searchInput.addEventListener('input', filterItems);

            // Auto-refresh every 10 seconds
            setInterval(() => {
                // In a real implementation, this would fetch new data from the server
                // For now, we'll just simulate progress updates for running tracking groups

                // Update table view
                tableRows.forEach(row => {
                    if (row.getAttribute('data-status') === 'processing') {
                        const progressBar = row.querySelector('.w-full.bg-gray-200.dark\\:bg-gray-700.rounded-full.h-2\\.5 > div');
                        const progressText = row.querySelector('.text-sm.text-gray-500.dark\\:text-gray-400');

                        if (progressBar && progressText) {
                            const progressInfo = progressText.textContent.split('/');
                            let current = parseInt(progressInfo[0].trim());
                            const total = parseInt(progressInfo[1].trim());

                            // Increment progress
                            current = Math.min(current + Math.floor(Math.random() * 3) + 1, total);
                            const percentage = (current / total) * 100;

                            // Update UI
                            progressBar.style.width = `${percentage}%`;
                            progressText.textContent = `${current} / ${total}`;
                        }
                    }
                });

                // Update card view
                cardItems.forEach(card => {
                    if (card.getAttribute('data-status') === 'processing') {
                        const progressBar = card.querySelector('.w-full.bg-gray-200.dark\\:bg-gray-700.rounded-full.h-2 > div');
                        const progressText = card.querySelectorAll('.text-xs.text-gray-500.dark\\:text-gray-400')[1]?.querySelector('span:first-child');
                        const percentageText = card.querySelector('.text-xs.font-medium.text-gray-700.dark\\:text-gray-300 span:last-child');

                        if (progressBar && progressText) {
                            const current = parseInt(progressText.textContent.split(' ')[0]);
                            const total = parseInt(card.querySelector('.text-lg.font-semibold').textContent);

                            // Increment progress
                            const newCurrent = Math.min(current + Math.floor(Math.random() * 3) + 1, total);
                            const percentage = (newCurrent / total) * 100;

                            // Update UI
                            progressBar.style.width = `${percentage}%`;
                            progressText.textContent = `${newCurrent} processed`;
                            if (percentageText) {
                                percentageText.textContent = `${Math.round(percentage)}%`;
                            }
                        }
                    }
                });
            }, 10000);
        });
    </script>
{% endblock %}
