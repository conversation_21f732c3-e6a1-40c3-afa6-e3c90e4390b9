{% extends 'app/dashboard/page.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .timeline-event-icon i {
            text-align: center;
        }

        @media (min-width: 768px) {
            .adapt-h {
                height: 100%;
            }
        }
    </style>
{% endblock %}

{% block title %}View Tracking Group | {{ trackingGroup.name }}{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener("click", function (event) {
            let target = event.target;
            if (target.classList.contains('btn-delete-row')) {
                confirmDelete(target.getAttribute('data-href'));
            }
        });

        function confirmDelete(link) {
            let result = confirm("Are you sure you want to delete this? It's irreversible!");
            if (result) {
                // If confirmed, allow the link to navigate
                window.location.replace(link);
                return true;
            } else {
                // If canceled, prevent the link from being followed
                return false;
            }
        }
    </script>
{% endblock %}

{% block page_title %}Social Tracking Group {{ trackingGroup.name }}{% endblock %}
{% block page_subtitle %}{% endblock %}
{% block page_actions %}
    <div class="btn-list">
        <a href="{{ path('app_social_tracking_group_edit', { id: trackingGroup.id }) }}" class="btn btn-primary">
            <i class="fa fa-pencil icon icon-2"></i> Edit
        </a>
        <a class="btn btn-outline-danger btn-delete-row" href="#" data-href="{{ path('app_social_tracking_group_delete', { id: trackingGroup.id }) }}"><i class="fa fa-trash-can me-2"></i> Delete</a>
    </div>
{% endblock %}

{% block page_content %}
    {{ component('Social:TrackingGroupStats', { trackingGroup: trackingGroup }) }}

    <div class="row row-cards d-flex align-content-stretch align-items-stretch mb-3">
        <div class="col-12 col-md-4">
            <div class="card adapt-h">
                <div class="card-body p-4 py-5 text-left">
                    <span class="avatar avatar-xl mb-4 rounded bg-primary-lt">{{ trackingGroup.name|first|upper }}</span>
                    <h3 class="mb-0">{{ trackingGroup.name }}</h3>
                    <p class="text-secondary">Created At: {{ trackingGroup.createdAt|date('Y-m-d H:i:s') }}</p>
                    <div class="mt-3 mb-3">
                        <span class="status {{ trackingGroup.statusClass }}">
                            <span class="status-dot {% if trackingGroup.statusFinished != true %}status-dot-animated{% endif %}"></span>
                            {{ trackingGroup.statusLabel }}
                        </span>
                    </div>

                    <div class="mt-4">
                        {% if trackingGroup.status.status.value == 'finished' %}
                            <a href="{{ path('app_social_tracking_group_queue', { id: trackingGroup.id }) }}" class="btn btn-primary">
                                <i class="fa fa-arrows-rotate me-2"></i> Restart
                            </a>
                        {% elseif trackingGroup.status.status.value == 'waiting_next_update' %}
                            <a href="{{ path('app_social_tracking_group_force_update', { id: trackingGroup.id }) }}" class="btn btn-primary">
                                <i class="fa fa-arrows-rotate me-2"></i> Update Now
                            </a>
                        {% elseif trackingGroup.status.status.value == 'processing' or trackingGroup.status.status.value == 'queued' or trackingGroup.status.status.value == 'update_requested' %}
                            <a href="{{ path('app_social_tracking_group_pause', { id: trackingGroup.id }) }}" class="btn btn-warning">
                                <i class="fa fa-pause me-2"></i> Pause
                            </a>
                        {% elseif trackingGroup.status.status.value == 'paused' %}
                            <a href="{{ path('app_social_tracking_group_queue', { id: trackingGroup.id }) }}" class="btn btn-success">
                                <i class="fa fa-play me-2"></i> Resume
                            </a>
                        {% endif %}
                    </div>
                </div>
                {% set percentage = trackingGroup.status.totalProfiles > 0 ? ((trackingGroup.status.progress / trackingGroup.status.totalProfiles) * 100)|round : 0 %}
                {% if trackingGroup.statusFinished != true %}
                    <div class="progress progress-3 card-progress">
                        <div class="progress-bar {{ trackingGroup.progressBarClass }} progress-bar-striped progress-bar-animated" style="width: {{ percentage }}%" role="progressbar" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100" aria-label="{{ percentage }}% Complete">
                            <span class="visually-hidden">{{ percentage }}%</span>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
        <div class="col-12 col-md-8">
            <div class="card">
                <div class="card-body">
                    {% if is_granted('ROLE_ADMIN') %}
                        <h4>API URL</h4>
                        <div>
                            <pre><code>GET <a class="text-reset">{{ absolute_url(path('app_api_social_tracking_group_all', { id: trackingGroup.id })) }}</a></code></pre>
                        </div>
                        <h4>Required Request Headers</h4>
                        <div>
                            <pre>Authorization: <code>Bearer {{ trackingGroup.credential.token }}</code></pre>
                        </div>
                        <h4>Returned Header</h4>
                        <div>
                            <pre>Content-Type: <code>application/json</code></pre>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <h3>History</h3>
    <ul class="timeline">
        {% for entry in trackingGroup.trackingHistory|reverse %}
            <li class="timeline-event">
                <div class="timeline-event-icon {{ loop.last == false ? 'bg-blue-lt' : 'bg-green-lt' }}">
                    <i class="{{ loop.last == false ? 'far fa-circle-check' : 'fa fa-plus' }} icon icon-2"></i>
                </div>
                <div class="card timeline-event-card">
                    <div class="card-body">
                        <div class="text-secondary float-end">{{ entry.createdAt|date('Y-m-d H:i:s') }}</div>
                        <h4>{{ loop.last == false ? 'Tracking Group Data Updated' : 'Tracking Group Data Created' }}</h4>
                        <p class="text-secondary">Tracked {{ entry.progress }} out of {{ entry.totalProfiles }}.</p>
                        <h5 class="mb-0">Used Credits</h5>
                        <p class="text-secondary">Profiles: {{ entry.creditDetails.profiles ?? 0 }} | Posts: {{ entry.creditDetails.posts ?? 0 }}  | Comments: {{ entry.creditDetails.comments ?? 0 }}</p>
                    </div>
                </div>
            </li>
        {% endfor %}
    </ul>
{% endblock %}
