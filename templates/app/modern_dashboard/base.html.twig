<!DOCTYPE html>
<html lang="{{ app.request.locale }}" class="h-full {{ app.request.cookies.get('theme') == 'dark' ? 'dark' : '' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ 'app.name'|trans }} - {{ 'app.tagline'|trans }}{% endblock %}</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 128 128%22><text y=%221.2em%22 font-size=%2296%22>M</text></svg>">

    {% block stylesheets %}
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            tailwind.config = {
                darkMode: 'class',
                theme: {
                    extend: {
                        colors: {
                            primary: {
                                50: '#eef2ff',
                                100: '#e0e7ff',
                                200: '#c7d2fe',
                                300: '#a5b4fc',
                                400: '#818cf8',
                                500: '#6366f1',
                                600: '#4f46e5',
                                700: '#4338ca',
                                800: '#3730a3',
                                900: '#312e81',
                                950: '#1e1b4b',
                            }
                        }
                    }
                }
            }
        </script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
        <style type="text/tailwindcss">
            @layer base {
                :root {
                    color-scheme: light;
                }

                .dark {
                    color-scheme: dark;
                }
            }

            @layer components {
                .card {
                    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md;
                }

                .card-header {
                    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
                }

                .card-body {
                    @apply p-6;
                }

                .card-footer {
                    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700;
                }
            }
        </style>
    {% endblock %}

    {% block javascripts %}
        {{ importmap(['app', 'dashboard', 'modern_dashboard']) }}
    {% endblock %}
</head>
<body class="h-full bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 antialiased transition-colors duration-200">
    <div class="min-h-full">
        <!-- Sidebar -->
        <div id="sidebar" class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-md transform transition-transform duration-300 ease-in-out lg:translate-x-0 -translate-x-full">
            <div class="flex items-center justify-between h-16 px-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex items-center">
                    <span class="text-xl font-bold text-primary-600 dark:text-primary-400">Magnora</span>
                </div>
                <button id="close-sidebar" class="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 lg:hidden">
                    <span class="sr-only">Close sidebar</span>
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Organization Selector -->
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                <select id="switch-org" class="w-full px-3 py-2 text-sm rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                    {% for org in app.user.organizationUsers %}
                        {% set organization = org.organization %}
                        <option value="{{ organization.id }}" {% if app.session.get('current_organization') == organization.id %}selected{% endif %}>{{ organization.name }}</option>
                    {% endfor %}
                </select>
            </div>

            <!-- Navigation -->
            <nav class="px-2 py-4 space-y-1">
                <a href="{{ path('app_modern_dashboard') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if app.request.get('_route') == 'app_modern_dashboard' %}bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300{% else %}text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700{% endif %}">
                    <i class="fas fa-home mr-3 text-gray-500 dark:text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400"></i>
                    Dashboard
                </a>
                <a href="{{ path('app_modern_tracking_groups') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if app.request.get('_route') == 'app_modern_tracking_groups' %}bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300{% else %}text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700{% endif %}">
                    <i class="fas fa-layer-group mr-3 text-gray-500 dark:text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400"></i>
                    Tracking Groups
                </a>
                <a href="{{ path('app_modern_profiles') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md {% if app.request.get('_route') == 'app_modern_profiles' %}bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300{% else %}text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700{% endif %}">
                    <i class="fas fa-users mr-3 text-gray-500 dark:text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400"></i>
                    Profiles
                </a>
                <a href="{{ path('app_profile') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-user-cog mr-3 text-gray-500 dark:text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400"></i>
                    Settings
                </a>
                <a href="{{ path('app_app_dashboard') }}" class="group flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                    <i class="fas fa-arrow-left mr-3 text-gray-500 dark:text-gray-400 group-hover:text-primary-500 dark:group-hover:text-primary-400"></i>
                    Classic Dashboard
                </a>
            </nav>
        </div>

        <!-- Main content -->
        <div class="lg:pl-64">
            <!-- Top navigation -->
            <div class="sticky top-0 z-40 flex h-16 bg-white dark:bg-gray-800 shadow-sm">
                <button id="open-sidebar" class="px-4 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:hidden">
                    <span class="sr-only">Open sidebar</span>
                    <i class="fas fa-bars"></i>
                </button>
                <div class="flex-1 flex justify-between px-4">
                    <div class="flex-1 flex items-center">
                        <h1 class="text-xl font-semibold">{% block page_title %}Dashboard{% endblock %}</h1>
                    </div>
                    <div class="ml-4 flex items-center md:ml-6 space-x-3">
                        <!-- Theme toggle -->
                        <button id="theme-toggle" class="p-2 rounded-full text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200">
                            <i class="fas fa-sun hidden dark:block"></i>
                            <i class="fas fa-moon block dark:hidden"></i>
                        </button>

                        <!-- Profile dropdown -->
                        <div class="relative">
                            <button id="user-menu-button" class="flex items-center max-w-xs rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <span class="sr-only">Open user menu</span>
                                <div class="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center text-white">
                                    {% if app.user.firstName is defined %}
                                        {{ app.user.firstName|first|upper }}{{ app.user.lastName|first|upper }}
                                    {% else %}
                                        {{ app.user.email|first|upper }}
                                    {% endif %}
                                </div>
                            </button>

                            <!-- Dropdown menu -->
                            <div id="user-menu" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none">
                                <a href="{{ path('app_profile') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Your Profile</a>
                                <a href="{{ path('app_organization') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Organization</a>
                                <a href="{{ path('app_logout') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">Sign out</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content area -->
            <main class="py-6 px-4 sm:px-6 lg:px-8">
                {% block content %}{% endblock %}
            </main>

            <!-- Footer -->
            <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 py-4 px-6">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        Copyright &copy; {{ 'now'|date('Y') }} Terra. All rights reserved.
                    </div>
                    <div class="mt-2 md:mt-0 flex space-x-4">
                        <a href="https://privacy-policy.terra.marketing" target="_blank" class="text-sm text-gray-500 dark:text-gray-400 hover:text-primary-600 dark:hover:text-primary-400">Privacy Policy</a>
                        <span class="text-sm text-gray-500 dark:text-gray-400">v0.9.0-alpha1</span>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme toggle
            const themeToggle = document.getElementById('theme-toggle');
            themeToggle.addEventListener('click', function() {
                const html = document.documentElement;
                const isDark = html.classList.contains('dark');

                if (isDark) {
                    html.classList.remove('dark');
                    document.cookie = 'theme=light;path=/;max-age=31536000';
                } else {
                    html.classList.add('dark');
                    document.cookie = 'theme=dark;path=/;max-age=31536000';
                }
            });

            // Sidebar toggle for mobile
            const openSidebar = document.getElementById('open-sidebar');
            const closeSidebar = document.getElementById('close-sidebar');
            const sidebar = document.getElementById('sidebar');

            openSidebar.addEventListener('click', function() {
                sidebar.classList.remove('-translate-x-full');
            });

            closeSidebar.addEventListener('click', function() {
                sidebar.classList.add('-translate-x-full');
            });

            // User menu toggle
            const userMenuButton = document.getElementById('user-menu-button');
            const userMenu = document.getElementById('user-menu');

            userMenuButton.addEventListener('click', function() {
                userMenu.classList.toggle('hidden');
            });

            // Close user menu when clicking outside
            document.addEventListener('click', function(event) {
                if (!userMenuButton.contains(event.target) && !userMenu.contains(event.target)) {
                    userMenu.classList.add('hidden');
                }
            });

            // Organization switcher
            let switchOrg = document.getElementById('switch-org');
            if (switchOrg) {
                switchOrg.addEventListener('change', function() {
                    window.location.href = '/?switch-to-org=' + switchOrg.value;
                });
            }
        });
    </script>
</body>
</html>
