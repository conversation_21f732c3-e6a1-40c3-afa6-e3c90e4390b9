{% extends 'app/modern_dashboard/base.html.twig' %}

{% block title %}{{ 'menu.profile_list'|trans(domain='menu') }} | {{ 'app.name'|trans }}{% endblock %}
{% block page_title %}{{ 'menu.profile_list'|trans(domain='menu') }}{% endblock %}

{% block content %}
    <!-- Profiles List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">{{ 'profiles.social_profiles'|trans }}</h2>
            <div class="flex flex-col sm:flex-row gap-3">
                <div class="relative">
                    <input type="text" id="search" placeholder="Search profiles..." class="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <select id="filter-platform" class="block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                        <option value="all" {% if platformFilter == 'all' %}selected{% endif %}>{{ 'profiles.all_platforms'|trans }}</option>
                        <option value="instagram" {% if platformFilter == 'instagram' %}selected{% endif %}>Instagram</option>
                        <option value="twitter" {% if platformFilter == 'twitter' %}selected{% endif %}>Twitter</option>
                        <option value="facebook" {% if platformFilter == 'facebook' %}selected{% endif %}>Facebook</option>
                        <option value="tiktok" {% if platformFilter == 'tiktok' %}selected{% endif %}>TikTok</option>
                    </select>
                    <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <i class="fas fa-filter mr-2"></i> Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Profile Grid View -->
        <div class="p-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {% set platformIcon = {
                    'instagram': 'fa-instagram',
                    'twitter': 'fa-twitter',
                    'facebook': 'fa-facebook',
                    'tiktok': 'fa-tiktok',
                    'youtube': 'fa-youtube',
                    'linkedin': 'fa-linkedin',
                    'pinterest': 'fa-pinterest',
                    'snapchat': 'fa-snapchat',
                    'reddit': 'fa-reddit',
                    'twitch': 'fa-twitch'
                } %}

                {% set platformColor = {
                    'instagram': 'bg-gradient-to-r from-purple-500 to-pink-500',
                    'twitter': 'bg-blue-500',
                    'facebook': 'bg-blue-600',
                    'tiktok': 'bg-black',
                    'youtube': 'bg-red-600',
                    'linkedin': 'bg-blue-700',
                    'pinterest': 'bg-red-700',
                    'snapchat': 'bg-yellow-400',
                    'reddit': 'bg-orange-600',
                    'twitch': 'bg-purple-600'
                } %}

                {% if profiles|length > 0 %}

                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-200 hover:shadow-md" data-platform="{{ platform }}">
                        <div class="h-32 {{ platformColor[platform] }} relative">
                            <div class="absolute inset-0 flex items-center justify-center opacity-20">
                                <i class="fab {{ platformIcon[platform] }} text-6xl text-white"></i>
                            </div>
                            <div class="absolute top-2 right-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-white text-gray-800">
                                    <i class="fab {{ platformIcon[platform] }} mr-1"></i>
                                    {{ platform|capitalize }}
                                </span>
                            </div>
                            <div class="absolute -bottom-10 left-4">
                                <div class="h-20 w-20 rounded-full border-4 border-white dark:border-gray-800 bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-500 dark:text-gray-400 overflow-hidden">
                                    <img src="https://randomuser.me/api/portraits/{{ i % 2 == 0 ? 'men' : 'women' }}/{{ i }}.jpg" alt="Profile" class="h-full w-full object-cover">
                                </div>
                            </div>
                        </div>

                        <div class="pt-12 pb-4 px-4">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white">User{{ i }}</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">@username{{ i }}</p>

                            <div class="mt-4 grid grid-cols-3 gap-2 text-center">
                                <div>
                                    <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ (i * 123) + 500 }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Posts</div>
                                </div>
                                <div>
                                    <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ (i * 1234) + 1000 }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Followers</div>
                                </div>
                                <div>
                                    <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ (i * 123) + 200 }}</div>
                                    <div class="text-xs text-gray-500 dark:text-gray-400">Following</div>
                                </div>
                            </div>

                            <div class="mt-4 flex justify-end">
                                <button class="p-2 rounded-md text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                <a href="#" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                    Previous
                </a>
                <a href="#" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700">
                    Next
                </a>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700 dark:text-gray-300">
                        Showing <span class="font-medium">1</span> to <span class="font-medium">12</span> of <span class="font-medium">100</span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <span class="sr-only">Previous</span>
                            <i class="fas fa-chevron-left h-5 w-5"></i>
                        </a>
                        <a href="#" aria-current="page" class="z-10 bg-primary-50 dark:bg-primary-900 border-primary-500 dark:border-primary-500 text-primary-600 dark:text-primary-300 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            1
                        </a>
                        <a href="#" class="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            2
                        </a>
                        <a href="#" class="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            3
                        </a>
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300">
                            ...
                        </span>
                        <a href="#" class="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            8
                        </a>
                        <a href="#" class="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700 relative inline-flex items-center px-4 py-2 border text-sm font-medium">
                            9
                        </a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <span class="sr-only">Next</span>
                            <i class="fas fa-chevron-right h-5 w-5"></i>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Filter functionality
            const filterPlatform = document.getElementById('filter-platform');
            const searchInput = document.getElementById('search');
            const profileCards = document.querySelectorAll('[data-platform]');

            function filterProfiles() {
                const platformFilter = filterPlatform.value;
                const searchTerm = searchInput.value.toLowerCase();

                profileCards.forEach(card => {
                    const cardPlatform = card.getAttribute('data-platform');
                    const cardText = card.textContent.toLowerCase();
                    const platformMatch = platformFilter === 'all' || cardPlatform === platformFilter;
                    const searchMatch = searchTerm === '' || cardText.includes(searchTerm);

                    if (platformMatch && searchMatch) {
                        card.classList.remove('hidden');
                    } else {
                        card.classList.add('hidden');
                    }
                });
            }

            filterPlatform.addEventListener('change', filterProfiles);
            searchInput.addEventListener('input', filterProfiles);
        });
    </script>
{% endblock %}
