{% extends 'app/modern_dashboard/base.html.twig' %}

{% block title %}Create Tracking Group | Magnora{% endblock %}
{% block page_title %}Create Tracking Group{% endblock %}

{% block content %}
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <!-- Wizard Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">Create New Tracking Group</h2>
                <div class="flex items-center space-x-2">
                    <a href="{{ path('app_modern_tracking_group_wizard_cancel') }}" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                        <i class="fas fa-times mr-1"></i> Cancel
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Progress Bar -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-750 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between mb-2">
                {% for i in 1..total_steps %}
                    <div class="flex flex-col items-center">
                        <div class="relative">
                            <div class="w-10 h-10 rounded-full flex items-center justify-center 
                                {% if wizard_data.current_step > i %}
                                    bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300
                                {% elseif wizard_data.current_step == i %}
                                    bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300 ring-4 ring-primary-50 dark:ring-primary-900/30
                                {% else %}
                                    bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300
                                {% endif %}">
                                {% if wizard_data.current_step > i %}
                                    <i class="fas fa-check"></i>
                                {% else %}
                                    {{ i }}
                                {% endif %}
                            </div>
                            
                            {% if i < total_steps %}
                                <div class="absolute top-5 left-10 w-full h-0.5 
                                    {% if wizard_data.current_step > i %}
                                        bg-green-500 dark:bg-green-700
                                    {% else %}
                                        bg-gray-300 dark:bg-gray-600
                                    {% endif %}">
                                </div>
                            {% endif %}
                        </div>
                        <span class="mt-2 text-xs font-medium 
                            {% if wizard_data.current_step == i %}
                                text-primary-700 dark:text-primary-300
                            {% else %}
                                text-gray-500 dark:text-gray-400
                            {% endif %}">
                            {% if i == 1 %}
                                Basics
                            {% elseif i == 2 %}
                                Profiles
                            {% elseif i == 3 %}
                                Options
                            {% elseif i == 4 %}
                                Review
                            {% endif %}
                        </span>
                    </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- Step Content -->
        <div class="p-6">
            {% block step_content %}{% endblock %}
        </div>
    </div>
{% endblock %}
