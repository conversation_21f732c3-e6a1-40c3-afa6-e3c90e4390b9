{% extends 'app/modern_dashboard/tracking_group/wizard/base.html.twig' %}

{% block step_content %}
    <div class="max-w-3xl mx-auto">
        <div class="mb-6">
            <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">Add Profiles to Track</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
                Enter the profiles, pages, or groups you want to track. You can enter one per line or separate them with commas.
            </p>
        </div>
        
        <form method="post" action="{{ path('app_modern_tracking_group_wizard_step', {step: 2}) }}">
            <!-- Profiles List Field -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-1">
                    <label for="list" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {% if wizard_data.data.listType == 'meta:profiles' %}
                            Profiles <span class="text-red-500">*</span>
                        {% elseif wizard_data.data.listType == 'meta:pages' %}
                            Pages <span class="text-red-500">*</span>
                        {% elseif wizard_data.data.listType == 'meta:groups' %}
                            Groups <span class="text-red-500">*</span>
                        {% endif %}
                    </label>
                    <span class="text-xs text-gray-500 dark:text-gray-400" id="profile-count">
                        0 items
                    </span>
                </div>
                
                <div class="relative">
                    <textarea id="list" name="list" rows="10" 
                        class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        placeholder="Enter usernames, one per line or separated by commas">{{ wizard_data.data.list|join('\n') }}</textarea>
                    
                    <div class="absolute top-2 right-2">
                        <button type="button" id="paste-button" class="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none" title="Paste from clipboard">
                            <i class="fas fa-paste"></i>
                        </button>
                        <button type="button" id="clear-button" class="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none" title="Clear all">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                
                <div class="mt-2 flex flex-col sm:flex-row sm:items-center gap-2">
                    <div class="flex-1">
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            Enter usernames without the @ symbol. For example: <span class="font-mono">username1, username2</span> or one per line.
                        </p>
                    </div>
                    <div>
                        <button type="button" id="format-button" class="text-xs px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <i class="fas fa-magic mr-1"></i> Format
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Example Profiles -->
            <div class="mb-6">
                <div class="bg-gray-50 dark:bg-gray-750 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                    <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Example Format</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h5 class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">Comma-separated</h5>
                            <pre class="text-xs bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-700 overflow-x-auto">username1, username2, username3</pre>
                        </div>
                        <div>
                            <h5 class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">One per line</h5>
                            <pre class="text-xs bg-white dark:bg-gray-800 p-2 rounded border border-gray-200 dark:border-gray-700 overflow-x-auto">username1
username2
username3</pre>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Bulk Import Options -->
            <div class="mb-6">
                <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                    <h4 class="text-sm font-medium text-blue-700 dark:text-blue-300 mb-2">
                        <i class="fas fa-lightbulb mr-1"></i> Pro Tip: Bulk Import
                    </h4>
                    <p class="text-xs text-blue-600 dark:text-blue-400 mb-2">
                        You can quickly import profiles from various sources:
                    </p>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-2">
                        <button type="button" class="text-xs px-3 py-2 border border-blue-300 dark:border-blue-700 rounded text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800/50 flex items-center justify-center">
                            <i class="fas fa-file-csv mr-1"></i> Import from CSV
                        </button>
                        <button type="button" class="text-xs px-3 py-2 border border-blue-300 dark:border-blue-700 rounded text-blue-700 dark:text-blue-300 hover:bg-blue-100 dark:hover:bg-blue-800/50 flex items-center justify-center">
                            <i class="fas fa-file-excel mr-1"></i> Import from Excel
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Navigation Buttons -->
            <div class="flex justify-between mt-8">
                <a href="{{ path('app_modern_tracking_group_wizard_step', {step: 1}) }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-arrow-left mr-1"></i> Back
                </a>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Continue <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
        </form>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const listTextarea = document.getElementById('list');
            const profileCount = document.getElementById('profile-count');
            const pasteButton = document.getElementById('paste-button');
            const clearButton = document.getElementById('clear-button');
            const formatButton = document.getElementById('format-button');
            
            // Update profile count
            function updateProfileCount() {
                const text = listTextarea.value.trim();
                let count = 0;
                
                if (text) {
                    if (text.includes(',')) {
                        count = text.split(',').filter(item => item.trim()).length;
                    } else {
                        count = text.split('\n').filter(item => item.trim()).length;
                    }
                }
                
                profileCount.textContent = count + (count === 1 ? ' item' : ' items');
            }
            
            // Format the list
            function formatList() {
                const text = listTextarea.value.trim();
                let items = [];
                
                if (text) {
                    if (text.includes(',')) {
                        items = text.split(',').map(item => item.trim()).filter(item => item);
                    } else {
                        items = text.split('\n').map(item => item.trim()).filter(item => item);
                    }
                    
                    // Remove @ symbols if present
                    items = items.map(item => item.startsWith('@') ? item.substring(1) : item);
                    
                    // Remove duplicates
                    items = [...new Set(items)];
                    
                    // Sort alphabetically
                    items.sort();
                    
                    // Update textarea with one item per line
                    listTextarea.value = items.join('\n');
                    updateProfileCount();
                }
            }
            
            // Paste from clipboard
            pasteButton.addEventListener('click', async function() {
                try {
                    const text = await navigator.clipboard.readText();
                    listTextarea.value = text;
                    updateProfileCount();
                } catch (err) {
                    console.error('Failed to read clipboard contents: ', err);
                }
            });
            
            // Clear the list
            clearButton.addEventListener('click', function() {
                if (confirm('Are you sure you want to clear the list?')) {
                    listTextarea.value = '';
                    updateProfileCount();
                }
            });
            
            // Format the list
            formatButton.addEventListener('click', formatList);
            
            // Update count on input
            listTextarea.addEventListener('input', updateProfileCount);
            
            // Initial count update
            updateProfileCount();
        });
    </script>
{% endblock %}
