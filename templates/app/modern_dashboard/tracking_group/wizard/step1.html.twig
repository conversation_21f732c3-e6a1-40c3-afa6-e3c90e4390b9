{% extends 'app/modern_dashboard/tracking_group/wizard/base.html.twig' %}

{% block step_content %}
    <div class="max-w-3xl mx-auto">
        <div class="mb-6">
            <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">Basic Information</h3>
            <p class="text-sm text-gray-500 dark:text-gray-400">
                Let's start with the basic details of your tracking group.
            </p>
        </div>
        
        <form method="post" action="{{ path('app_modern_tracking_group_wizard_step', {step: 1}) }}">
            <!-- Name Field -->
            <div class="mb-6">
                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Group Name <span class="text-gray-400 dark:text-gray-500">(optional)</span>
                </label>
                <input type="text" id="name" name="name" value="{{ wizard_data.data.name }}" 
                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="My Tracking Group">
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    If left empty, a name will be automatically generated based on the creation date.
                </p>
            </div>
            
            <!-- Data Source Selection -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Data Source <span class="text-red-500">*</span>
                </label>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Instagram Option -->
                    <label class="relative block cursor-pointer">
                        <input type="radio" name="dataSource" value="meta:instagram" class="sr-only peer" 
                            {{ wizard_data.data.dataSource == 'meta:instagram' ? 'checked' : '' }}>
                        <div class="p-4 border-2 rounded-lg peer-checked:border-primary-500 peer-checked:bg-primary-50 dark:peer-checked:bg-primary-900/20 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors">
                            <div class="flex items-center">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white">
                                    <i class="fab fa-instagram text-2xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-base font-medium text-gray-900 dark:text-white">Instagram</h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Track Instagram profiles, posts, and comments</p>
                                </div>
                            </div>
                            <div class="mt-3 text-xs text-gray-500 dark:text-gray-400">
                                <ul class="list-disc pl-5 space-y-1">
                                    <li>Profile information and statistics</li>
                                    <li>Posts and engagement metrics</li>
                                    <li>Comments and interactions</li>
                                </ul>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 w-5 h-5 rounded-full border border-gray-300 dark:border-gray-600 peer-checked:border-primary-500 peer-checked:bg-primary-500 flex items-center justify-center">
                            <svg class="w-3 h-3 text-white hidden peer-checked:block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </label>
                    
                    <!-- Facebook Option -->
                    <label class="relative block cursor-pointer">
                        <input type="radio" name="dataSource" value="meta:facebook" class="sr-only peer"
                            {{ wizard_data.data.dataSource == 'meta:facebook' ? 'checked' : '' }}>
                        <div class="p-4 border-2 rounded-lg peer-checked:border-primary-500 peer-checked:bg-primary-50 dark:peer-checked:bg-primary-900/20 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors">
                            <div class="flex items-center">
                                <div class="w-12 h-12 rounded-full bg-blue-600 flex items-center justify-center text-white">
                                    <i class="fab fa-facebook-f text-2xl"></i>
                                </div>
                                <div class="ml-4">
                                    <h4 class="text-base font-medium text-gray-900 dark:text-white">Facebook</h4>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">Track Facebook pages, groups, and profiles</p>
                                </div>
                            </div>
                            <div class="mt-3 text-xs text-gray-500 dark:text-gray-400">
                                <ul class="list-disc pl-5 space-y-1">
                                    <li>Page and profile information</li>
                                    <li>Posts and engagement metrics</li>
                                    <li>Comments and interactions</li>
                                </ul>
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 w-5 h-5 rounded-full border border-gray-300 dark:border-gray-600 peer-checked:border-primary-500 peer-checked:bg-primary-500 flex items-center justify-center">
                            <svg class="w-3 h-3 text-white hidden peer-checked:block" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </label>
                </div>
            </div>
            
            <!-- List Type Selection -->
            <div class="mb-6">
                <label for="listType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    List Type <span class="text-red-500">*</span>
                </label>
                <select id="listType" name="listType" 
                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="meta:profiles" {{ wizard_data.data.listType == 'meta:profiles' ? 'selected' : '' }}>Profiles</option>
                    <option value="meta:pages" {{ wizard_data.data.listType == 'meta:pages' ? 'selected' : '' }}>Pages</option>
                    <option value="meta:groups" {{ wizard_data.data.listType == 'meta:groups' ? 'selected' : '' }}>Groups</option>
                </select>
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                    Select the type of entities you want to track.
                </p>
            </div>
            
            <!-- Navigation Buttons -->
            <div class="flex justify-between mt-8">
                <a href="{{ path('app_modern_tracking_groups') }}" class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    Continue <i class="fas fa-arrow-right ml-1"></i>
                </button>
            </div>
        </form>
    </div>
{% endblock %}
