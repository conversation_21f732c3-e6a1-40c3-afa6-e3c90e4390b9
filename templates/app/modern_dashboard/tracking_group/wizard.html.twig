{% extends 'app/modern_dashboard/base.html.twig' %}

{% block title %}{{ 'tracking_group.create'|trans }} | {{ 'app.name'|trans }}{% endblock %}
{% block page_title %}{{ 'tracking_group.create'|trans }}{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ importmap(['tracking-group-wizard']) }}
{% endblock %}

{% block content %}
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <!-- <PERSON> Header -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">{{ 'tracking_group.create_new'|trans }}</h2>
                <div class="flex items-center space-x-2">
                    <a href="{{ path('app_modern_tracking_groups') }}" class="text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                        <i class="fas fa-times mr-1"></i> {{ 'common.cancel'|trans }}
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Progress Indicator -->
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    <button type="button" class="step-indicator w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium bg-primary-600 text-white border-primary-600" data-step="1">1</button>
                    <div class="h-0.5 w-8 bg-gray-200 dark:bg-gray-700"></div>
                    <button type="button" class="step-indicator w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-700 border-gray-300" data-step="2">2</button>
                    <div class="h-0.5 w-8 bg-gray-200 dark:bg-gray-700"></div>
                    <button type="button" class="step-indicator w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-700 border-gray-300" data-step="3">3</button>
                    <div class="h-0.5 w-8 bg-gray-200 dark:bg-gray-700"></div>
                    <button type="button" class="step-indicator w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium bg-gray-200 text-gray-700 border-gray-300" data-step="4">4</button>
                </div>
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    <span id="current-step">1</span> / <span id="total-steps">4</span>
                </div>
            </div>
        </div>
        
        <!-- Wizard Content -->
        <div class="wizard-content p-6">
            <!-- Step 1: Basic Information -->
            <div class="wizard-step" data-step="1">
                <div class="max-w-3xl mx-auto">
                    <div class="mb-6">
                        <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">{{ 'tracking_group.basic_info'|trans }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            {{ 'tracking_group.basic_info_desc'|trans }}
                        </p>
                    </div>
                    
                    <form class="wizard-form">
                        <!-- Name Field -->
                        <div class="mb-6">
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {{ 'tracking_group.name'|trans }} <span class="text-gray-400 dark:text-gray-500">({{ 'common.optional'|trans }})</span>
                            </label>
                            <input type="text" id="name" name="name" 
                                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                placeholder="{{ 'tracking_group.name_placeholder'|trans }}">
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                {{ 'tracking_group.name_help'|trans }}
                            </p>
                        </div>
                        
                        <!-- Data Source Field -->
                        <div class="mb-6">
                            <label for="dataSource" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {{ 'tracking_group.data_source'|trans }} <span class="text-red-500">*</span>
                            </label>
                            <select id="dataSource" name="dataSource" 
                                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="instagram">Instagram</option>
                                <option value="facebook">Facebook</option>
                                <option value="twitter">Twitter</option>
                            </select>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                {{ 'tracking_group.data_source_help'|trans }}
                            </p>
                        </div>
                        
                        <!-- List Type Field -->
                        <div class="mb-6">
                            <label for="listType" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {{ 'tracking_group.list_type'|trans }} <span class="text-red-500">*</span>
                            </label>
                            <select id="listType" name="listType" 
                                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="meta:profiles">{{ 'tracking_group.list_type_profiles'|trans }}</option>
                                <option value="meta:pages">{{ 'tracking_group.list_type_pages'|trans }}</option>
                                <option value="meta:groups">{{ 'tracking_group.list_type_groups'|trans }}</option>
                            </select>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                {{ 'tracking_group.list_type_help'|trans }}
                            </p>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Step 2: Profiles List -->
            <div class="wizard-step hidden" data-step="2">
                <div class="max-w-3xl mx-auto">
                    <div class="mb-6">
                        <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">{{ 'tracking_group.add_profiles'|trans }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            {{ 'tracking_group.add_profiles_desc'|trans }}
                        </p>
                    </div>
                    
                    <form class="wizard-form">
                        <!-- Profiles List Field -->
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-1">
                                <label for="list" class="block text-sm font-medium text-gray-700 dark:text-gray-300" id="list-label">
                                    {{ 'tracking_group.profiles'|trans }} <span class="text-red-500">*</span>
                                </label>
                                <span class="text-xs text-gray-500 dark:text-gray-400" id="profile-count">
                                    0 {{ 'tracking_group.items'|trans }}
                                </span>
                            </div>
                            <textarea id="list" name="list" rows="10" 
                                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                                placeholder="{{ 'tracking_group.profiles_placeholder'|trans }}"></textarea>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                {{ 'tracking_group.profiles_help'|trans }}
                            </p>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Step 3: Tracking Options -->
            <div class="wizard-step hidden" data-step="3">
                <div class="max-w-3xl mx-auto">
                    <div class="mb-6">
                        <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">{{ 'tracking_group.tracking_options'|trans }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            {{ 'tracking_group.tracking_options_desc'|trans }}
                        </p>
                    </div>
                    
                    <form class="wizard-form">
                        <!-- Category Field -->
                        <div class="mb-6">
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {{ 'tracking_group.category'|trans }} <span class="text-red-500">*</span>
                            </label>
                            <select id="category" name="category" 
                                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option value="general">{{ 'tracking_group.category_general'|trans }}</option>
                                <option value="business">{{ 'tracking_group.category_business'|trans }}</option>
                                <option value="politics">{{ 'tracking_group.category_politics'|trans }}</option>
                                <option value="entertainment">{{ 'tracking_group.category_entertainment'|trans }}</option>
                                <option value="sports">{{ 'tracking_group.category_sports'|trans }}</option>
                                <option value="other">{{ 'tracking_group.category_other'|trans }}</option>
                            </select>
                        </div>
                        
                        <!-- Maximum Posts Per Profile Field -->
                        <div class="mb-6">
                            <label for="maximumPostsPerProfile" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {{ 'tracking_group.max_posts'|trans }}
                            </label>
                            <input type="number" id="maximumPostsPerProfile" name="maximumPostsPerProfile" value="10" min="0" max="100" 
                                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                {{ 'tracking_group.max_posts_help'|trans }}
                            </p>
                        </div>
                        
                        <!-- Maximum Comments Per Post Field -->
                        <div class="mb-6">
                            <label for="maximumCommentsPerPost" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {{ 'tracking_group.max_comments'|trans }}
                            </label>
                            <input type="number" id="maximumCommentsPerPost" name="maximumCommentsPerPost" value="0" min="0" max="100" 
                                class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                {{ 'tracking_group.max_comments_help'|trans }}
                            </p>
                        </div>
                        
                        <!-- AI Analysis Field -->
                        <div class="mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="runProfileAIAnalysis" name="runProfileAIAnalysis" 
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                <label for="runProfileAIAnalysis" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    {{ 'tracking_group.run_ai_analysis'|trans }}
                                </label>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 ml-6">
                                {{ 'tracking_group.run_ai_analysis_help'|trans }}
                            </p>
                        </div>
                        
                        <!-- Keep Updated Field -->
                        <div class="mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" id="keepDataUpdated" name="keepDataUpdated" checked 
                                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                <label for="keepDataUpdated" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    {{ 'tracking_group.keep_updated'|trans }}
                                </label>
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400 ml-6">
                                {{ 'tracking_group.keep_updated_help'|trans }}
                            </p>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Step 4: Review & Create -->
            <div class="wizard-step hidden" data-step="4">
                <div class="max-w-3xl mx-auto">
                    <div class="mb-6">
                        <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">{{ 'tracking_group.review'|trans }}</h3>
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            {{ 'tracking_group.review_desc'|trans }}
                        </p>
                    </div>
                    
                    <!-- Summary Card -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden mb-6">
                        <!-- Basic Information Section -->
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="text-base font-medium text-gray-900 dark:text-white">{{ 'tracking_group.basic_info'|trans }}</h4>
                                <button type="button" class="edit-step text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300" data-step="1">
                                    <i class="fas fa-edit mr-1"></i> {{ 'common.edit'|trans }}
                                </button>
                            </div>
                            
                            <div class="mb-2">
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ 'tracking_group.name'|trans }}</p>
                                <p class="text-sm text-gray-900 dark:text-white" id="review-name">-</p>
                            </div>
                            
                            <div class="mb-2">
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ 'tracking_group.data_source'|trans }}</p>
                                <p class="text-sm text-gray-900 dark:text-white" id="review-data-source">-</p>
                            </div>
                            
                            <div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ 'tracking_group.list_type'|trans }}</p>
                                <p class="text-sm text-gray-900 dark:text-white" id="review-list-type">-</p>
                            </div>
                        </div>
                        
                        <!-- Profiles Section -->
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="text-base font-medium text-gray-900 dark:text-white">{{ 'tracking_group.profiles'|trans }}</h4>
                                <button type="button" class="edit-step text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300" data-step="2">
                                    <i class="fas fa-edit mr-1"></i> {{ 'common.edit'|trans }}
                                </button>
                            </div>
                            
                            <div class="mb-2">
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ 'tracking_group.total_profiles'|trans }}</p>
                                <p class="text-sm text-gray-900 dark:text-white" id="review-profile-count">0 {{ 'tracking_group.profiles'|trans }}</p>
                            </div>
                        </div>
                        
                        <!-- Tracking Options Section -->
                        <div class="px-6 py-4">
                            <div class="flex justify-between items-center mb-2">
                                <h4 class="text-base font-medium text-gray-900 dark:text-white">{{ 'tracking_group.tracking_options'|trans }}</h4>
                                <button type="button" class="edit-step text-xs text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300" data-step="3">
                                    <i class="fas fa-edit mr-1"></i> {{ 'common.edit'|trans }}
                                </button>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ 'tracking_group.category'|trans }}</p>
                                    <p class="text-sm text-gray-900 dark:text-white" id="review-category">-</p>
                                </div>
                                
                                <div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ 'tracking_group.max_posts'|trans }}</p>
                                    <p class="text-sm text-gray-900 dark:text-white" id="review-posts-per-profile">10</p>
                                </div>
                                
                                <div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ 'tracking_group.max_comments'|trans }}</p>
                                    <p class="text-sm text-gray-900 dark:text-white" id="review-comments-per-post">0</p>
                                </div>
                                
                                <div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ 'tracking_group.run_ai_analysis'|trans }}</p>
                                    <p class="text-sm text-gray-900 dark:text-white" id="review-ai-analysis">{{ 'common.no'|trans }}</p>
                                </div>
                                
                                <div>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ 'tracking_group.keep_updated'|trans }}</p>
                                    <p class="text-sm text-gray-900 dark:text-white" id="review-keep-updated">{{ 'common.yes'|trans }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Wizard Footer -->
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-between">
            <button type="button" id="prev-step" class="hidden px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-arrow-left mr-2"></i> {{ 'common.previous'|trans }}
            </button>
            <div class="ml-auto">
                <button type="button" id="next-step" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    {{ 'common.next'|trans }} <i class="fas fa-arrow-right ml-2"></i>
                </button>
                <button type="button" id="create-tracking-group" class="hidden px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <i class="fas fa-check mr-2"></i> {{ 'tracking_group.create'|trans }}
                </button>
            </div>
        </div>
    </div>
{% endblock %}
