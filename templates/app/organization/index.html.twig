{% extends 'app/dashboard/page.html.twig' %}

{% block title %}Edit Organization | Magnora{% endblock %}

{% block page_actions %}
    <div class="btn-list">
        <button type="button" class="btn btn-primary d-none d-sm-inline-block" data-bs-toggle="modal" data-bs-target="#add-tracking-group">
            <i class="fa fa-plus icon icon-2"></i> Create Organization
        </button>
    </div>
{% endblock %}

{% block page_content %}
    {% include 'app/components/modal/organization.html.twig' %}
    <div class="card">
        <div class="row g-0">
            <div class="col-12 col-md-3 border-end">
                <div class="card-body">
                    <h4 class="subheader">Settings</h4>
                    <div class="list-group list-group-transparent">
                        <a href="{{ path('app_profile') }}" class="list-group-item list-group-item-action d-flex align-items-center">My Account</a>
                        <a href="{{ path('app_organization') }}" class="list-group-item list-group-item-action d-flex align-items-center active">Organization Settings</a>
                        <a href="{{ path('app_organization_settings_custom_fields') }}" class="list-group-item list-group-item-action d-flex align-items-center">Custom Fields</a>
                        <a href="{{ path('app_organization_settings_custom_field_templates') }}" class="list-group-item list-group-item-action d-flex align-items-center">Custom Field Templates</a>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-9 d-flex flex-column">
                <div class="card">
                    {% if (is_authorized ?? false) %}
                        {{ form_start(forms.current) }}
                        <div class="card-body">
                            <h2 class="mb-4">{{ organization.name }}</h2>
                            <h3 class="card-title">Organization Details</h3>
                            {{ form_row(forms.current.name) }}
                            <div class="btn-list justify-content-end">
                                {{ form_widget(forms.current.submit) }}
                            </div>
                        </div>
                        {{ form_end(forms.current) }}
                        <hr>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-6">
                                    <h3 class="card-title">Users</h3>
                                </div>
                                <div class="col-6 d-flex justify-content-end">
                                    <a href="#" class="btn btn-primary btn-block"><i class="fa fa-plus me-2"></i> Add user</a>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-vcenter table-mobile-md card-table">
                                    <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>E-mail</th>
                                        <th>Role</th>
                                        <th>Status</th>
                                        <th class="w-1"></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {% for rel in organization.organizationUsers %}
                                        {% set user = rel.user %}
                                        <tr>
                                            <td data-label="Name">
                                                <div class="d-flex py-1 align-items-center">
                                                    <div class="flex-fill">
                                                        <div class="font-weight-medium">{{ user.name }}</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td data-label="E-mail">
                                                <div>{{ user.email }}</div>
                                            </td>
                                            <td data-label="Role">{{ rel.role|title }}</td>
                                            <td data-label="Status">{{ rel.invitationStatus == 'pending' ? 'Pending' : 'Active' }}</td>
                                            <td>
                                                {% if user.id != app.user.id %}
                                                    <div class="btn-list flex-nowrap">
                                                        <div class="dropdown">
                                                            <button class="btn dropdown-toggle align-text-top" data-bs-toggle="dropdown" aria-expanded="false">Actions</button>
                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                {% if rel.invitationStatus == 'pending' %}
                                                                    <a class="dropdown-item" href="#">Cancel Invitation</a>
                                                                {% else %}
                                                                    <a class="dropdown-item" href="#">Edit Privilege</a>
                                                                    <a class="dropdown-item" href="#">Remove</a>
                                                                {% endif %}
                                                            </div>
                                                        </div>
                                                    </div>
                                                {% else %}
                                                    <div class="text-secondary">You</div>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="card-body d-flex justify-content-center align-items-center">
                        <p class="text-center">Nothing here.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}
