{% extends 'app/dashboard/page.html.twig' %}

{% block title %}Custom Fields | Magnora{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .collection-content > div {
            display: flex;
        }
    </style>
{% endblock %}

{% block page_content %}
    <div class="card">
        <div class="row g-0">
            <div class="col-12 col-md-3 border-end">
                <div class="card-body">
                    <h4 class="subheader">Settings</h4>
                    <div class="list-group list-group-transparent">
                        <a href="{{ path('app_profile') }}" class="list-group-item list-group-item-action d-flex align-items-center">My Account</a>
                        <a href="{{ path('app_organization') }}" class="list-group-item list-group-item-action d-flex align-items-center">Organization Settings</a>
                        <a href="{{ path('app_organization_settings_custom_fields') }}" class="list-group-item list-group-item-action d-flex align-items-center active">Custom Fields</a>
                        <a href="{{ path('app_organization_settings_custom_field_templates') }}" class="list-group-item list-group-item-action d-flex align-items-center">Custom Field Templates</a>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-9 d-flex flex-column">
                {{ form_start(form) }}
                <div class="card-body">
                    <h2 class="mb-4">Custom Fields</h2>
                    <p>Those fields values can only be edited here and <b style="text-decoration: underline;">changes are applied globally</b> to the organization. If you are looking for fields that can be edited individually later in each entity, please refer to Custom Field Templates.</p>
                    {{ form_row(form.fields) }}
                </div>
                <div class="card-footer bg-transparent mt-auto">
                    <div class="btn-list justify-content-end">
                        {{ form_widget(form.submit) }}
                    </div>
                </div>
                {{ form_end(form) }}
            </div>
        </div>
    </div>
{% endblock %}
