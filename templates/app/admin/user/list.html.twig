{% extends 'app/dashboard/page.html.twig' %}

{% block title %}User List | Magnora Admin{% endblock %}
{% block page_actions %}{% endblock %}

{% block page_content %}
    <div class="table-responsive">
        <table class="table table-vcenter card-table table-striped">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                    <tr>
                        <td>{{ user.name }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            {{ component('UserToggle', { user: user }) }}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% endblock %}