{% extends 'app/dashboard/page.html.twig' %}

{% set profile = configuration.profile %}
{% set profile_picture = profile.data.profile_picture_url ?? profile.data.picture.data.url ?? '#' %}

{% block title %}Profile Configuration | Magnora{% endblock %}
{% block page_actions %}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .collection-content > div {
            display: flex;
        }

        .org-alert h4 span {
            color: #4299e1;
            background: white;
            padding: 0 0.5rem;
        }
    </style>
{% endblock %}

{% block page_content %}
    <div class="alert alert-important alert-info alert-dismissible org-alert d-flex" role="alert">
        <div class="alert-icon d-grid align-items-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon alert-icon icon-2">
                <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>
                <path d="M12 9h.01"></path>
                <path d="M11 12h1v4h1"></path>
            </svg>
        </div>
        <div>
            <h4 class="alert-heading mb-0">This configuration will apply to the following organization: <span>{{ configuration.organization.name }}</span>.</h4>
        </div>
        <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
    </div>
    <div class="card">
        <div class="row row-0">
            <div class="col-3 col-md-2">
                <img src="{{ profile_picture }}" class="w-100 h-100 object-cover card-img-start" alt="{{ profile.username }}">
            </div>
            <div class="col">
                <div class="card-body">
                    <h3 class="card-title">{{ profile.username }}</h3>
                    <p class="text-secondary">{{ profile.data.biography ?? null }}</p>
                </div>
            </div>
        </div>
    </div>
    {{ form(form) }}
{% endblock %}
