{% extends 'app/dashboard/page.html.twig' %}

{% block title %}Profiles | Magnora{% endblock %}

{% block stylesheets %}
    {{ parent() }}
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ importmap('profiles') }}
{% endblock %}

{% block page_actions %}
    <div class="btn-list">
        <button type="button" class="btn btn-primary d-none d-sm-inline-block" id="toggleMultiSelect">
            <i class="fa fa-check-square me-2"></i> Multi-Select
        </button>
        <button type="button" class="btn btn-outline-primary d-none d-sm-inline-block" data-bs-toggle="collapse" data-bs-target="#filter-section" aria-expanded="false" aria-controls="filter-section">
            <i class="fa fa-filter me-2"></i> Filters
            {% if app.request.query.get('search') or app.request.query.get('platform') or app.request.query.get('tracking_group') %}
                <span class="badge bg-green-lt ms-2">Active</span>
            {% endif %}
        </button>
    </div>
{% endblock %}

{% block page_content %}
    {# Multi-select controls - hidden by default #}
    <div id="multi-select-controls" class="card mb-3 d-none">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="form-check me-3">
                        <input type="checkbox" id="selectAllProfiles" class="form-check-input">
                        <label class="form-check-label" for="selectAllProfiles">Select All</label>
                    </div>
                    <span class="text-muted"><span id="selected-count">0</span> profiles selected</span>
                </div>
                <div class="btn-list">
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle multi-select-action disabled" data-bs-toggle="dropdown">
                            Actions
                        </button>
                        <div class="dropdown-menu dropdown-menu-end">
                            <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#m-cf-cf">
                                <i class="fa fa-gear me-2"></i> Configure Custom Fields
                            </a>
                            <a class="dropdown-item" href="#">
                                <i class="fa fa-tag me-2"></i> Add to Tracking Group
                            </a>
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-secondary" id="cancelMultiSelect">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    {# Filter section - collapsible #}
    <div class="collapse mb-4" id="filter-section">
        <div class="card">
            <div class="card-body">
                <form method="get" action="{{ path('app_meta_profile_list') }}" class="row g-3" id="profile-filter-form">
                    <div class="col-md-4">
                        <label class="form-label">Search</label>
                        <div class="input-icon">
                            <input type="text" class="form-control" placeholder="Search profiles…" name="search" value="{{ app.request.query.get('search') }}">
                            <span class="input-icon-addon">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                    <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                                    <path d="M21 21l-6 -6"></path>
                                </svg>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Platform</label>
                        <select class="form-select" name="platform">
                            <option value="">All Platforms</option>
                            <option value="meta:instagram" {{ app.request.query.get('platform') == 'meta:instagram' ? 'selected' : '' }}>Instagram</option>
                            <option value="meta:facebook" {{ app.request.query.get('platform') == 'meta:facebook' ? 'selected' : '' }}>Facebook</option>
                            <option value="meta:threads" {{ app.request.query.get('platform') == 'meta:threads' ? 'selected' : '' }}>Threads</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Tracking Group</label>
                        <select class="form-select" name="tracking_group">
                            <option value="">All Tracking Groups</option>
                            {% for group in tracking_groups|default([]) %}
                                <option value="{{ group.id }}" {{ app.request.query.get('tracking_group') == group.id ? 'selected' : '' }}>{{ group.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <div class="btn-list w-100">
                            <button type="submit" class="btn btn-primary w-100">Apply</button>
                            <button type="button" class="btn btn-outline-secondary w-100" id="clear-filters">Clear</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    {# Profile Grid #}
    <div class="row row-cards profile-grid">
        {% if profiles|length > 0 %}
            {% for profile in profiles %}
                <div class="col-sm-6 col-lg-4 col-xl-3 mb-3">
                    <div class="card profile-card">
                        {# Card Header with Cover Photo and Platform Badge #}
                        <div class="card-header position-relative p-0">
                            {% set has_cover = ((profile.data.cover.source ?? null) is not null) %}
                            {% set profile_picture = profile.data.profile_picture_url ?? profile.data.picture.data.url ?? '#' %}

                            {% if has_cover %}
                                <div class="card-cover" style="background-image: url({{ profile.data.cover.source ?? '#' }})"></div>
                            {% endif %}

                            {# Platform Badge #}
                            {% set platform_name = profile.platform|replace({'meta:': ''}) %}
                            {% set badge_class = platform_name == 'instagram' ? 'badge-instagram' : (platform_name == 'facebook' ? 'badge-facebook' : 'badge-threads') %}
                            <span class="badge position-absolute top-2 end-2 {{ badge_class }}" style="z-index: 5;">
                                {% if platform_name == 'instagram' %}
                                    <i class="fa fa-instagram me-1"></i>
                                {% elseif platform_name == 'facebook' %}
                                    <i class="fa fa-facebook me-1"></i>
                                {% elseif platform_name == 'threads' %}
                                    <i class="fa fa-hashtag me-1"></i>
                                {% endif %}
                                {{ platform_name|capitalize }}
                            </span>

                            {# Checkbox for Multi-select - Hidden by Default #}
                            <div class="form-check position-absolute top-2 start-2 d-none">
                                <input type="checkbox" class="form-check-input profile-checkbox" name="form_selectedProfiles[]" value="{{ profile.id }}">
                            </div>

                            {# Profile Avatar #}
                            <div class="avatar-container d-flex justify-content-center" style="{% if has_cover %}margin-top: -40px;{% else %}padding-top: 20px;{% endif %}">
                                <span class="avatar avatar-xl rounded" style="background-image: url({{ profile_picture }}); border: 3px solid #fff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);"></span>
                            </div>
                        </div>

                        {# Card Body with Profile Info #}
                        <div class="card-body text-center pt-2">
                            <h3 class="card-title mb-1">{{ profile.data.username ?? profile.data.name ?? profile.metaId }}</h3>
                            <div class="text-secondary mb-3">
                                {% if profile.data.name and profile.data.name != profile.data.username %}
                                    {{ profile.data.name }}
                                {% else %}
                                    @{{ profile.username }}
                                {% endif %}
                            </div>

                            {# Profile Stats #}
                            <div class="profile-stats mt-3 p-0 bg-transparent shadow-none">
                                <div class="row text-center g-0">
                                    {% if profile.data.media_count is defined %}
                                        <div class="col-4">
                                            <div class="h5 m-0 fw-bold">
                                                {% set media_count = profile.data.media_count %}
                                                {% if media_count is iterable %}{{ media_count|first }}{% else %}{{ media_count }}{% endif %}
                                            </div>
                                            <div class="text-secondary small">posts</div>
                                        </div>
                                    {% endif %}

                                    <div class="col-{% if profile.data.media_count is defined and profile.data.follows_count is defined %}4{% elseif profile.data.media_count is defined or profile.data.follows_count is defined %}6{% else %}12{% endif %}">
                                        <div class="h5 m-0 fw-bold">
                                            {% set followers = profile.data.followers_count ?? profile.data.fan_count ?? '?' %}
                                            {% if followers is iterable %}{{ followers|first }}{% else %}{{ followers }}{% endif %}
                                        </div>
                                        <div class="text-secondary small">followers</div>
                                    </div>

                                    {% if profile.data.follows_count is defined %}
                                        <div class="col-4">
                                            <div class="h5 m-0 fw-bold">
                                                {% set following = profile.data.follows_count %}
                                                {% if following is iterable %}{{ following|first }}{% else %}{{ following }}{% endif %}
                                            </div>
                                            <div class="text-secondary small">following</div>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        {# Card Footer with Actions #}
                        <div class="card-footer d-flex justify-content-between align-items-center">
                            <div class="text-muted small">
                                <i class="fa fa-calendar me-1"></i> {{ profile.createdAt|date('M d, Y') }}
                            </div>
                            <a href="{{ path('app_meta_profile_view', { id: profile.id }) }}" class="btn btn-sm btn-primary">
                                <i class="fa fa-eye me-1"></i> View
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="empty">
                    <div class="empty-img">
                        <img src="{{ asset('images/cat.webp') }}" height="128" alt="No profiles found">
                    </div>
                    <p class="empty-title">No Profiles Found</p>
                    <p class="empty-subtitle text-secondary">
                        {% if app.request.query.get('search') or app.request.query.get('platform') or app.request.query.get('tracking_group') %}
                            No profiles match your current filters.
                        {% else %}
                            No profiles have been added yet.
                        {% endif %}
                    </p>
                    {% if app.request.query.get('search') or app.request.query.get('platform') or app.request.query.get('tracking_group') %}
                        <div class="empty-action">
                            <a href="{{ path('app_meta_profile_list') }}" class="btn btn-primary">
                                <i class="fa fa-times me-1"></i> Clear Filters
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>

    {# Custom Fields Modal #}
    {% embed '@Tabler/embeds/modal.html.twig' with { id: 'm-cf-cf' } %}
        {% block modal_title 'Configure Custom Fields' %}
        {% block modal_size 'modal_lg' %}
        {% block modal_body %}
            <div class="modal-body">
                <div class="alert alert-info mb-3">
                    <h4 class="alert-title">Custom Fields Configuration</h4>
                    <div class="text-secondary">
                        Configure custom fields for the selected profiles. These fields will be applied to all selected profiles.
                    </div>
                </div>

                <div class="mb-3">
                    <label for="cf-in-add-cft" class="form-label">Add Custom Field Templates (Dynamic)</label>
                    <select id="cf-in-add-cft" class="form-control form-select">
                        <option value="" selected>Select a Custom Field Template</option>
                        {% for cf in data.availableCustomFieldTemplates|default([]) %}
                            <option data-template="{{ cf.getJsonTemplate }}" value="{{ cf.id }}">{{ cf.key }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <div id="custom-field-templates-container"></div>
                </div>
                <hr>
                <div class="mb-3">
                    <label for="cf-in-add-ocf" class="form-label">Add Org. Custom Field (Static)</label>
                    <select id="cf-in-add-ocf" class="form-control form-select">
                        <option value="" selected>Select an Org. Custom Field</option>
                        {% for cf in data.availableCustomFields|default([]) %}
                            <option data-template="{{ cf.getJsonTemplate }}" value="{{ cf.id }}">{{ cf.key }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <div id="org-custom-field-container"></div>
                </div>
            </div>
        {% endblock %}
        {% block modal_footer %}
            <button type="button" class="btn btn-link link-secondary" data-bs-dismiss="modal">Cancel</button>
            <button type="button" id="f-cf-cf" class="btn btn-primary ms-auto">
                <i class="fa fa-save me-2"></i> Save Custom Fields
            </button>
        {% endblock %}
    {% endembed %}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let iCustomFieldTemplateSelector = document.getElementById('cf-in-add-cft');
            let iOrganizationCustomFieldSelector = document.getElementById('cf-in-add-ocf');

            let cCustomFieldTemplates = document.getElementById('custom-field-templates-container');
            let cOrganizationCustomFields = document.getElementById('org-custom-field-container');

            iCustomFieldTemplateSelector.addEventListener('change', function(e) {
                let _id = e.target.value;
                let _op = e.target.querySelector('option[value="' + _id + '"]');
                let _tp = JSON.parse(_op.getAttribute('data-template'));

                if (_tp) {
                    let _el = document.createElement('div');
                    _el.innerHTML = `<div class="row mb-2 data-template-row" data-template="${_id}">
                        <div class="col-5">
                            <input type="text" name="form_cft_key[]" value="${_tp.key}" class="form-control">
                        </div>
                        <div class="col-5">
                            <input type="text" name="form_cft_value[]" value="${_tp.value ?? ''}" class="form-control">
                        </div>
                        <div class="col-2 d-flex justify-content-end">
                            <button type="button" class="btn btn-list-tpl-item-remove btn-danger h-100" data-remove="${_id}" data-template="${JSON.stringify(_tp).replace(/"/g, '&quot;')}"><i class="fa fa-trash" style="pointer-events: none;"></i></button>
                        </div>
                    </div>`;

                    cCustomFieldTemplates.appendChild(_el);

                    _op.remove();
                    iCustomFieldTemplateSelector.value = '';
                }
            });

            iOrganizationCustomFieldSelector.addEventListener('change', function(e) {
                let _id = e.target.value;
                let _op = e.target.querySelector('option[value="' + _id + '"]');
                let _tp = JSON.parse(_op.getAttribute('data-template'));

                if (_tp) {
                    let _el = document.createElement('div');
                    _el.innerHTML = `<div class="row mb-2" data-ocf="${_id}">
                        <input type="hidden" name="form_ocf[]" value="${_id}">
                        <div class="col-5">
                            <input type="text" value="${_tp.key}" class="form-control" readonly disabled>
                        </div>
                        <div class="col-5">
                            <input type="text" value="${_tp.value}" class="form-control" readonly disabled>
                        </div>
                        <div class="col-2 d-flex justify-content-end">
                            <button type="button" class="btn btn-list-ocf-item-remove btn-danger h-100" data-remove="${_id}" data-template="${JSON.stringify(_tp).replace(/"/g, '&quot;')}"><i class="fa fa-trash" style="pointer-events: none;"></i></button>
                        </div>
                    </div>`;

                    cOrganizationCustomFields.appendChild(_el);

                    _op.remove();
                    iOrganizationCustomFieldSelector.value = '';
                }
            });

            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('btn-list-tpl-item-remove')) {
                    let removeId = e.target.getAttribute('data-remove');
                    let tpl = JSON.parse(e.target.getAttribute('data-template'));

                    let _o = document.createElement('option');
                    _o.value = removeId;
                    _o.dataset.template = JSON.stringify(tpl);
                    _o.innerHTML = tpl.key;
                    document.getElementById('cf-in-add-cft').appendChild(_o);

                    document.querySelector(`.data-template-row[data-template="${removeId}"]`).remove();
                }

                if (e.target.classList.contains('btn-list-ocf-item-remove')) {
                    let removeId = e.target.getAttribute('data-remove');
                    let tpl = JSON.parse(e.target.getAttribute('data-template'));

                    let _o = document.createElement('option');
                    _o.value = removeId;
                    _o.dataset.template = JSON.stringify(tpl);
                    _o.innerHTML = tpl.key;
                    document.getElementById('cf-in-add-ocf').appendChild(_o);

                    document.querySelector(`[data-ocf="${removeId}"]`).remove();
                }

                if (e.target.matches('#f-cf-cf')) {
                    submitAsForm();
                }
            });

            function submitAsForm({
                  inputsSelector = 'input, select, textarea',
                  formAction = window.location.href,
                  formMethod = 'POST',
                  extraFields = {}, // Optional key-value pairs
            } = {}) {
                const form = document.createElement('form');
                form.method = formMethod;
                form.action = formAction;
                form.style.display = 'none';

                // Collect all matching inputs
                const inputs = document.querySelectorAll(inputsSelector);

                inputs.forEach(input => {
                    const { name, type, value, checked } = input;

                    if (!name) return; // Skip inputs without a name

                    // Handle checkboxes and radios
                    if ((type === 'checkbox' || type === 'radio') && !checked) return;

                    const hidden = document.createElement('input');
                    hidden.type = 'hidden';
                    hidden.name = name;
                    hidden.value = value;

                    form.appendChild(hidden);
                });

                // Add any extra fields manually
                for (const [key, val] of Object.entries(extraFields)) {
                    const hidden = document.createElement('input');
                    hidden.type = 'hidden';
                    hidden.name = key;
                    hidden.value = val;
                    form.appendChild(hidden);
                }

                document.body.appendChild(form);
                form.submit();
            }
        });
    </script>
{% endblock %}
