{% extends 'app/dashboard/page.html.twig' %}

{% set appliedFiltersCount = (app.request.query.get('group-id') is not empty ? 1 : 0) + app.request.query.all('filters')|length %}

{% block title %}Profiles Configuration | Magnora{% endblock %}
{% block page_actions %}
    <form method="get" class="d-flex w-100">
        <div class="btn-list me-2">
            <button type="button" class="btn btn-primary d-none d-sm-inline-block" data-bs-toggle="modal" data-bs-target="#add-filters">
                <i class="fa fa-filter icon icon-2"></i> Filters {% if appliedFiltersCount > 0 %}<span class="badge bg-green-lt ms-2">{{ appliedFiltersCount }}</span>{% endif %}
            </button>
        </div>

        {% embed '@Tabler/embeds/modal.html.twig' with { id: 'add-filters' } %}
            {% block modal_title 'Add Filters' %}
            {% block modal_size 'modal_sm' %}
            {% block modal_body %}
                <h4>Static Filters</h4>
                <select name="group-id" class="form-control form-select mb-2">
                    <option value="">Filter by Tracking Group</option>
                    {% for tg in tracking_groups %}
                        <option value="{{ tg.id }}" {% if app.request.get('group-id') == tg.id %}selected{% endif %}>{{ tg.name }}</option>
                    {% endfor %}
                </select>
                <h4>Dynamic Filters</h4>
                <div id="filter-container">
                    {# Filters will be inserted here #}
                </div>
                <button type="button" class="btn btn-sm btn-success" onclick="addFilter()">+ Add Filter</button>

                <script>
                    let filterIndex = 0;

                    function addFilter(key = '', operator = 'equals', value = '') {
                        const container = document.getElementById('filter-container');
                        const group = document.createElement('div');
                        group.className = 'mb-2 input-group';
                        const inputId = `filters_${filterIndex}`;

                        group.innerHTML = `
                            <input type="text" name="filters[${filterIndex}][key]" class="form-control" placeholder="Field (e.g. gender)" value="${key}">
                            <select name="filters[${filterIndex}][operator]" class="form-select" onchange="toggleValueInput(this)">
                                <option value="equals" ${operator === 'equals' ? 'selected' : ''}>equals</option>
                                <option value="not_equals" ${operator === 'not_equals' ? 'selected' : ''}>not equals</option>
                                <option value="is_empty" ${operator === 'is_empty' ? 'selected' : ''}>is empty</option>
                                <option value="is_not_empty" ${operator === 'is_not_empty' ? 'selected' : ''}>is not empty</option>
                            </select>
                            <input type="text" name="filters[${filterIndex}][value]" class="form-control ${operator.includes('empty') ? 'd-none' : ''}" placeholder="Value" value="${value}" ${operator.includes('empty') ? 'disabled' : ''}>
                            <button type="button" class="btn btn-danger" onclick="this.parentElement.remove()">&times;</button>
                        `;
                        container.appendChild(group);
                        filterIndex++;
                    }

                    function toggleValueInput(selectElement) {
                        const valueInput = selectElement.parentElement.querySelector('input[name*="[value]"]');
                        const operator = selectElement.value;

                        if (operator === 'is_empty' || operator === 'is_not_empty') {
                            valueInput.disabled = true;
                            valueInput.value = '';
                            valueInput.classList.add('d-none');
                        } else {
                            valueInput.disabled = false;
                            valueInput.classList.remove('d-none');
                        }
                    }

                    // Load filters from query params
                    window.addEventListener('DOMContentLoaded', () => {
                        const params = new URLSearchParams(window.location.search);
                        const filters = {};

                        for (const [key, value] of params.entries()) {
                            const match = key.match(/^filters\[(\d+)]\[(key|operator|value)]$/);
                            if (match) {
                                const index = match[1];
                                const type = match[2];
                                filters[index] = filters[index] || {};
                                filters[index][type] = value;
                            }
                        }

                        Object.values(filters).forEach(f => {
                            addFilter(f.key || '', f.operator || 'equals', f.value || '');
                        });
                    });
                </script>
            {% endblock %}
            {% block modal_footer %}
                <a href="#" class="btn btn-link link-secondary btn-3" data-bs-dismiss="modal">Cancel</a>
                <button type="submit" id="apply-filters" class="btn btn-primary btn-5 ms-auto">
                    <i class="fa fa-filter icon icon-2"></i> Apply Filters
                </button>
            {% endblock %}
        {% endembed %}

        <div class="input-icon me-1" style="min-width: 200px;">
            <span class="input-icon-addon">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-1">
                <path d="M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"></path>
                <path d="M21 21l-6 -6"></path>
              </svg>
            </span>
            <input type="text" name="q" value="{{ app.request.get('q') }}" class="form-control" placeholder="Search…" aria-label="Search">
        </div>
        <button type="submit" class="btn btn-primary">Apply</button>
    </form>
{% endblock %}

{% block page_content_before %}
    {% embed '@Tabler/embeds/modal.html.twig' with { id: 'm-cf-cf' } %}
        {% block modal_title 'Configure Custom Fields in Bulk' %}
        {% block modal_size 'modal_sm' %}
        {% block modal_body %}
            <div class="modal-body">
                <div class="mb-3">
                    <label for="cf-in-add-cft" class="form-label">Add Custom Field Templates (Dynamic)</label>
                    <select id="cf-in-add-cft" class="form-control form-select">
                        <option value="" selected>Select a Custom Field Template</option>
                        {% for cf in data.availableCustomFieldTemplates %}
                            <option data-template="{{ cf.getJsonTemplate }}" value="{{ cf.id }}" >{{ cf.key }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <div id="custom-field-templates-container"></div>
                </div>
                <hr>
                <div class="mb-3">
                    <label for="cf-in-add-ocf" class="form-label">Add Org. Custom Field (Static)</label>
                    <select id="cf-in-add-ocf" class="form-control form-select">
                        <option value="" selected>Select an Org. Custom Field</option>
                        {% for cf in data.availableCustomFields %}
                            <option data-template="{{ cf.getJsonTemplate }}" value="{{ cf.id }}">{{ cf.key }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <div id="org-custom-field-container"></div>
                </div>
            </div>
        {% endblock %}
        {% block modal_footer %}
            <a href="#" class="btn btn-link link-secondary btn-3" data-bs-dismiss="modal">Cancel</a>
            <button type="button" id="f-cf-cf" class="btn btn-primary btn-5 ms-auto">
                <i class="fa fa-gears icon icon-2"></i> Configure
            </button>
        {% endblock %}
    {% endembed %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            let iCustomFieldTemplateSelector = document.getElementById('cf-in-add-cft');
            let iOrganizationCustomFieldSelector = document.getElementById('cf-in-add-ocf');

            let cCustomFieldTemplates = document.getElementById('custom-field-templates-container');
            let cOrganizationCustomFields = document.getElementById('org-custom-field-container');

            iCustomFieldTemplateSelector.addEventListener('change', function(e) {
                let _id = e.target.value;
                let _op = e.target.querySelector('option[value="' + _id + '"]');
                let _tp = JSON.parse(_op.getAttribute('data-template'));

                if (_tp) {
                    let _el = document.createElement('div');
                    _el.innerHTML = `<div class="row mb-2 data-template-row" data-template="${_id}">
                        <div class="col-5">
                            <input type="text" name="form_cft_key[]" value="${_tp.key}" class="form-control">
                        </div>
                        <div class="col-5">
                            <input type="text" name="form_cft_value[]" value="${_tp.value ?? ''}" class="form-control">
                        </div>
                        <div class="col-2 d-flex justify-content-end">
                            <button type="button" class="btn btn-list-tpl-item-remove btn-danger h-100" data-remove="${_id}" data-template="${JSON.stringify(_tp).replace(/"/g, '&quot;')}"><i class="fa fa-trash" style="pointer-events: none;"></i></button>
                        </div>
                    </div>`;

                    cCustomFieldTemplates.appendChild(_el);

                    _op.remove();
                    iCustomFieldTemplateSelector.value = '';
                }
            });

            iOrganizationCustomFieldSelector.addEventListener('change', function(e) {
                let _id = e.target.value;
                let _op = e.target.querySelector('option[value="' + _id + '"]');
                let _tp = JSON.parse(_op.getAttribute('data-template'));

                if (_tp) {
                    let _el = document.createElement('div');
                    _el.innerHTML = `<div class="row mb-2" data-ocf="${_id}">
                        <input type="hidden" name="form_ocf[]" value="${_id}">
                        <div class="col-5">
                            <input type="text" value="${_tp.key}" class="form-control" readonly disabled>
                        </div>
                        <div class="col-5">
                            <input type="text" value="${_tp.value}" class="form-control" readonly disabled>
                        </div>
                        <div class="col-2 d-flex justify-content-end">
                            <button type="button" class="btn btn-list-ocf-item-remove btn-danger h-100" data-remove="${_id}" data-template="${JSON.stringify(_tp).replace(/"/g, '&quot;')}"><i class="fa fa-trash" style="pointer-events: none;"></i></button>
                        </div>
                    </div>`;

                    cOrganizationCustomFields.appendChild(_el);

                    _op.remove();
                    iOrganizationCustomFieldSelector.value = '';
                }
            });

            const selectAll = document.getElementById('selectAllProfiles');
            document.getElementById('selectAllProfiles').addEventListener('change', function(e) {
                const checkboxes = document.querySelectorAll('.profile-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = e.target.checked;
                });
            });

            const checkboxes = document.querySelectorAll('.profile-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    const checkedCount = document.querySelectorAll('.profile-checkbox:checked').length;
                    if (checkedCount === 0) {
                        selectAll.checked = false;
                        selectAll.indeterminate = false;
                    } else if (checkedCount === checkboxes.length) {
                        selectAll.checked = true;
                        selectAll.indeterminate = false;
                    } else {
                        selectAll.indeterminate = true;
                    }
                });
            });
        });

        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-list-tpl-item-remove')) {
                let removeId = e.target.getAttribute('data-remove');
                let tpl = JSON.parse(e.target.getAttribute('data-template'));

                let _o = document.createElement('option');
                _o.value = removeId;
                _o.dataset.template = JSON.stringify(tpl);
                _o.innerHTML = tpl.key;
                document.getElementById('cf-in-add-cft').appendChild(_o);

                document.querySelector(`.data-template-row[data-template="${removeId}"]`).remove();
            }

            if (e.target.classList.contains('btn-list-ocf-item-remove')) {
                let removeId = e.target.getAttribute('data-remove');
                let tpl = JSON.parse(e.target.getAttribute('data-template'));

                let _o = document.createElement('option');
                _o.value = removeId;
                _o.dataset.template = JSON.stringify(tpl);
                _o.innerHTML = tpl.key;
                document.getElementById('cf-in-add-ocf').appendChild(_o);

                document.querySelector(`[data-ocf="${removeId}"]`).remove();
            }

            if (e.target.matches('#f-cf-cf')) {
                submitAsForm();
            }
        });

        function submitAsForm({
              inputsSelector = 'input, select, textarea',
              formAction = window.location.href,
              formMethod = 'POST',
              extraFields = {}, // Optional key-value pairs
        } = {}) {
            const form = document.createElement('form');
            form.method = formMethod;
            form.action = formAction;
            form.style.display = 'none';

            // Collect all matching inputs
            const inputs = document.querySelectorAll(inputsSelector);

            inputs.forEach(input => {
                const { name, type, value, checked } = input;

                if (!name) return; // Skip inputs without a name

                // Handle checkboxes and radios
                if ((type === 'checkbox' || type === 'radio') && !checked) return;

                const hidden = document.createElement('input');
                hidden.type = 'hidden';
                hidden.name = name;
                hidden.value = value;

                form.appendChild(hidden);
            });

            // Add any extra fields manually
            for (const [key, val] of Object.entries(extraFields)) {
                const hidden = document.createElement('input');
                hidden.type = 'hidden';
                hidden.name = key;
                hidden.value = val;
                form.appendChild(hidden);
            }

            document.body.appendChild(form);
            form.submit();
        }
    </script>

    <div class="d-flex content w-100 justify-content-around">
        <div class="col-4 col-md-6 col-lg-9 align-content-center">
            <div class="form-check">
                <input type="checkbox" id="selectAllProfiles" class="form-check-input">
                <label class="form-check-label" for="selectAllProfiles">Select All</label>
            </div>
        </div>
        <div class="col-8 col-md-6 col-lg-3 d-flex justify-content-end">
            <div class="btn-list flex-nowrap">
                <div class="dropdown">
                    <button class="btn dropdown-toggle align-text-top" data-bs-toggle="dropdown" aria-expanded="false">Actions</button>
                    <div class="dropdown-menu dropdown-menu-end" data-popper-placement="bottom-end">
                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#m-cf-cf">Configure Custom Fields</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block page_content %}
    <div class="row row-cards">
        {% for profile in profiles %}
            <div class="col-md-6 col-xl-3 h-100">
                <label class="form-imagecheck mb-2 w-100">
                    <input name="form_selectedProfiles[]" type="checkbox" value="{{ profile.id }}" class="form-imagecheck-input profile-checkbox">
                    <span class="form-imagecheck-figure">
                        <div class="card">
                            {% set has_cover = ((profile.data.cover.source ?? null) is not null) %}
                            {% set profile_picture = profile.data.profile_picture_url ?? profile.data.picture.data.url ?? '#' %}

                            {% if has_cover %}
                                <div class="card-cover card-cover-blurred text-center" style="background-image: url({{ profile.data.cover.source ?? '#' }})">
                                    <span class="avatar avatar-xl avatar-thumb rounded" style="background-image: url({{ profile_picture }})"></span>
                                </div>
                            {% endif %}
                            <div class="card-body text-center">
                                {% if has_cover == false %}
                                    <div class="mb-3">
                                        <span class="avatar avatar-xl rounded" style="background-image: url({{ profile_picture }})"></span>
                                    </div>
                                {% endif %}
                                <div class="card-title mb-1">{{ profile.data.username ?? profile.data.name ?? profile.metaId }}</div>
                                <div class="text-secondary">{{ profile.platform }}</div>
                            </div>
                            <a href="{{ path('app_meta_profile_view', { id: profile.id }) }}" class="card-btn"><i class="fa fa-eye me-1"></i> View</a>
                        </div>
                    </span>
                </label>
            </div>
        {% endfor %}
    </div>
{% endblock %}
