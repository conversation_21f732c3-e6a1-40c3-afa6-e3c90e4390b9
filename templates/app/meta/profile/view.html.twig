{% extends 'app/dashboard/page.html.twig' %}

{% set profile_picture = profile.data.profile_picture_url ?? profile.data.picture.data.url ?? '#' %}

{% block title %}{{ profile.data.name ?? profile.data.username ?? profile.metaId }} | Magnora{% endblock %}
{% block page_actions %}{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .collection-content > div {
            display: flex;
        }

        .org-alert h4 span {
            color: #4299e1;
            background: white;
            padding: 0 0.5rem;
        }
    </style>
{% endblock %}

{% block page_content %}
    {% embed '@Tabler/embeds/modal.html.twig' with { id: 'updateModal' } %}
        {% block modal_title 'Updating Profile' %}
        {% block modal_size 'modal_sm' %}
        {% block modal_body %}
            <h4>Profile Update Progress</h4>
            <div class="progress mb-2">
                <div id="progress-bar" class="progress-bar progress-bar-animated progress-bar-indeterminate bg-green" role="progressbar" style="width: 0"></div>
            </div>
            <div class="d-flex justify-content-between">
                <p class="status-text">Waiting to start... keep this open.</p>
                <p class="progress-text">0%</p>
            </div>
        {% endblock %}
        {% block modal_footer %}
            <a href="{{ path('app_meta_profile_view', { id: profile.id }) }}" class="btn btn-success btn-done btn-3 disabled">Done</a>
        {% endblock %}
    {% endembed %}

    {% embed '@Tabler/embeds/modal.html.twig' with { id: 'configureCustomFields' } %}
        {% block modal_title 'Configure Custom Fields' %}
        {% block modal_size 'modal_sm' %}
        {% block modal_body %}
            <div class="alert alert-important alert-info alert-dismissible org-alert d-flex mb-3" role="alert">
                <div class="alert-icon d-grid align-items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon alert-icon icon-2">
                        <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0"></path>
                        <path d="M12 9h.01"></path>
                        <path d="M11 12h1v4h1"></path>
                    </svg>
                </div>
                <div>
                    <h4 class="alert-heading mb-0">This configuration will apply to the following organization: <span>{{ configuration.organization.name }}</span>.</h4>
                </div>
                <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
            </div>
            {{ form(customFieldsForm) }}
        {% endblock %}
        {% block modal_footer %}
        {% endblock %}
    {% endembed %}

    <div class="container-custom">
        <div class="card">
            <div class="row row-0">
                <div class="col-3 col-md-2">
                    <img src="{{ profile_picture }}" class="w-100 h-100 object-cover card-img-start" alt="{{ profile.username }}">
                </div>
                <div class="col">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-9">
                                <h3 class="card-title">{% if (profile.data.name ?? null) is empty %}{{ profile.username }}{% else %}{{ profile.data.name ~ ' (' ~ profile.username ~ ')' }}{% endif %}</h3>

                                <div class="row">
                                    {% if (profile.data.media_count ?? null) is not null %}
                                        <div class="col-4">
                                            <p><b>{{ profile.data.media_count ?? '??' }}</b> posts</p>
                                        </div>
                                    {% endif %}
                                    <div class="col-4">
                                        <p><b>{{ profile.data.fan_count ?? profile.data.followers_count ?? '??' }}</b> followers</p>
                                    </div>
                                    {% if (profile.data.follows_count ?? null) is not null %}
                                        <div class="col-4">
                                            <p><b>{{ profile.data.follows_count ?? '??' }}</b> following</p>
                                        </div>
                                    {% endif %}
                                </div>

                                <p>{{ (profile.data.biography ?? profile.data.description ?? null)|meta_profile_bio(profile.platform) }}</p>

                                {% if (profile.data.about ?? null) is not null %}
                                    <p><b>{{ profile.data.about }}</b></p>
                                {% endif %}

                                {% if (profile.data.display_subtext ?? null) is not null %}
                                    <p><b>{{ profile.data.display_subtext }}</b></p>
                                {% endif %}

                                {% if (profile.data.website ?? null) is not null %}
                                    <p><a href="{{ profile.data.website }}" target="_blank" rel="noreferrer noopener nofollow" class="text-decoration-none">{{ ux_icon('ph:link-simple-duotone', { width: 16, height: 16 }) }} {{ profile.data.website }}</a></p>
                                {% endif %}
                            </div>
                            <div class="col-3 d-flex justify-content-end align-items-center">
                                <div class="btn-list flex-nowrap">
                                    <div class="dropdown">
                                        <button class="btn dropdown-toggle align-text-top btn-primary" data-bs-toggle="dropdown" aria-expanded="false">
                                            Actions
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-end" data-popper-placement="bottom-end">
                                            <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#updateModal"><i class="fa fa-arrows-rotate text-green me-2"></i> Force Update</a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#configureCustomFields"><i class="fa fa-gear me-2"></i> Configure Custom Fields</a>
                                            <div class="dropdown-divider"></div>
                                            <a class="dropdown-item disabled" href="#"><i class="fa fa-robot me-2"></i> Generate AI Report (Soon)</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="posts" class="row posts mt-3">
            {% for post in posts %}
                <div class="col-12 col-md-6 col-lg-4 mb-3">
                    <div class="card d-flex flex-column h-100">
                        <div class="ratio ratio-1x1">
                            <img class="card-img-top object-fit-cover" src="{{ post.data.full_picture ?? post.data.thumbnail_url ?? post.data.media_url ?? asset('images/default/media-unavailable-min.jpg') }}" alt="{{ post.data.caption ?? null }}">
                        </div>
                        <div class="card-body d-flex flex-column">
                            <div class="text-secondary">{{ (post.data.caption ?? post.data.message ?? null)|u.truncate(256, '...') }}</div>
                            <div class="d-flex align-items-center justify-content-between pt-4 mt-auto">
                                <div class="likes">
                                    {{ ux_icon('ph:heart-straight-fill', { width: 16, height: 16 }) }} {{ post|meta_post_reactions_count }}
                                </div>
                                <div class="comments">
                                    {{ ux_icon('ph:chat-teardrop-text', { width: 16, height: 16 }) }} {{ post.data.comments_count }}
                                </div>
                                <div class="time">
                                    {{ ux_icon('ph:clock-fill', { width: 16, height: 16 }) }} {{ (post.data.timestamp ?? post.data.created_time)|format_datetime('short', 'short') }}
                                </div>
                                <a class="link btn" href="{{ post.data.permalink ?? post.data.permalink_url }}" target="_blank" rel="noreferrer noopener nofollow">
                                    {{ ux_icon('ph:arrow-square-out-duotone', { width: 16, height: 16 }) }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        <div class="d-flex w-100 justify-content-center">
            <div id="post-loader" class="text-center py-4">
                <div class="spinner-border text-primary d-none" role="status"></div>
            </div>
        </div>
    </div>
    <script>
        const icons = {
            like: '{{ ux_icon('ph:heart-straight-fill', { width: 16, height: 16 })|e('js') }}',
            comment: '{{ ux_icon('ph:chat-teardrop-text', { width: 16, height: 16 })|e('js') }}',
            clock: '{{ ux_icon('ph:clock-fill', { width: 16, height: 16 })|e('js') }}',
            link: '{{ ux_icon('ph:arrow-square-out-duotone', { width: 16, height: 16 })|e('js') }}',
        };

        let page = 0;
        let loading = false;
        let hasMore = true;

        const postsContainer = document.getElementById('posts');
        const loader = document.getElementById('post-loader');
        const loaderSpinner = loader.querySelector('.spinner-border');

        const formatDate = (isoString) => {
            const date = new Date(isoString);
            return date.toLocaleDateString(undefined, { hour: '2-digit', minute: '2-digit' });
        };

        const truncate = (str, length = 256) => {
            if (!str) return '';
            return str.length > length ? str.slice(0, length) + '...' : str;
        };

        function createPostCard(post) {
            const caption = truncate(post.data.caption ?? post.data.message);
            const imageUrl = post.data.full_picture || post.data.thumbnail_url || post.data.media_url || '{{ asset('images/default/media-unavailable-min.jpg') }}';
            const permalink = post.data.permalink || post.data.permalink_url || '#';

            return `
            <div class="col-12 col-md-6 col-lg-4 mb-3">
                <div class="card d-flex flex-column h-100">
                    <div class="ratio ratio-1x1">
                        <img class="card-img-top object-fit-cover" src="${imageUrl}" alt="${caption}">
                    </div>
                    <div class="card-body d-flex flex-column">
                        <div class="text-secondary">${caption}</div>
                        <div class="d-flex align-items-center justify-content-between pt-4 mt-auto">
                            <div class="likes">${icons.like} ${post.data.like_count ?? 0}</div>
                            <div class="comments">${icons.comment} ${post.data.comments_count ?? 0}</div>
                            <div class="time">${icons.clock} ${formatDate(post.data.timestamp ?? post.data.created_time)}</div>
                            <a class="link btn" href="${permalink}" target="_blank" rel="noreferrer noopener nofollow">
                                ${icons.link}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;
        }

        const observer = new IntersectionObserver(async ([entry]) => {
            if (entry.isIntersecting && !loading && hasMore) {
                loading = true;
                loaderSpinner.classList.remove('d-none');
                page++;

                const res = await fetch(`{{ path('app_meta_profile_posts_load', { id: profile.id }) }}?offset=${page * 12}`);
                const data = await res.json();

                if (!Array.isArray(data) || data.length === 0) {
                    hasMore = false;
                    observer.disconnect();
                } else {
                    data.forEach(post => {
                        const div = document.createElement('div');
                        div.innerHTML = createPostCard(post);
                        postsContainer.appendChild(div.firstElementChild);
                    });
                }

                loaderSpinner.classList.add('d-none');
                loading = false;
            }
        }, { threshold: 1 });

        observer.observe(loader);
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            document.getElementById('updateModal').addEventListener('shown.bs.modal', () => {
                startStreamingUpdate();
            });
        });

        function startStreamingUpdate() {
            const evtSource = new EventSource("{{ path('app_meta_profile_update', { id: profile.id }) }}");
            const updateModal = document.getElementById('updateModal')
            const progressBar = updateModal.querySelector('.progress-bar');
            const statusText = updateModal.querySelector('.status-text');
            const progressText = updateModal.querySelector('.progress-text');
            const doneButton = updateModal.querySelector('.btn-done');

            evtSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                progressBar.classList.remove('progress-bar-indeterminate');
                progressBar.style.width = data.progress + '%';
                progressText.textContent = data.progress + '%';
                statusText.textContent = data.message;
            };

            evtSource.addEventListener('done', (event) => {
                const data = JSON.parse(event.data);
                progressBar.style.width = data.progress + '%';
                statusText.textContent = data.message;
                progressText.textContent = data.progress + '%';
                doneButton.classList.remove('disabled');
                evtSource.close();
            });

            evtSource.onerror = function(err) {
                statusText.textContent = 'An error occurred, try again later.';
                doneButton.classList.remove('disabled');
                evtSource.close();
            };
        }
    </script>
{% endblock %}
