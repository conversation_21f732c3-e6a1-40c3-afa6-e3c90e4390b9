<style>
    .collection-content > div {
        display: flex;
    }
</style>
{{ form_start(forms.socialTrackingGroup) }}
    <div class="modal-body">
        <div class="mb-3">
            {{ form_row(forms.socialTrackingGroup.name) }}
        </div>
        {% if (edit ?? false) == false %}
            <label class="form-label required">Data Source</label>
            <div class="form-selectgroup-boxes row mb-3">
                {% for src in forms.socialTrackingGroup.dataSource %}
                    {% if loop.index == 1 %}
                        <div class="col-lg-6">
                            <label class="form-selectgroup-item">
                                <input type="radio" name="{{ src.vars.full_name }}" value="{{ src.vars.value }}" class="form-selectgroup-input" {{ forms.socialTrackingGroup.dataSource.vars.data == src.vars.value ? 'checked="checked"' : '' }}>
                                <span class="form-selectgroup-label d-flex align-items-center p-3">
                                    <span class="me-3">
                                        <span class="form-selectgroup-check"></span>
                                    </span>
                                    <span class="form-selectgroup-label-content">
                                      <span class="form-selectgroup-title strong mb-1">Facebook</span>
                                      <span class="d-block text-secondary">Public Facebook Data</span>
                                    </span>
                                </span>
                            </label>
                        </div>
                    {% else %}
                        <div class="col-lg-6">
                            <label class="form-selectgroup-item">
                                <input type="radio" name="{{ src.vars.full_name }}" value="{{ src.vars.value }}" class="form-selectgroup-input" {{ forms.socialTrackingGroup.dataSource.vars.data == src.vars.value ? 'checked="checked"' : '' }}>
                                <span class="form-selectgroup-label d-flex align-items-center p-3">
                                    <span class="me-3">
                                      <span class="form-selectgroup-check"></span>
                                    </span>
                                    <span class="form-selectgroup-label-content">
                                      <span class="form-selectgroup-title strong mb-1">Instagram</span>
                                      <span class="d-block text-secondary">Public Instagram Data</span>
                                    </span>
                                </span>
                            </label>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
            <div class="mb-3">
                {{ form_row(forms.socialTrackingGroup.listType) }}
            </div>
        {% endif %}
        <div class="mb-3">
            {{ form_row(forms.socialTrackingGroup.category) }}
        </div>
        <div class="mb-3">
            {{ form_row(forms.socialTrackingGroup.subCategory) }}
        </div>
        <div class="mb-3">
            <label class="form-label required" for="profiles">Profiles List</label>
            <textarea class="form-control" id="profiles" name="{{ forms.socialTrackingGroup.list.vars.full_name }}" placeholder="john_doe&#10;alice.doe" data-bs-toggle="autosize" rows="5">{{ (trackingGroup ?? null) != null ? trackingGroup.listForDisplay|join("\r\n") : null }}</textarea>
            <span class="form-hint">Usernames. One per line or separated by comma. Each profile counts as a row.</span>
        </div>
    </div>
    <div class="modal-body">
        <div class="row instagram-filters {% if (trackingGroup ?? null) != null and trackingGroup.dataSource != 'meta:instagram' %}d-none{% endif %}">
            <div class="col-lg-12">
                <div class="mb-3">
                    <label class="form-label required" for="max-posts-per-profile">Maximum Posts per Profile</label>
                    <input type="number" id="max-posts-per-profile" name="{{ forms.socialTrackingGroup.maximumPostsPerProfile.vars.full_name }}" class="form-control" min="0" value="{{ forms.socialTrackingGroup.maximumPostsPerProfile.vars.data ?? 10000 }}">
                    <span class="form-hint">0 = Unlimited. Each post counts as a row.</span>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="mb-3">
                    <label class="form-label required" for="max-comments-per-post">Maximum Comments per Post</label>
                    <input type="number" id="max-comments-per-post" name="{{ forms.socialTrackingGroup.maximumCommentsPerPost.vars.full_name }}" class="form-control" min="0" value="{{ forms.socialTrackingGroup.maximumCommentsPerPost.vars.data ?? 10000 }}">
                    <span class="form-hint">0 = Unlimited. Each comment counts as a row.</span>
                </div>
            </div>
        </div>
        <div class="row facebook-filters {% if (trackingGroup ?? null) != null and trackingGroup.dataSource != 'meta:facebook' %}d-none{% endif %}">
            <div class="col-lg-6">
                <div class="mb-3">
                    <label class="form-label required" for="fb-filter-start-at">Get posts starting from</label>
                    <input class="form-control" type="date" placeholder="Select a date" id="fb-filter-start-at" name="tracking_group[filterStartsAt]" value="{{ filters is defined and (filters is not empty) and (filters.starts_at is defined) ? filters.starts_at : ('now'|date_modify('first day of last month')|date('Y-m-d')) }}" required>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="mb-3">
                    <label class="form-label" for="fb-filter-ends-at">Get posts until</label>
                    <input class="form-control" type="date" placeholder="Select a date" id="fb-filter-ends-at" name="tracking_group[filterEndsAt]" value="{{ (filters is defined and  (filters is not empty) and (filters.ends_at is defined) ? filters.ends_at : '') }}">
                    <span class="form-hint">Leave it empty to get up to the most recent posts.</span>
                </div>
            </div>
        </div>
        <div class="row mb-3">
            <div class="col-lg-12">
                <div class="accordion" id="accordion-advanced-settings">
                    <div class="accordion-item">
                        <button class="accordion-header accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-1-default" aria-expanded="true">
                            Advanced Settings
                        </button>
                        <div id="collapse-1-default" class="accordion-collapse collapse" data-bs-parent="#accordion-advanced-settings">
                            <div class="accordion-body">
                                <div class="col-12">
                                    <div class="mb-3">
                                        <div class="form-label">Advanced Analysis</div>
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="{{ forms.socialTrackingGroup.runProfileAIAnalysis.vars.full_name }}" {{ forms.socialTrackingGroup.runProfileAIAnalysis.vars.data ? 'checked="checked"' : '' }}>
                                            <span class="form-check-label">Advanced AI Profile Analysis</span>
                                        </label>
                                        <span class="form-hint">This costs 5 additional credits per profile/person/group per data update.</span>
                                    </div>
                                    <div class="mb-3">
                                        <div class="form-label">Data State</div>
                                        <label class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" name="{{ forms.socialTrackingGroup.keepDataUpdated.vars.full_name }}" {{ forms.socialTrackingGroup.keepDataUpdated.vars.data ? 'checked="checked"' : '' }}>
                                            <span class="form-check-label">Automatically update data (updated every 3 hours)</span>
                                        </label>
                                        <span class="form-hint">Each new profile update, each new post and each new comment will count as individual rows.</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="accordion" id="accordion-custom-fields">
                    <div class="accordion-item">
                        <button class="accordion-header accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-2-default" aria-expanded="true">
                            Custom Fields
                        </button>
                        <div id="collapse-2-default" class="accordion-collapse collapse" data-bs-parent="#accordion-custom-fields">
                            <div class="accordion-body">
                                <div class="col-12">
                                    <div class="mb-3">
                                        {{ form_row(forms.socialTrackingGroup.customFields) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-body pb-0">
        <div id="credits-estimation" class="mb-3 d-none">
            <div class="alert alert-important alert-info alert-dismissible mb-0" role="alert">
                <div class="d-flex">
                    <div>
                        <i class="fas fa-info icon icon-1"></i>
                    </div>
                    <div>
                        <h4 class="alert-title"></h4>
                    </div>
                </div>
            </div>
        </div>
        {% if (options.include_submit ?? true) == true %}
            <div class="row mt-3">
                <div class="col-lg-12 d-flex justify-content-end">
                    <a href="{{ path('app_app_dashboard') }}" class="btn btn-link link-secondary btn-3" data-bs-dismiss="modal">Cancel</a>
                    <button type="submit" class="btn btn-primary btn-5 ms-auto">
                        <i class="fa fa-save icon icon-2"></i> Update
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
    {{ form_row(forms.socialTrackingGroup._token) }}
{{ form_end(forms.socialTrackingGroup, { render_rest: false }) }}
<script>
    {% if (edit ?? false) == false %}
        document.addEventListener('DOMContentLoaded', function() {
            let listType = document.querySelector('select[name="{{ forms.socialTrackingGroup.listType.vars.full_name }}"]');
            let fbFilters = document.querySelector('.facebook-filters');
            let inFilters = document.querySelector('.instagram-filters');

            document.querySelectorAll('input[name="{{ forms.socialTrackingGroup.dataSource.vars.full_name }}"]').forEach((radio) => {
                radio.addEventListener('change', (event) => {
                    if (event.target.value === 'meta:instagram') {
                        listType.value = 'meta:profiles';
                        listType.parentElement.style.display = 'none';

                        fbFilters.classList.add('d-none');
                        fbFilters.classList.remove('d-flex');
                        inFilters.classList.add('d-flex');
                        inFilters.classList.remove('d-none');
                    } else {
                        listType.value = 'meta:pages';
                        listType.parentElement.style.display = 'block';

                        fbFilters.classList.add('d-flex');
                        fbFilters.classList.remove('d-none');
                        inFilters.classList.add('d-none');
                        inFilters.classList.remove('d-flex');

                        document.getElementById('max-posts-per-profile').value = 0;
                        document.getElementById('max-posts-per-profile').dispatchEvent(new Event('change', { bubbles: true }));
                    }
                });
            });
        {% endif %}

        let profiles = document.getElementById('profiles');
        let maxPostsPerProfile = document.getElementById('max-posts-per-profile');
        let maxCommentsPerPost = document.getElementById('max-comments-per-post');

        let creditsEstimation = document.getElementById('credits-estimation');

        profiles.addEventListener('change', estimateCredits);
        maxPostsPerProfile.addEventListener('change', estimateCredits);
        maxCommentsPerPost.addEventListener('change', estimateCredits);

        function estimateCredits() {
            const str = profiles.value;
            const array = str.includes(',') ? str.split(',') : str.split(/\r?\n/);

            if (maxPostsPerProfile.value > 0 && maxCommentsPerPost.value > 0) {
                if (array.length > 0) {
                    const est = array.length * maxPostsPerProfile.value * maxCommentsPerPost.value;

                    creditsEstimation.classList.remove('d-none');
                    creditsEstimation.classList.add('d-block');
                    creditsEstimation.querySelector('.alert-title').innerHTML = `This might cost approximately ~${est} credits. Real cost may vary.`;
                } else {
                    creditsEstimation.classList.remove('d-block');
                    creditsEstimation.classList.add('d-none');
                }
            } else {
                creditsEstimation.classList.remove('d-none');
                creditsEstimation.classList.add('d-block');
                creditsEstimation.querySelector('.alert-title').innerHTML = `Since you are going with unlimited parameters, this can end up using all your credits.`;
            }
        }
    });
</script>