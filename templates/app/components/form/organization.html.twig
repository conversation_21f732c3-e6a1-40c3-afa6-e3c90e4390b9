{{ form_start(forms.organization, { action: path('app_organization_create') }) }}
    <div class="modal-body">
        <div class="mb-3">
            {{ form_row(forms.organization.name) }}
        </div>
    </div>
    {% if (options.include_submit ?? true) == true %}
        <div class="modal-body pb-0">
            <div class="row">
                <div class="col-lg-12 d-flex justify-content-end">
                    <a href="{{ path('app_organization') }}" class="btn btn-link link-secondary btn-3" data-bs-dismiss="modal">Cancel</a>
                    <button type="submit" class="btn btn-primary btn-5 ms-auto">
                        <i class="fa fa-save icon icon-2"></i> Update
                    </button>
                </div>
            </div>
        </div>
    {% endif %}
    {{ form_row(forms.organization._token) }}
{{ form_end(forms.organization, { render_rest: false }) }}