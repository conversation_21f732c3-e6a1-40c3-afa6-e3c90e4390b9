{% extends 'app/modern_dashboard/base.html.twig' %}

{% block title %}{{ 'common.dashboard'|trans }} | {{ 'app.name'|trans }}{% endblock %}
{% block page_title %}{{ 'common.dashboard'|trans }}{% endblock %}

{% block content %}
    <!-- Stats Summary -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <!-- Total Tracking Groups -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-primary-100 dark:bg-primary-900 rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600 dark:text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                </div>
                <div class="ml-5">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ 'dashboard.stats.total_groups'|trans }}</div>
                    <div class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">{{ trackingGroups|length }}</div>
                </div>
            </div>
        </div>

        <!-- Active Tracking Groups -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-green-100 dark:bg-green-900 rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600 dark:text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-5">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ 'dashboard.stats.active_tracking'|trans }}</div>
                    <div class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
                        {% set activeCount = 0 %}
                        {% for group in trackingGroups %}
                            {% if group.status.status.value == 'running' or group.status.status.value == 'waiting_next_update' %}
                                {% set activeCount = activeCount + 1 %}
                            {% endif %}
                        {% endfor %}
                        {{ activeCount }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Completed Tracking Groups -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex-shrink-0 bg-blue-100 dark:bg-blue-900 rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <div class="ml-5">
                    <div class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ 'dashboard.stats.completed_tracking'|trans }}</div>
                    <div class="mt-1 text-3xl font-semibold text-gray-900 dark:text-white">
                        {% set completedCount = 0 %}
                        {% for group in trackingGroups %}
                            {% if group.status.status.value == 'finished' %}
                                {% set completedCount = completedCount + 1 %}
                            {% endif %}
                        {% endfor %}
                        {{ completedCount }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tracking Groups -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h2 class="text-lg font-medium text-gray-900 dark:text-white">{{ 'menu.tracking_groups'|trans(domain='menu') }}</h2>
            <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <i class="fas fa-plus mr-2"></i> {{ 'tracking_group.create_new'|trans }}
            </button>
        </div>
        <div class="p-6">
            {% if trackingGroups|length > 0 %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {% for group in trackingGroups %}
                        {% set percentage = group.status.totalProfiles > 0 ? ((group.status.progress / group.status.totalProfiles) * 100)|round : 0 %}

                        {% set statusColor = {
                            'running': {
                                'border': 'border-blue-500',
                                'bg': 'bg-blue-50 dark:bg-blue-900/20',
                                'text': 'text-blue-700 dark:text-blue-300',
                                'badge': 'bg-blue-100 dark:bg-blue-900',
                                'icon': 'bg-blue-500',
                                'progress': 'bg-blue-600 dark:bg-blue-500',
                                'animate': 'animate-pulse',
                                'glow': 'shadow-[0_0_15px_rgba(59,130,246,0.5)]',
                                'card': 'ring-1 ring-blue-500/50'
                            },
                            'waiting_next_update': {
                                'border': 'border-green-500',
                                'bg': 'bg-green-50 dark:bg-green-900/20',
                                'text': 'text-green-700 dark:text-green-300',
                                'badge': 'bg-green-100 dark:bg-green-900',
                                'icon': 'bg-green-500',
                                'progress': 'bg-green-600 dark:bg-green-500',
                                'animate': '',
                                'glow': '',
                                'card': 'ring-1 ring-green-500/50'
                            },
                            'finished': {
                                'border': 'border-purple-500',
                                'bg': 'bg-purple-50 dark:bg-purple-900/20',
                                'text': 'text-purple-700 dark:text-purple-300',
                                'badge': 'bg-purple-100 dark:bg-purple-900',
                                'icon': 'bg-purple-500',
                                'progress': 'bg-purple-600 dark:bg-purple-500',
                                'animate': '',
                                'glow': '',
                                'card': 'ring-1 ring-purple-500/50'
                            },
                            'paused': {
                                'border': 'border-yellow-500',
                                'bg': 'bg-yellow-50 dark:bg-yellow-900/20',
                                'text': 'text-yellow-700 dark:text-yellow-300',
                                'badge': 'bg-yellow-100 dark:bg-yellow-900',
                                'icon': 'bg-yellow-500',
                                'progress': 'bg-yellow-600 dark:bg-yellow-500',
                                'animate': '',
                                'glow': '',
                                'card': 'ring-1 ring-yellow-500/50'
                            },
                            'pause_requested': {
                                'border': 'border-amber-500',
                                'bg': 'bg-amber-50 dark:bg-amber-900/20',
                                'text': 'text-amber-700 dark:text-amber-300',
                                'badge': 'bg-amber-100 dark:bg-amber-900',
                                'icon': 'bg-amber-500',
                                'progress': 'bg-amber-600 dark:bg-amber-500',
                                'animate': 'animate-pulse',
                                'glow': 'shadow-[0_0_15px_rgba(245,158,11,0.5)]',
                                'card': 'ring-1 ring-amber-500/50'
                            },
                            'queued': {
                                'border': 'border-indigo-500',
                                'bg': 'bg-indigo-50 dark:bg-indigo-900/20',
                                'text': 'text-indigo-700 dark:text-indigo-300',
                                'badge': 'bg-indigo-100 dark:bg-indigo-900',
                                'icon': 'bg-indigo-500',
                                'progress': 'bg-indigo-600 dark:bg-indigo-500',
                                'animate': 'animate-pulse',
                                'glow': '',
                                'card': 'ring-1 ring-indigo-500/50'
                            },
                            'error': {
                                'border': 'border-red-500',
                                'bg': 'bg-red-50 dark:bg-red-900/20',
                                'text': 'text-red-700 dark:text-red-300',
                                'badge': 'bg-red-100 dark:bg-red-900',
                                'icon': 'bg-red-500',
                                'progress': 'bg-red-600 dark:bg-red-500',
                                'animate': '',
                                'glow': 'shadow-[0_0_15px_rgba(239,68,68,0.5)]',
                                'card': 'ring-1 ring-red-500/50'
                            }
                        } %}

                        {% set status = group.status.status.value %}
                        {% set colors = statusColor[status] ?? {
                            'border': 'border-gray-300',
                            'bg': 'bg-gray-50 dark:bg-gray-800',
                            'text': 'text-gray-700 dark:text-gray-300',
                            'badge': 'bg-gray-100 dark:bg-gray-700',
                            'icon': 'bg-gray-500',
                            'progress': 'bg-gray-600 dark:bg-gray-500',
                            'animate': '',
                            'glow': '',
                            'card': ''
                        } %}

                        {% set statusIcons = {
                            'running': 'fa-play-circle',
                            'waiting_next_update': 'fa-clock',
                            'finished': 'fa-check-circle',
                            'paused': 'fa-pause-circle',
                            'pause_requested': 'fa-hand-paper',
                            'queued': 'fa-hourglass-half',
                            'error': 'fa-exclamation-triangle'
                        } %}

                        {% set statusIcon = statusIcons[status] ?? 'fa-question' %}

                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-200 hover:shadow-md hover:translate-y-[-2px] {{ colors.card }} {{ colors.glow }}">
                            <!-- Card Header with Status -->
                            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700 {{ colors.bg }} flex justify-between items-center">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 rounded-full {{ colors.badge }} flex items-center justify-center {{ colors.text }}">
                                        <i class="fas {{ statusIcon }} {{ colors.animate }}"></i>
                                    </div>
                                    <h3 class="text-base font-medium text-gray-900 dark:text-white truncate max-w-[150px]" title="{{ group.name }}">{{ group.name }}</h3>
                                </div>
                                <div class="flex flex-col items-end">
                                    {% include 'components/Modern/TrackingGroupStatusBadge.html.twig' with {'status': status} %}
                                </div>
                            </div>

                            <!-- Card Body -->
                            <div class="p-4">
                                <!-- Key Stats -->
                                <div class="grid grid-cols-2 gap-3 mb-4">
                                    <div class="bg-gray-50 dark:bg-gray-700/50 rounded p-2 text-center">
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Profiles</div>
                                        <div class="text-lg font-semibold text-gray-900 dark:text-white">{{ group.status.totalProfiles }}</div>
                                    </div>
                                    <div class="bg-gray-50 dark:bg-gray-700/50 rounded p-2 text-center">
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">Source</div>
                                        <div class="text-sm font-medium text-gray-900 dark:text-white truncate" title="{{ group.dataSource }}">{{ group.dataSource }}</div>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <div class="mb-4">
                                    <div class="flex justify-between text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        <span>Progress</span>
                                        <span>{{ percentage }}%</span>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="{{ colors.progress }} h-2 rounded-full {{ colors.animate }}" style="width: {{ percentage }}%"></div>
                                    </div>
                                    <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <span>{{ group.status.progress }} processed</span>
                                        <span>{{ group.status.totalProfiles }} total</span>
                                    </div>
                                </div>

                                <!-- Metadata -->
                                <div class="text-xs text-gray-500 dark:text-gray-400 mb-4">
                                    <div class="flex justify-between mb-1">
                                        <span>Created:</span>
                                        <span>{{ group.createdAt|date('M d, Y') }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span>Updated:</span>
                                        <span>{{ group.updatedAt|date('M d, Y H:i') }}</span>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex justify-between border-t border-gray-200 dark:border-gray-700 pt-3">
                                    <a href="{{ path('app_social_tracking_group_view', {id: group.id}) }}" class="text-xs flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300">
                                        <i class="fas fa-eye mr-1"></i> View Details
                                    </a>

                                    <div class="flex space-x-1">
                                        {% if status == 'running' %}
                                            <a href="{{ path('app_social_tracking_group_pause', {id: group.id}) }}" class="text-xs px-2 py-1 rounded bg-yellow-100 text-yellow-700 hover:bg-yellow-200 dark:bg-yellow-900/50 dark:text-yellow-300 dark:hover:bg-yellow-900">
                                                <i class="fas fa-pause mr-1"></i> Pause
                                            </a>
                                        {% elseif status == 'paused' %}
                                            <a href="{{ path('app_social_tracking_group_queue', {id: group.id}) }}" class="text-xs px-2 py-1 rounded bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/50 dark:text-green-300 dark:hover:bg-green-900">
                                                <i class="fas fa-play mr-1"></i> Resume
                                            </a>
                                        {% endif %}

                                        <button type="button" class="group relative text-xs px-2 py-1 rounded bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                                            <i class="fas fa-ellipsis-h"></i>
                                            <div class="hidden group-hover:block absolute right-0 bottom-8 w-32 bg-white dark:bg-gray-800 rounded shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-10">
                                                <a href="{{ path('app_social_tracking_group_edit', {id: group.id}) }}" class="block px-3 py-1 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                    <i class="fas fa-edit mr-2"></i> Edit
                                                </a>
                                                <a href="{{ path('app_social_tracking_group_force_update', {id: group.id}) }}" class="block px-3 py-1 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                                                    <i class="fas fa-sync mr-2"></i> Update
                                                </a>
                                                <a href="{{ path('app_social_tracking_group_delete', {id: group.id}) }}" class="block px-3 py-1 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/30">
                                                    <i class="fas fa-trash mr-2"></i> Delete
                                                </a>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">{{ 'tracking_group.no_groups'|trans }}</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ 'tracking_group.get_started'|trans }}</p>
                    <div class="mt-6">
                        <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-2"></i> {{ 'tracking_group.create_new'|trans }}
                        </button>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Activity Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Activity Chart -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">{{ 'tracking_group.activity_chart'|trans }}</h2>
            </div>
            <div class="p-6">
                <div id="activity-chart" class="h-80"></div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">{{ 'tracking_group.recent_activity'|trans }}</h2>
            </div>
            <div class="p-6">
                {% if recentActivity|length > 0 %}
                    <div class="space-y-4">
                        {% for activity in recentActivity %}
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900 flex items-center justify-center text-primary-600 dark:text-primary-400">
                                        {{ activity.trackingGroup.name|first|upper }}
                                    </div>
                                </div>
                                <div class="min-w-0 flex-1">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">
                                        {{ activity.trackingGroup.name }}
                                    </p>
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ activity.progress }} / {{ activity.totalProfiles }} profiles processed
                                    </p>
                                    <p class="text-xs text-gray-400 dark:text-gray-500">
                                        {{ activity.createdAt|date('M d, H:i') }}
                                    </p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-6">
                        <p class="text-sm text-gray-500 dark:text-gray-400">No recent activity</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.41.0/dist/apexcharts.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Sample data for the activity chart
            // In a real implementation, this would come from the server
            const options = {
                chart: {
                    type: 'area',
                    height: 320,
                    toolbar: {
                        show: false
                    },
                    fontFamily: 'inherit',
                    foreColor: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#4b5563',
                },
                series: [{
                    name: 'Profiles Processed',
                    data: [30, 40, 35, 50, 49, 60, 70, 91, 125, 150]
                }],
                xaxis: {
                    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct'],
                    labels: {
                        style: {
                            colors: document.documentElement.getAttribute('data-theme') === 'dark' ? '#9ca3af' : '#4b5563',
                        }
                    }
                },
                yaxis: {
                    labels: {
                        style: {
                            colors: document.documentElement.getAttribute('data-theme') === 'dark' ? '#9ca3af' : '#4b5563',
                        }
                    }
                },
                colors: ['#6366f1'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.3,
                    }
                },
                stroke: {
                    curve: 'smooth',
                    width: 2,
                },
                dataLabels: {
                    enabled: false
                },
                grid: {
                    borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                    strokeDashArray: 4,
                },
                tooltip: {
                    theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light',
                }
            };

            const chart = new ApexCharts(document.querySelector("#activity-chart"), options);
            chart.render();

            // Update chart theme when theme changes
            const themeToggle = document.getElementById('theme-toggle');
            themeToggle.addEventListener('click', function() {
                setTimeout(() => {
                    const isDark = document.documentElement.classList.contains('dark');
                    chart.updateOptions({
                        chart: {
                            foreColor: isDark ? '#9ca3af' : '#4b5563',
                        },
                        xaxis: {
                            labels: {
                                style: {
                                    colors: isDark ? '#9ca3af' : '#4b5563',
                                }
                            }
                        },
                        yaxis: {
                            labels: {
                                style: {
                                    colors: isDark ? '#9ca3af' : '#4b5563',
                                }
                            }
                        },
                        grid: {
                            borderColor: isDark ? '#374151' : '#e5e7eb',
                        },
                        tooltip: {
                            theme: isDark ? 'dark' : 'light',
                        }
                    });
                }, 100);
            });

            // Auto-refresh every 10 seconds
            setInterval(() => {
                // In a real implementation, this would fetch new data from the server
                // For now, we'll just simulate data changes
                const newData = options.series[0].data.map(val => val + Math.floor(Math.random() * 10) - 5);
                chart.updateSeries([{
                    data: newData
                }]);

                // You would also update other elements like tracking groups and recent activity
            }, 10000);
        });
    </script>
{% endblock %}
