{% extends 'app/dashboard/page.html.twig' %}

{% block title %}Magnora{% endblock %}

{% block page_subtitle %}{% endblock %}
{% block page_actions %}{% endblock %}

{% block page_content_before %}
    {% include 'app/components/modal/add_tracking_group.html.twig' %}
{% endblock %}

{% block page_content %}
    {{ component('Dashboard:StatsSummary') }}

    <div class="card mb-4">
        <div class="card-header">
            <h3 class="card-title">Tracking Groups</h3>
        </div>
        <div class="card-body">
            {{ component('Social:TrackingGroupCards') }}
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-lg-8">
            {{ component('Dashboard:ActivityChart') }}
        </div>
        <div class="col-lg-4">
            {{ component('Dashboard:RecentActivity') }}
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-refresh tracking group cards every 10 seconds
            setInterval(() => {
                const trackingGroupCards = document.getElementById('social-tracking-group-cards-controller');
                if (trackingGroupCards && trackingGroupCards.__component) {
                    trackingGroupCards.__component.render();
                }

                // Also refresh the stats summary and activity chart
                const statsSummary = document.querySelector('[data-controller="live"][data-live-id="Dashboard:StatsSummary"]');
                if (statsSummary && statsSummary.__component) {
                    statsSummary.__component.render();
                }

                const activityChart = document.querySelector('[data-controller="live"][data-live-id="Dashboard:ActivityChart"]');
                if (activityChart && activityChart.__component) {
                    activityChart.__component.render();
                }

                const recentActivity = document.querySelector('[data-controller="live"][data-live-id="Dashboard:RecentActivity"]');
                if (recentActivity && recentActivity.__component) {
                    recentActivity.__component.render();
                }
            }, 10000);
        });
    </script>
{% endblock %}