{#
Use this as your new starter template page, use it to start your new project,
by adding this code to your own base template:

{% extends '@Tabler/layout-horizontal.html.twig' %}

Enjoy your theme!
#}
<!doctype html{% block html_start %}{% endblock %}>
<html lang="{{ app.request.locale }}"{% if tabler_bundle.isRightToLeft() %} dir="rtl"{% endif %}>
<head>
    {% block head %}
    {% include '@Tabler/includes/html_head.html.twig' %}
    {% endblock %}
    <title>{% block title %}{{ block('page_title') }}{% endblock %}</title>
    {% block stylesheets %}
        {% if tabler_bundle.isRightToLeft() %}
        <link rel="stylesheet" href="{{ asset('bundles/tabler/tabler-rtl.css') }}?{{ tabler_asset_version() }}">
        {% else %}
        <link rel="stylesheet" href="{{ asset('bundles/tabler/tabler.css') }}?{{ tabler_asset_version() }}">
        {% endif %}
        <style>
            .sf-toolbar {
                display: none !important;
            }

            .select-org {
                width: fit-content;
                font-size: 1rem;
            }
        </style>
    {% endblock %}
</head>
<body{% block body_start %}{% endblock %} class="{{ 'antialiased'|tabler_body }} {% block body_class %}{% endblock %}" data-bs-theme="{{ tabler_theme() }}">
{% block after_body_start %}{% endblock %}
<div class="page">

    {# *** Layout type changes *** #}
    {% block header %}
    <header id="{% block header_id %}{% endblock %}" class="navbar navbar-expand-md{% block header_class %} {% if tabler_bundle.isNavbarOverlapping() %}navbar-overlap{% endif %}{% endblock %} d-print-none" data-bs-theme="{{ tabler_bundle.isNavbarOverlapping() or tabler_bundle.isHeaderDark() or tabler_bundle.isDarkMode() ? 'dark' : 'light' }}">
        <div class="{{ ''|tabler_container }}">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu">
                <span class="navbar-toggler-icon"></span>
            </button>
            {% block app_logo %}
            <h1 class="{% block navbar_brand_classes %}navbar-brand d-none-navbar-horizontal pe-0 pe-md-3 {% block navbar_brand_class %}navbar-brand-autodark{% endblock %}{% endblock %}">
                {% include '@Tabler/includes/logo.html.twig' %}
            </h1>
            {% endblock %}
            <div class="navbar-nav flex-row order-md-last">
                {% block navbar_start %}{% endblock %}
                {% block navbar_notifications %}
                {% include '@Tabler/includes/navbar_notifications.html.twig' %}
                {% endblock %}
                {% block navbar_user %}
                {% include '@Tabler/includes/navbar_user.html.twig' %}
                {% endblock %}
                {% block navbar_end %}{% endblock %}
            </div>
            {% if tabler_bundle.isCondensedNavbar() %}
            <div class="collapse navbar-collapse" id="navbar-menu">
                <div class="d-flex flex-column flex-md-row flex-fill align-items-stretch align-items-md-center">
                    {% block navbar_menu_condensed %}
                    {% include '@Tabler/includes/menu.html.twig' %}
                    {% endblock %}
                </div>
            </div>
            {% endif %}
        </div>
    </header>
    {% endblock %}

    {% block navbar %}
    {% if not tabler_bundle.isCondensedNavbar() %}
    <div id="{% block navbar_id %}{% endblock %}" class="navbar-expand-md">
        <div class="collapse navbar-collapse" id="navbar-menu">
            <div class="navbar" data-bs-theme="{{ tabler_bundle.isNavbarDark() or tabler_bundle.isDarkMode() ? 'dark' : 'light' }}">
                <div class="{{ ''|tabler_container }}">
                    {% block navbar_menu %}
                    {% include '@Tabler/includes/menu.html.twig' %}
                    {% endblock %}
                    {% block navbar_search %}
                    {% include '@Tabler/includes/navbar_search.html.twig' %}
                    {% endblock %}
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    {% endblock %}
    {# *************************** #}

    <div id="{% block page_wrapper_id %}{% endblock %}" class="page-wrapper">
        {% block full_page_content %}{% endblock %}
        {% block footer %}
            <footer id="{% block footer_id %}{% endblock %}" class="footer footer-transparent d-print-none">
                <div class="{{ ''|tabler_container }}">
                    <div class="row text-center align-items-center flex-row-reverse">
                        <div class="col-lg-auto ms-lg-auto">
                            <ul class="list-inline list-inline-dots mb-0">
                                <li class="list-inline-item"><a href="https://privacy-policy.terra.marketing" target="_blank" class="link-secondary">Privacy Policy</a>
                            </ul>
                        </div>
                        <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                            <ul class="list-inline list-inline-dots mb-0">
                                <li class="list-inline-item">
                                    Copyright &copy; {{ 'now'|date('Y') }}
                                    <a href="#" class="link-secondary">Terra</a>.
                                    All rights reserved.
                                </li>
                                <li class="list-inline-item">
                                    <a href="#" class="link-secondary" rel="noopener">v0.9.0-alpha1</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </footer>
        {% endblock %}
    </div>
</div>

{% block javascripts %}
<script src="{{ asset('bundles/tabler/tabler.js') }}?{{ tabler_asset_version() }}"></script>
{% endblock %}
</body>
</html>
