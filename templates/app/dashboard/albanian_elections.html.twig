{% extends 'app/dashboard/custom_page.html.twig' %}

{% block title %}Magnora{% endblock %}

{% block page_subtitle %}{% endblock %}
{% block page_actions %}{% endblock %}

{% block page_content_before %}
{% endblock %}

{% block full_page_content %}
    <iframe src="https://app.powerbi.com/view?r=eyJrIjoiMjY3ZTExZGYtOTg1MS00MGNiLTlhZDMtMzdhMGE2MThlMzRiIiwidCI6IjhlOWVhNzI4LWNiOWMtNDE4NC1hOWZlLWIyNjJhODIzZTA2NiIsImMiOjl9" width="100%" frameborder="0" style="width: 100%; min-height: calc(100vh - 120px);"></iframe>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener("click", function (event) {
            let target = event.target;
            if (target.classList.contains('btn-delete-row')) {
                confirmDelete(target.getAttribute('data-href'));
            }
        });

        function confirmDelete(link) {
            let result = confirm("Are you sure you want to delete this? It's irreversible!");
            if (result) {
                // If confirmed, allow the link to navigate
                window.location.replace(link);
                return true;
            } else {
                // If canceled, prevent the link from being followed
                return false;
            }
        }
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function () {
            setInterval(() => {
                document.getElementById('social-tracking-group-status-controller').__component.render();
            }, 10000); // Refresh every 10 seconds
        });
    </script>
{% endblock %}