{% extends 'base.html.twig' %}

{% block title %}{{ 'home.app.button'|trans }} | PostChat{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <style>
        .download-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }
        
        .download-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 2rem;
            max-width: 600px;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        }
        
        .prototype-badge {
            display: inline-block;
            background-color: #ffc107;
            color: #212529;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
    </style>
{% endblock %}

{% block body %}
    <div class="download-container">
        <div class="download-card">
            <div class="prototype-badge">{{ 'home.app.prototype_warning'|trans }}</div>
            <h1 class="mb-4">{{ 'home.app.button'|trans }}</h1>
            <p class="mb-4">{{ message }}</p>
            <a href="{{ path('app_main_index') }}" class="btn gradient-btn">{{ 'header.menu.home'|trans }}</a>
        </div>
    </div>
{% endblock %}
