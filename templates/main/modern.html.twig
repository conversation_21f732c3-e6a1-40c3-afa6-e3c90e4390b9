{% extends 'base.html.twig' %}

{% block title %}PostCHAT - {{ 'home.meta.title_suffix'|trans }}{% endblock %}

{% block meta %}
    <meta name="description" content="{{ 'home.meta.description'|trans }}">
    <meta name="keywords" content="delivery, logistics, seller, e-commerce, sales, marketing, automation, ai, chatbot">
    <meta property="og:title" content="PostCHAT - {{ 'home.meta.title_suffix'|trans }}">
    <meta property="og:description" content="{{ 'home.meta.description'|trans }}">
    <meta property="og:image" content="{{ absolute_url(asset('img/logo/postchat-logo-512-min.png')) }}">
    <meta property="og:url" content="{{ app.request.uri }}">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="PostCHAT - {{ 'home.meta.title_suffix'|trans }}">
    <meta name="twitter:description" content="{{ 'home.meta.description'|trans }}">
    <meta name="twitter:image" content="{{ absolute_url(asset('img/logo/postchat-logo-512-min.png')) }}">
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('build/tailwind.css') }}">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, rgba(255,0,0,0.05) 0%, rgba(255,255,255,0) 100%);
        }

        .text-gradient {
            background: linear-gradient(90deg, #ff0000, #ff5e5e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .circle-text {
            animation: spin 20s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        .blob-animation {
            animation: blob 8s ease-in-out infinite;
        }

        @keyframes blob {
            0%, 100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
            25% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
            50% { border-radius: 50% 60% 30% 60% / 40% 30% 70% 50%; }
            75% { border-radius: 40% 60% 70% 30% / 60% 40% 30% 70%; }
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Intersection Observer for scroll animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('active');
                    }
                });
            }, {
                threshold: 0.1
            });

            document.querySelectorAll('.animate-on-scroll').forEach(element => {
                observer.observe(element);
            });

            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Language switcher
            const languageSwitcher = document.getElementById('language-switcher');
            const mobileLangSwitcher = document.getElementById('mobile-language-switcher');

            if (languageSwitcher) {
                languageSwitcher.addEventListener('change', function() {
                    const selectedLang = this.value;
                    const url = new URL(window.location.href);
                    url.searchParams.set('lang', selectedLang);
                    window.location.href = url.toString();
                });
            }

            if (mobileLangSwitcher) {
                mobileLangSwitcher.addEventListener('change', function() {
                    const selectedLang = this.value;
                    const url = new URL(window.location.href);
                    url.searchParams.set('lang', selectedLang);
                    window.location.href = url.toString();
                });
            }

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    if (targetId === '#') return;

                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 80,
                            behavior: 'smooth'
                        });

                        // Close mobile menu if open
                        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                            mobileMenu.classList.add('hidden');
                        }
                    }
                });
            });
        });
    </script>
{% endblock %}

{% block body %}
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white shadow-md">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <a href="{{ path('app_main_index') }}" class="flex items-center">
                    <img src="{{ asset('img/logo/postchat-logo-512-min.png') }}" alt="PostCHAT" class="h-10">
                </a>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-6">
                    <a href="#home" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.home'|trans }}</a>
                    <a href="#about" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.about'|trans }}</a>
                    <a href="#features" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.features'|trans }}</a>
                    <a href="#faq" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.faq'|trans }}</a>

                    <!-- Language Selector -->
                    <select id="language-switcher" class="form-select rounded-md border-gray-300 focus:border-postchat-red focus:ring focus:ring-red-200">
                        <option value="en" {{ app.request.locale == 'en' ? 'selected' : '' }}>English</option>
                        <option value="tr" {{ app.request.locale == 'tr' ? 'selected' : '' }}>Türkçe</option>
                    </select>

                    <!-- CTA Button -->
                    <button class="btn-primary" data-bs-toggle="modal" data-bs-target="#connectModal">
                        {{ 'header.menu.button'|trans }}
                    </button>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="md:hidden text-gray-500 hover:text-postchat-red">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
                <nav class="flex flex-col space-y-4">
                    <a href="#home" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.home'|trans }}</a>
                    <a href="#about" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.about'|trans }}</a>
                    <a href="#features" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.features'|trans }}</a>
                    <a href="#faq" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.faq'|trans }}</a>

                    <div class="flex items-center space-x-4">
                        <select id="mobile-language-switcher" class="form-select rounded-md border-gray-300 focus:border-postchat-red focus:ring focus:ring-red-200">
                            <option value="en" {{ app.request.locale == 'en' ? 'selected' : '' }}>English</option>
                            <option value="tr" {{ app.request.locale == 'tr' ? 'selected' : '' }}>Türkçe</option>
                        </select>

                        <button class="btn-primary" data-bs-toggle="modal" data-bs-target="#connectModal">
                            {{ 'header.menu.button'|trans }}
                        </button>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section id="home" class="pt-32 pb-20 md:pt-40 md:pb-24 gradient-bg">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div class="animate-on-scroll">
                        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                            {{ 'home.hero.title'|trans }}
                        </h1>
                        <p class="text-xl text-gray-600 mb-8">
                            {{ 'home.hero.subtitle'|trans }}
                        </p>
                        <div class="flex flex-wrap gap-4">
                            <a href="#about" class="btn-primary">
                                {{ 'home.hero.button'|trans }} <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                            <a href="{{ path('app_download') }}" class="btn-dark relative">
                                <i class="fab fa-android mr-2"></i> {{ 'home.app.button'|trans }}
                                <span class="absolute -top-2 -right-2 bg-yellow-400 text-xs text-black px-2 py-1 rounded-full">
                                    {{ 'home.app.prototype_warning'|trans }}
                                </span>
                            </a>
                        </div>
                    </div>
                    <div class="relative animate-on-scroll">
                        <div class="absolute top-0 right-0 -z-10 w-72 h-72 bg-red-100 rounded-full opacity-30 blur-3xl"></div>
                        <img src="{{ asset('img/artwork/hero-min.png') }}" alt="PostCHAT Platform" class="w-full h-auto rounded-lg shadow-xl">

                        <!-- Circular Text -->
                        <div class="absolute -bottom-6 -right-6 w-24 h-24 bg-white rounded-full shadow-lg flex items-center justify-center">
                            <div class="circle-text w-full h-full flex items-center justify-center">
                                <svg viewBox="0 0 100 100" width="100%" height="100%">
                                    <path id="circle" d="M 50, 50 m -37, 0 a 37,37 0 1,1 74,0 a 37,37 0 1,1 -74,0" fill="none"/>
                                    <text font-size="7.5">
                                        <textPath href="#circle">PostCHAT • Logistics • AI • Sell • PostCHAT •</textPath>
                                    </text>
                                </svg>
                            </div>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section id="about" class="py-20 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="text-center mb-16 animate-on-scroll">
                    <h2 class="section-title">{{ 'home.about.title'|trans }}</h2>
                </div>

                <!-- Item 1 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-16 mb-20">
                    <div class="animate-on-scroll">
                        <div class="relative">
                            <div class="absolute -z-10 top-10 -left-10 w-32 h-32 bg-red-100 rounded-full opacity-50 blob-animation"></div>
                            <img src="{{ asset('img/artwork/sc-3-min.png') }}" alt="Automation" class="w-full h-auto rounded-lg shadow-lg">
                        </div>
                    </div>
                    <div class="flex flex-col justify-center animate-on-scroll">
                        <span class="text-postchat-red font-semibold mb-2">{{ 'home.about.item.1.subtitle'|trans }}</span>
                        <h3 class="text-3xl font-bold mb-4">{{ 'home.about.item.1.title'|trans }}</h3>
                        <p class="text-gray-600">{{ 'home.about.item.1.text'|trans }}</p>
                    </div>
                </div>

                <!-- Item 2 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-16 mb-20">
                    <div class="order-2 md:order-1 flex flex-col justify-center animate-on-scroll">
                        <span class="text-postchat-red font-semibold mb-2">{{ 'home.about.item.2.subtitle'|trans }}</span>
                        <h3 class="text-3xl font-bold mb-4">{{ 'home.about.item.2.title'|trans }}</h3>
                        <p class="text-gray-600">{{ 'home.about.item.2.text'|trans }}</p>
                    </div>
                    <div class="order-1 md:order-2 animate-on-scroll">
                        <div class="relative">
                            <div class="absolute -z-10 bottom-10 -right-10 w-32 h-32 bg-red-100 rounded-full opacity-50 blob-animation"></div>
                            <img src="{{ asset('img/artwork/sc-4-min.png') }}" alt="Customer Satisfaction" class="w-full h-auto rounded-lg shadow-lg">
                        </div>
                    </div>
                </div>

                <!-- Item 3 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-16 mb-20">
                    <div class="animate-on-scroll">
                        <div class="relative">
                            <div class="absolute -z-10 top-10 -left-10 w-32 h-32 bg-red-100 rounded-full opacity-50 blob-animation"></div>
                            <img src="{{ asset('img/artwork/sc-3-min.png') }}" alt="AI Operator" class="w-full h-auto rounded-lg shadow-lg">
                        </div>
                    </div>
                    <div class="flex flex-col justify-center animate-on-scroll">
                        <span class="text-postchat-red font-semibold mb-2">{{ 'home.about.item.3.subtitle'|trans }}</span>
                        <h3 class="text-3xl font-bold mb-4">{{ 'home.about.item.3.title'|trans }}</h3>
                        <p class="text-gray-600">{{ 'home.about.item.3.text'|trans }}</p>
                    </div>
                </div>

                <!-- Item 4 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-16 mb-20">
                    <div class="order-2 md:order-1 flex flex-col justify-center animate-on-scroll">
                        <span class="text-postchat-red font-semibold mb-2">{{ 'home.about.item.4.subtitle'|trans }}</span>
                        <h3 class="text-3xl font-bold mb-4">{{ 'home.about.item.4.title'|trans }}</h3>
                        <p class="text-gray-600">{{ 'home.about.item.4.text'|trans }}</p>
                    </div>
                    <div class="order-1 md:order-2 animate-on-scroll">
                        <div class="relative">
                            <div class="absolute -z-10 bottom-10 -right-10 w-32 h-32 bg-red-100 rounded-full opacity-50 blob-animation"></div>
                            <img src="{{ asset('img/artwork/sc-4-min.png') }}" alt="Customer Satisfaction" class="w-full h-auto rounded-lg shadow-lg">
                        </div>
                    </div>
                </div>

                <!-- Item 5 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-16">
                    <div class="animate-on-scroll">
                        <div class="relative">
                            <div class="absolute -z-10 top-10 -left-10 w-32 h-32 bg-red-100 rounded-full opacity-50 blob-animation"></div>
                            <img src="{{ asset('img/artwork/sc-3-min.png') }}" alt="AI Operator" class="w-full h-auto rounded-lg shadow-lg">
                        </div>
                    </div>
                    <div class="flex flex-col justify-center animate-on-scroll">
                        <span class="text-postchat-red font-semibold mb-2">{{ 'home.about.item.5.subtitle'|trans }}</span>
                        <h3 class="text-3xl font-bold mb-4">{{ 'home.about.item.5.title'|trans }}</h3>
                        <p class="text-gray-600">{{ 'home.about.item.5.text'|trans }}</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Features Section -->
        <section id="features" class="py-20">
            <div class="container mx-auto px-4">
                <div class="text-center mb-16 animate-on-scroll">
                    <h2 class="section-title">{{ 'home.features.title'|trans }}</h2>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- Feature 1 -->
                    <div class="feature-card animate-on-scroll">
                        <div class="feature-icon">
                            <img src="{{ asset('img/icons/languages.png') }}" alt="Multi-channel" class="w-8 h-8">
                        </div>
                        <h3 class="text-xl font-bold mb-3">{{ 'home.features.item.1.title'|trans }}</h3>
                        <p class="text-gray-600">{{ 'home.features.item.1.subtitle'|trans }}</p>
                    </div>

                    <!-- Feature 2 -->
                    <div class="feature-card animate-on-scroll">
                        <div class="feature-icon">
                            <img src="{{ asset('img/icons/quality.png') }}" alt="AI capabilities" class="w-8 h-8">
                        </div>
                        <h3 class="text-xl font-bold mb-3">{{ 'home.features.item.2.title'|trans }}</h3>
                        <p class="text-gray-600">{{ 'home.features.item.2.subtitle'|trans }}</p>
                    </div>

                    <!-- Feature 3 -->
                    <div class="feature-card animate-on-scroll">
                        <div class="feature-icon">
                            <img src="{{ asset('img/icons/online-shop.png') }}" alt="E-commerce" class="w-8 h-8">
                        </div>
                        <h3 class="text-xl font-bold mb-3">{{ 'home.features.item.3.title'|trans }}</h3>
                        <p class="text-gray-600">{{ 'home.features.item.3.subtitle'|trans }}</p>
                    </div>

                    <!-- Feature 4 -->
                    <div class="feature-card animate-on-scroll">
                        <div class="feature-icon">
                            <img src="{{ asset('img/icons/stats.png') }}" alt="Business tools" class="w-8 h-8">
                        </div>
                        <h3 class="text-xl font-bold mb-3">{{ 'home.features.item.4.title'|trans }}</h3>
                        <p class="text-gray-600">{{ 'home.features.item.4.subtitle'|trans }}</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="py-20 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Stat 1 -->
                    <div class="bg-white p-8 rounded-xl shadow-lg text-center animate-on-scroll">
                        <div class="text-5xl font-bold text-postchat-red mb-4">4,000+</div>
                        <h3 class="text-xl font-bold">{{ 'home.numbers.item.1.title'|trans }}</h3>
                    </div>

                    <!-- Stat 2 -->
                    <div class="bg-white p-8 rounded-xl shadow-lg text-center animate-on-scroll">
                        <div class="text-5xl font-bold text-postchat-red mb-4">2,000+</div>
                        <h3 class="text-xl font-bold">{{ 'home.numbers.item.2.title'|trans }}</h3>
                    </div>

                    <!-- Stat 3 -->
                    <div class="bg-white p-8 rounded-xl shadow-lg text-center animate-on-scroll">
                        <div class="text-5xl font-bold text-postchat-red mb-4">24/7</div>
                        <h3 class="text-xl font-bold">{{ 'home.numbers.item.3.title'|trans }}</h3>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="py-20">
            <div class="container mx-auto px-4">
                <div class="text-center mb-16 animate-on-scroll">
                    <h2 class="section-title">{{ 'home.faq.title'|trans }}</h2>
                </div>

                <div class="max-w-3xl mx-auto">
                    <div class="space-y-6">
                        <!-- FAQ Item 1 -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden animate-on-scroll">
                            <button class="flex justify-between items-center w-full p-6 text-left" onclick="toggleFaq(this)">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.1.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.1.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 2 -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden animate-on-scroll">
                            <button class="flex justify-between items-center w-full p-6 text-left" onclick="toggleFaq(this)">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.2.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.2.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 3 -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden animate-on-scroll">
                            <button class="flex justify-between items-center w-full p-6 text-left" onclick="toggleFaq(this)">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.3.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.3.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 4 -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden animate-on-scroll">
                            <button class="flex justify-between items-center w-full p-6 text-left" onclick="toggleFaq(this)">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.4.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.4.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 5 -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden animate-on-scroll">
                            <button class="flex justify-between items-center w-full p-6 text-left" onclick="toggleFaq(this)">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.5.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.5.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 6 -->
                        <div class="bg-white rounded-lg shadow-md overflow-hidden animate-on-scroll">
                            <button class="flex justify-between items-center w-full p-6 text-left" onclick="toggleFaq(this)">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.6.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.6.text'|trans }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 bg-postchat-red text-white">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto text-center animate-on-scroll">
                    <h2 class="text-4xl md:text-5xl font-bold mb-8">{{ 'home.cta.title'|trans }}</h2>
                    <div class="flex flex-wrap justify-center gap-4">
                        <button class="bg-white text-postchat-red font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-all duration-300" data-bs-toggle="modal" data-bs-target="#connectModal">
                            {{ 'home.cta.button'|trans }}
                        </button>
                        <a href="{{ path('app_download') }}" class="bg-postchat-dark text-white font-bold py-3 px-8 rounded-lg hover:bg-gray-900 transition-all duration-300 relative">
                            <i class="fab fa-android mr-2"></i> {{ 'home.app.button'|trans }}
                            <span class="absolute -top-2 -right-2 bg-yellow-400 text-xs text-black px-2 py-1 rounded-full">
                                {{ 'home.app.prototype_warning'|trans }}
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="py-16 bg-gray-900 text-white">
            <div class="container mx-auto px-4">
                <div class="max-w-lg mx-auto text-center mb-12">
                    <a href="{{ path('app_main_index') }}">
                        <img src="{{ asset('img/logo/postchat-logo-512-min.png') }}" alt="PostCHAT" class="h-20 mx-auto mb-6">
                    </a>
                    <p class="text-gray-400 mb-8">{{ 'home.footer.text'|trans }}</p>
                    <div class="flex justify-center space-x-6">
                        <a href="{{ path('app_download') }}" class="text-white hover:text-postchat-red transition-colors relative">
                            <i class="fab fa-android text-xl"></i>
                            <span class="ml-2">{{ 'home.app.button'|trans }}</span>
                            <span class="absolute -top-2 -right-2 bg-yellow-400 text-xs text-black px-1 py-0.5 rounded-full">
                                {{ 'home.app.prototype_warning'|trans }}
                            </span>
                        </a>
                        <a href="https://my.terra.marketing/webchat/?p=1788388" target="_blank" class="text-white hover:text-postchat-red transition-colors">
                            <i class="fa fa-globe text-xl"></i>
                            <span class="ml-2">WebChat</span>
                        </a>
                    </div>
                </div>
                <div class="border-t border-gray-800 pt-8">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <p class="text-gray-500">&copy; {{ 'now'|date('Y') }} PostChat. {{ 'home.footer.copyright'|trans }}</p>
                        <div class="flex space-x-6 mt-4 md:mt-0">
                            <a href="#" class="text-gray-500 hover:text-white transition-colors">{{ 'home.footer.privacy'|trans }}</a>
                            <a href="https://api.post-chat.com/api/docs" target="_blank" class="text-gray-500 hover:text-white transition-colors">{{ 'home.footer.documentation'|trans }}</a>
                            <a href="#home" class="text-gray-500 hover:text-white transition-colors">
                                <i class="fas fa-arrow-up"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>

        <!-- Connect Modal -->
        <div class="modal fade" id="connectModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title font-bold text-xl">{{ 'header.modal.title'|trans }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <p class="mb-6">{{ 'header.modal.text'|trans }}</p>
                        <div class="flex justify-center space-x-4 mb-6">
                            <a href="https://my.terra.marketing/webchat/?p=1788388" target="_blank" class="btn-primary">
                                <i class="fa fa-globe mr-2"></i> WebChat
                            </a>
                        </div>
                        <p class="text-sm text-gray-500">{{ 'header.modal.terms'|trans({'privacy_url': 'https://privacy-policy.terra.marketing'})|raw }}</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        function toggleFaq(button) {
            const content = button.nextElementSibling;
            const icon = button.querySelector('svg');

            content.classList.toggle('hidden');
            icon.classList.toggle('rotate-180');
        }
    </script>
{% endblock %}
