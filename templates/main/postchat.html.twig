{% extends 'base.html.twig' %}

{% block title %}PostCHAT - {{ 'home.meta.title_suffix'|trans }}{% endblock %}

{% block meta %}
    <meta name="description" content="PostCHAT - A modern platform for borderless, stock-free, and staff-free sales. Connect your customers to trusted global suppliers and build a successful online business.">
    <meta name="keywords" content="dropshipping, e-commerce, online business, social selling, multichannel, AI marketing, global suppliers, PostCHAT, Terra">
    <meta property="og:title" content="PostCHAT - Smart Sales Platform">
    <meta property="og:description" content="A modern platform for borderless, stock-free, and staff-free sales. Connect your customers to trusted global suppliers.">
    <meta property="og:image" content="{{ absolute_url(asset('img/logo/postchat-logo-512-min.png')) }}">
    <meta property="og:url" content="{{ app.request.uri }}">
    <meta property="og:type" content="website">
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="PostCHAT - Smart Sales Platform">
    <meta name="twitter:description" content="A modern platform for borderless, stock-free, and staff-free sales. Connect your customers to trusted global suppliers.">
    <meta name="twitter:image" content="{{ absolute_url(asset('img/logo/postchat-logo-512-min.png')) }}">
{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Lexend:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('build/tailwind.css') }}">
    <style>
        .gradient-bg {
            background: radial-gradient(circle at 10% 20%, rgba(255, 0, 0, 0.05) 0%, rgba(255, 255, 255, 0) 90%);
        }

        .gradient-text {
            background: linear-gradient(90deg, #ff0000, #ff5e5e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .floating {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }

        .blob {
            animation: blob 8s ease-in-out infinite;
        }

        @keyframes blob {
            0%, 100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
            25% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
            50% { border-radius: 50% 60% 30% 60% / 40% 30% 70% 50%; }
            75% { border-radius: 40% 60% 70% 30% / 60% 40% 30% 70%; }
        }

        .scroll-indicator {
            animation: scroll 2s ease-in-out infinite;
        }

        @keyframes scroll {
            0%, 100% { transform: translateY(0); opacity: 0.8; }
            50% { transform: translateY(10px); opacity: 0.4; }
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
        }

        .noise-bg {
            background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
            background-blend-mode: overlay;
            background-size: 200px;
            opacity: 0.05;
        }
    </style>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Intersection Observer for scroll animations
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('active');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -100px 0px'
            });

            document.querySelectorAll('.animate-on-scroll, .animate-fade-in, .animate-scale-in').forEach(element => {
                observer.observe(element);
            });

            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuButton && mobileMenu) {
                mobileMenuButton.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }

            // Language switcher
            const languageSwitcher = document.getElementById('language-switcher');
            const mobileLangSwitcher = document.getElementById('mobile-language-switcher');

            if (languageSwitcher) {
                languageSwitcher.addEventListener('change', function() {
                    const selectedLang = this.value;
                    const url = new URL(window.location.href);
                    url.searchParams.set('lang', selectedLang);
                    window.location.href = url.toString();
                });
            }

            if (mobileLangSwitcher) {
                mobileLangSwitcher.addEventListener('change', function() {
                    const selectedLang = this.value;
                    const url = new URL(window.location.href);
                    url.searchParams.set('lang', selectedLang);
                    window.location.href = url.toString();
                });
            }

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();

                    const targetId = this.getAttribute('href');
                    if (targetId === '#') return;

                    const targetElement = document.querySelector(targetId);

                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 80,
                            behavior: 'smooth'
                        });

                        // Close mobile menu if open
                        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
                            mobileMenu.classList.add('hidden');
                        }
                    }
                });
            });

            // FAQ accordion
            document.querySelectorAll('.faq-toggle').forEach(button => {
                button.addEventListener('click', function() {
                    const content = this.nextElementSibling;
                    const icon = this.querySelector('svg');

                    content.classList.toggle('hidden');
                    icon.classList.toggle('rotate-180');
                });
            });

            // Counter animation
            document.querySelectorAll('.counter').forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const duration = 2000; // ms
                const step = target / (duration / 16); // 60fps
                let current = 0;

                const updateCounter = () => {
                    current += step;
                    if (current < target) {
                        counter.textContent = Math.ceil(current).toLocaleString();
                        requestAnimationFrame(updateCounter);
                    } else {
                        counter.textContent = target.toLocaleString();
                    }
                };

                const counterObserver = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            updateCounter();
                            counterObserver.unobserve(entry.target);
                        }
                    });
                }, { threshold: 0.5 });

                counterObserver.observe(counter);
            });
        });
    </script>
{% endblock %}

{% block body %}
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-white/90 backdrop-blur-md shadow-sm">
        <div class="container mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <!-- Logo -->
                <a href="{{ path('app_main_index') }}" class="flex items-center">
                    <img src="{{ asset('img/logo/postchat-logo-512-min.png') }}" alt="PostCHAT" class="h-10">
                </a>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-6">
                    <a href="#home" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.home'|trans }}</a>
                    <a href="#about" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.about'|trans }}</a>
                    <a href="#features" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.features'|trans }}</a>
                    <a href="#how-it-works" class="font-medium hover:text-postchat-red transition-colors">How It Works</a>
                    <a href="#faq" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.faq'|trans }}</a>

                    <!-- Language Selector -->
                    <select id="language-switcher" class="form-select rounded-md border-gray-300 focus:border-postchat-red focus:ring focus:ring-red-200">
                        <option value="en" {{ app.request.locale == 'en' ? 'selected' : '' }}>English</option>
                        <option value="tr" {{ app.request.locale == 'tr' ? 'selected' : '' }}>Türkçe</option>
                    </select>

                    <!-- CTA Button -->
                    <button class="btn-primary" data-bs-toggle="modal" data-bs-target="#connectModal">
                        {{ 'header.menu.button'|trans }}
                    </button>
                </nav>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="md:hidden text-gray-500 hover:text-postchat-red">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden mt-4 pb-4">
                <nav class="flex flex-col space-y-4">
                    <a href="#home" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.home'|trans }}</a>
                    <a href="#about" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.about'|trans }}</a>
                    <a href="#features" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.features'|trans }}</a>
                    <a href="#how-it-works" class="font-medium hover:text-postchat-red transition-colors">How It Works</a>
                    <a href="#faq" class="font-medium hover:text-postchat-red transition-colors">{{ 'header.menu.faq'|trans }}</a>

                    <div class="flex items-center space-x-4">
                        <select id="mobile-language-switcher" class="form-select rounded-md border-gray-300 focus:border-postchat-red focus:ring focus:ring-red-200">
                            <option value="en" {{ app.request.locale == 'en' ? 'selected' : '' }}>English</option>
                            <option value="tr" {{ app.request.locale == 'tr' ? 'selected' : '' }}>Türkçe</option>
                        </select>

                        <button class="btn-primary" data-bs-toggle="modal" data-bs-target="#connectModal">
                            {{ 'header.menu.button'|trans }}
                        </button>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <main>
        <!-- Hero Section -->
        <section id="home" class="pt-32 pb-20 md:pt-40 md:pb-32 relative overflow-hidden">
            <div class="absolute inset-0 gradient-bg"></div>
            <div class="absolute inset-0 noise-bg"></div>

            <div class="container mx-auto px-4 relative">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div class="animate-fade-in">
                        <span class="inline-block px-4 py-2 rounded-full bg-postchat-light-red text-postchat-red font-medium text-sm mb-6">Smart Sales Platform</span>
                        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
                            Borderless, <span class="gradient-text">Stock-Free</span>, Staff-Free Sales
                        </h1>
                        <p class="text-xl text-gray-600 mb-8">
                            Create an online business within a day, connecting your customers to trusted global suppliers. Start selling without inventory or staff.
                        </p>
                        <div class="flex flex-wrap gap-4">
                            <a href="#about" class="btn-primary">
                                Learn More <i class="fas fa-arrow-right ml-2"></i>
                            </a>
                            <a href="{{ path('app_download') }}" class="btn-dark relative">
                                <i class="fab fa-android mr-2"></i> {{ 'home.app.button'|trans }}
                                <span class="absolute -top-2 -right-2 bg-yellow-400 text-xs text-black px-2 py-1 rounded-full">
                                    {{ 'home.app.prototype_warning'|trans }}
                                </span>
                            </a>
                        </div>

                        <div class="mt-8 flex items-center space-x-4 text-sm text-gray-500">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>No Inventory</span>
                            </div>
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>No Staff</span>
                            </div>
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>No Risk</span>
                            </div>
                        </div>
                    </div>

                    <div class="relative animate-scale-in">
                        <!-- Decorative elements -->
                        <div class="absolute -z-10 top-1/4 -right-10 w-40 h-40 bg-postchat-light-red rounded-full opacity-30 blur-3xl"></div>
                        <div class="absolute -z-10 bottom-1/4 -left-10 w-40 h-40 bg-blue-100 rounded-full opacity-30 blur-3xl"></div>

                        <!-- Main image -->
                        <div class="relative rounded-2xl overflow-hidden shadow-red-lg">
                            <img src="{{ asset('img/artwork/hero-min.png') }}" alt="PostCHAT Platform" class="w-full h-auto">

                            <!-- Floating badge -->
                            <div class="absolute -top-5 -right-5 bg-white rounded-xl shadow-lg p-3 floating">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 rounded-full bg-postchat-red flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <span class="font-bold text-sm">5,000+ Active Businesses</span>
                                </div>
                            </div>
                        </div>

                        <!-- Scroll indicator -->
                        <div class="hidden md:flex absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full items-center flex-col mt-8 scroll-indicator">
                            <span class="text-sm text-gray-500 mb-2">Scroll to explore</span>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- What is Dropshipping Section -->
        <section id="about" class="py-20 md:py-28 bg-gray-50 relative overflow-hidden">
            <div class="absolute inset-0 noise-bg"></div>

            <div class="container mx-auto px-4 relative">
                <div class="text-center mb-16 animate-fade-in">
                    <span class="inline-block px-4 py-2 rounded-full bg-postchat-light-red text-postchat-red font-medium text-sm mb-4">About PostCHAT</span>
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">What is Dropshipping?</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Dropshipping is a modern business model that allows you to sell products online without the need to maintain inventory. You simply connect customers with suppliers and profit from the intermediation.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                    <!-- Benefit 1 -->
                    <div class="feature-card animate-on-scroll card-hover">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4">Zero Inventory</h3>
                        <p class="text-gray-600">
                            You do not physically hold merchandise in a warehouse. Your suppliers store, package, and ship the products directly to your customers. This saves you space and logistics costs.
                        </p>
                    </div>

                    <!-- Benefit 2 -->
                    <div class="feature-card animate-on-scroll card-hover" style="transition-delay: 100ms;">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4">Zero Staff</h3>
                        <p class="text-gray-600">
                            The model operates without the need for additional employees. The automated platform and suppliers handle most of the processes, allowing you to focus on marketing and business expansion.
                        </p>
                    </div>

                    <!-- Benefit 3 -->
                    <div class="feature-card animate-on-scroll card-hover" style="transition-delay: 200ms;">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4">Zero Risk</h3>
                        <p class="text-gray-600">
                            Dropshipping eliminates the risk of unsold merchandise. Since you only purchase products after your customers have ordered and paid for them, you are left with no unsold or obsolete inventory.
                        </p>
                    </div>

                    <!-- Benefit 4 -->
                    <div class="feature-card animate-on-scroll card-hover" style="transition-delay: 300ms;">
                        <div class="feature-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4">Guaranteed Profit</h3>
                        <p class="text-gray-600">
                            You benefit from the price difference. You buy at a lower price from the supplier and sell at a higher price to your customer. This difference is your pure profit, without additional expenses.
                        </p>
                    </div>
                </div>

                <div class="bg-white rounded-2xl shadow-custom p-8 md:p-12 animate-scale-in">
                    <div class="flex flex-col md:flex-row items-center">
                        <div class="md:w-1/2 mb-8 md:mb-0 md:pr-8">
                            <img src="{{ asset('img/artwork/sc-3-min.png') }}" alt="PostCHAT Platform" class="w-full h-auto rounded-xl shadow-sm">
                        </div>
                        <div class="md:w-1/2 md:pl-8">
                            <h3 class="text-2xl md:text-3xl font-bold mb-6">Start Your Online Business Today</h3>
                            <p class="text-gray-600 mb-6">
                                With dropshipping, you can start an online business with minimal investment and quickly expand into different global markets, offering hundreds of products without logistical complications.
                            </p>
                            <ul class="space-y-4">
                                <li class="flex items-start">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>Access to thousands of products from trusted suppliers</span>
                                </li>
                                <li class="flex items-start">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>Automated order processing and fulfillment</span>
                                </li>
                                <li class="flex items-start">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>Integration with social networks for targeted marketing</span>
                                </li>
                                <li class="flex items-start">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red mr-2 flex-shrink-0 mt-0.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>Real-time analytics and performance tracking</span>
                                </li>
                            </ul>
                            <div class="mt-8">
                                <button class="btn-primary" data-bs-toggle="modal" data-bs-target="#connectModal">
                                    Get Started Now
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works Section -->
        <section id="how-it-works" class="py-20 md:py-28 relative overflow-hidden">
            <div class="absolute inset-0 noise-bg"></div>

            <div class="container mx-auto px-4 relative">
                <div class="text-center mb-16 animate-fade-in">
                    <span class="inline-block px-4 py-2 rounded-full bg-postchat-light-red text-postchat-red font-medium text-sm mb-4">Simple Process</span>
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Dropshipping Operations and Sales</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Our platform makes it easy to start and run your dropshipping business with a simple, streamlined process.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
                    <!-- Step 1 -->
                    <div class="step-card animate-on-scroll">
                        <div class="step-number">1</div>
                        <h3 class="text-xl font-bold mb-4 mt-4">Select Products</h3>
                        <p class="text-gray-600 mb-4">
                            Enter the platform, go to the Dropshipping module, and identify categories that fit your target market.
                        </p>
                        <ul class="space-y-2 text-sm text-gray-500">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Browse thousands of products</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>View detailed specifications</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Click "I want to sell" to add to inventory</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Step 2 -->
                    <div class="step-card animate-on-scroll" style="transition-delay: 200ms;">
                        <div class="step-number">2</div>
                        <h3 class="text-xl font-bold mb-4 mt-4">Customize Details</h3>
                        <p class="text-gray-600 mb-4">
                            Modify the description, price, and labels according to your marketing strategy and target audience.
                        </p>
                        <ul class="space-y-2 text-sm text-gray-500">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Set your own pricing strategy</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Customize product descriptions</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Choose from 20+ languages</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Step 3 -->
                    <div class="step-card animate-on-scroll" style="transition-delay: 400ms;">
                        <div class="step-number">3</div>
                        <h3 class="text-xl font-bold mb-4 mt-4">Publish & Monitor</h3>
                        <p class="text-gray-600 mb-4">
                            Press the "Publish" button and follow sales statistics and customer feedback to optimize your offerings over time.
                        </p>
                        <ul class="space-y-2 text-sm text-gray-500">
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Publish across multiple channels</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Track real-time performance</span>
                            </li>
                            <li class="flex items-start">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                                <span>Optimize based on analytics</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="flex justify-center">
                    <button class="btn-primary" data-bs-toggle="modal" data-bs-target="#connectModal">
                        Start Selling Today
                    </button>
                </div>
            </div>
        </section>

        <!-- AI Features Section -->
        <section id="features" class="py-20 md:py-28 bg-gray-50 relative overflow-hidden">
            <div class="absolute inset-0 noise-bg"></div>

            <div class="container mx-auto px-4 relative">
                <div class="text-center mb-16 animate-fade-in">
                    <span class="inline-block px-4 py-2 rounded-full bg-postchat-light-red text-postchat-red font-medium text-sm mb-4">AI-Powered</span>
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">AI-Generated Social Media Posts</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Our platform uses artificial intelligence to automatically generate professional and effective social media posts. The process is simple and fast, saving you valuable time.
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
                    <div class="order-2 lg:order-1 animate-on-scroll">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Feature 1 -->
                            <div class="bg-white p-6 rounded-xl shadow-custom border border-gray-100 card-hover">
                                <div class="w-12 h-12 rounded-full bg-postchat-light-red flex items-center justify-center mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-bold mb-2">Photo</h3>
                                <p class="text-gray-600 text-sm">
                                    Use the product photo from your supplier or your personal gallery. The system automatically optimizes the quality and resolution.
                                </p>
                            </div>

                            <!-- Feature 2 -->
                            <div class="bg-white p-6 rounded-xl shadow-custom border border-gray-100 card-hover">
                                <div class="w-12 h-12 rounded-full bg-postchat-light-red flex items-center justify-center mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-bold mb-2">Design</h3>
                                <p class="text-gray-600 text-sm">
                                    Generates marketing designs based on current trends and market analysis. AI selects colors, fonts, and graphic elements.
                                </p>
                            </div>

                            <!-- Feature 3 -->
                            <div class="bg-white p-6 rounded-xl shadow-custom border border-gray-100 card-hover">
                                <div class="w-12 h-12 rounded-full bg-postchat-light-red flex items-center justify-center mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-bold mb-2">Description</h3>
                                <p class="text-gray-600 text-sm">
                                    Creates engaging and persuasive text in the language of the market you are targeting, with SEO-optimized keywords.
                                </p>
                            </div>

                            <!-- Feature 4 -->
                            <div class="bg-white p-6 rounded-xl shadow-custom border border-gray-100 card-hover">
                                <div class="w-12 h-12 rounded-full bg-postchat-light-red flex items-center justify-center mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-bold mb-2">Post</h3>
                                <p class="text-gray-600 text-sm">
                                    Ready to publish on all social networks. You can schedule posts for maximum activity times and get detailed statistics.
                                </p>
                            </div>
                        </div>

                        <div class="mt-8">
                            <button class="btn-primary" data-bs-toggle="modal" data-bs-target="#connectModal">
                                Try AI Features
                            </button>
                        </div>
                    </div>

                    <div class="order-1 lg:order-2 animate-on-scroll">
                        <div class="relative">
                            <div class="absolute -z-10 top-10 -right-10 w-32 h-32 bg-blue-100 rounded-full opacity-50 blob-animation"></div>
                            <img src="{{ asset('img/artwork/sc-4-min.png') }}" alt="AI-Generated Content" class="w-full h-auto rounded-xl shadow-lg">

                            <!-- Floating badge -->
                            <div class="absolute -bottom-5 -left-5 bg-white rounded-xl shadow-lg p-3 floating">
                                <div class="flex items-center space-x-2">
                                    <div class="w-8 h-8 rounded-full bg-postchat-red flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <span class="font-bold text-sm">AI-Powered Marketing</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-2xl shadow-custom p-8 md:p-12 animate-scale-in">
                    <div class="text-center mb-10">
                        <h3 class="text-2xl md:text-3xl font-bold mb-4">With our advanced AI system</h3>
                        <p class="text-gray-600 max-w-3xl mx-auto">
                            Every product gets a professional presentation without the need for designers or marketing specialists. This saves you valuable time and resources while simultaneously increasing the effectiveness of your digital marketing.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div class="text-center">
                            <div class="text-4xl font-bold text-postchat-red mb-2">20+</div>
                            <p class="text-gray-600">Languages supported</p>
                        </div>
                        <div class="text-center">
                            <div class="text-4xl font-bold text-postchat-red mb-2">5x</div>
                            <p class="text-gray-600">Faster content creation</p>
                        </div>
                        <div class="text-center">
                            <div class="text-4xl font-bold text-postchat-red mb-2">24/7</div>
                            <p class="text-gray-600">Automated operation</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Logistics & Shipping Section -->
        <section class="py-20 md:py-28 bg-gray-50 relative overflow-hidden">
            <div class="absolute inset-0 noise-bg"></div>

            <div class="container mx-auto px-4 relative">
                <div class="text-center mb-16 animate-fade-in">
                    <span class="inline-block px-4 py-2 rounded-full bg-postchat-light-red text-postchat-red font-medium text-sm mb-4">End-to-End Solution</span>
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Logistics, Shipping and Support</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Our integrated process ensures fast and transparent deliveries for our clients, with digital systems monitoring every step for quick resolution of any potential issues.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-12 mb-20">
                    <div class="animate-on-scroll">
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <!-- Process 1 -->
                            <div class="bg-white p-6 rounded-xl shadow-custom border border-gray-100 card-hover">
                                <div class="w-12 h-12 rounded-full bg-postchat-light-red flex items-center justify-center mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-bold mb-2">Order</h3>
                                <p class="text-gray-600 text-sm">
                                    Automatically sent to the supplier through our integrated system. All product details, quantities, and delivery addresses are transferred without manual intervention.
                                </p>
                            </div>

                            <!-- Process 2 -->
                            <div class="bg-white p-6 rounded-xl shadow-custom border border-gray-100 card-hover">
                                <div class="w-12 h-12 rounded-full bg-postchat-light-red flex items-center justify-center mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-bold mb-2">Preparation</h3>
                                <p class="text-gray-600 text-sm">
                                    The supplier marks "Ready" on the platform after verifying availability and preparing the products. This includes quality checking and secure packaging for transport.
                                </p>
                            </div>

                            <!-- Process 3 -->
                            <div class="bg-white p-6 rounded-xl shadow-custom border border-gray-100 card-hover">
                                <div class="w-12 h-12 rounded-full bg-postchat-light-red flex items-center justify-center mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-bold mb-2">Export</h3>
                                <p class="text-gray-600 text-sm">
                                    Shipments are prepared for export, including all necessary customs documentation. Products are grouped according to destination to optimize transportation costs and delivery times.
                                </p>
                            </div>

                            <!-- Process 4 -->
                            <div class="bg-white p-6 rounded-xl shadow-custom border border-gray-100 card-hover">
                                <div class="w-12 h-12 rounded-full bg-postchat-light-red flex items-center justify-center mb-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                    </svg>
                                </div>
                                <h3 class="text-lg font-bold mb-2">Notification</h3>
                                <p class="text-gray-600 text-sm">
                                    The customs agency and post office are informed about upcoming shipments. Clients receive a notification with the tracking number and estimated time of arrival for their order.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="animate-on-scroll" style="transition-delay: 200ms;">
                        <div class="bg-white p-8 rounded-xl shadow-custom border border-gray-100">
                            <h3 class="text-2xl font-bold mb-6">Tracking and Customer Notification</h3>
                            <p class="text-gray-600 mb-6">
                                Our advanced order tracking system provides full transparency and real-time information for our customers.
                            </p>

                            <div class="space-y-6">
                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-postchat-light-red flex items-center justify-center mr-4 flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold mb-1">Tracking</h4>
                                        <p class="text-gray-600">
                                            Customers can see the real-time location of their order through our dedicated app. Our platform provides detailed information on location, expected arrival time, and current delivery status.
                                        </p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-postchat-light-red flex items-center justify-center mr-4 flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold mb-1">Notification</h4>
                                        <p class="text-gray-600">
                                            The system automatically sends a unique delivery code message as soon as the order is ready for delivery. Notifications are sent via SMS and email, including information on the expected date and time of delivery.
                                        </p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-postchat-light-red flex items-center justify-center mr-4 flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold mb-1">Confirmation</h4>
                                        <p class="text-gray-600">
                                            After delivery, the customer has the opportunity to confirm receipt of the order and provide feedback. This data is used to continuously improve our service quality and address any potential concerns.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Security & Reporting Section -->
        <section class="py-20 md:py-28 relative overflow-hidden">
            <div class="absolute inset-0 noise-bg"></div>

            <div class="container mx-auto px-4 relative">
                <div class="text-center mb-16 animate-fade-in">
                    <span class="inline-block px-4 py-2 rounded-full bg-postchat-light-red text-postchat-red font-medium text-sm mb-4">Trust & Transparency</span>
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Reporting and Security</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Our platform provides integrated solutions for data security and detailed reporting. These functionalities are essential for your business and ensure maximum reliability in all processes.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                    <!-- Security Feature 1 -->
                    <div class="bg-white p-8 rounded-2xl shadow-custom text-center animate-on-scroll card-hover">
                        <div class="w-16 h-16 rounded-full bg-postchat-light-red flex items-center justify-center mx-auto mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4">Maximum Security</h3>
                        <p class="text-gray-600">
                            Data and payment protection with advanced encryption. Our system uses the latest security protocols to ensure that your data remains protected from any cyber threats.
                        </p>
                    </div>

                    <!-- Security Feature 2 -->
                    <div class="bg-white p-8 rounded-2xl shadow-custom text-center animate-on-scroll card-hover" style="transition-delay: 100ms;">
                        <div class="w-16 h-16 rounded-full bg-postchat-light-red flex items-center justify-center mx-auto mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4">Quality Control</h3>
                        <p class="text-gray-600">
                            Evaluation of suppliers and postal services through advanced methodologies. We continuously monitor the performance of our partners to ensure the highest service standards for your customers.
                        </p>
                    </div>

                    <!-- Security Feature 3 -->
                    <div class="bg-white p-8 rounded-2xl shadow-custom text-center animate-on-scroll card-hover" style="transition-delay: 200ms;">
                        <div class="w-16 h-16 rounded-full bg-postchat-light-red flex items-center justify-center mx-auto mb-6">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold mb-4">Comprehensive Reporting</h3>
                        <p class="text-gray-600">
                            Informing all parties with detailed and real-time reports. Generate analytical reports on business performance, customer behavior, and the efficiency of your logistics processes.
                        </p>
                    </div>
                </div>

                <div class="bg-white rounded-2xl shadow-custom p-8 md:p-12 animate-scale-in">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                        <div>
                            <h3 class="text-2xl md:text-3xl font-bold mb-6">Performance Monitoring</h3>
                            <p class="text-gray-600 mb-6">
                                Our advanced solution allows you to monitor and analyze all aspects of your digital business.
                            </p>

                            <div class="space-y-4">
                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-postchat-light-red flex items-center justify-center mr-4 flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold mb-1">Performance</h4>
                                        <p class="text-gray-600 text-sm">
                                            Monitor posts on every channel with detailed analysis of engagement, views and conversions on different platforms. Customized reporting based on the time periods you need.
                                        </p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-postchat-light-red flex items-center justify-center mr-4 flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold mb-1">Top Products</h4>
                                        <p class="text-gray-600 text-sm">
                                            Analyze top-performing products and identify sales trends and consumer preferences with detailed statistics. Compare performance across categories and different time periods.
                                        </p>
                                    </div>
                                </div>

                                <div class="flex items-start">
                                    <div class="w-10 h-10 rounded-full bg-postchat-light-red flex items-center justify-center mr-4 flex-shrink-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-postchat-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="text-lg font-bold mb-1">Competitor Analysis</h4>
                                        <p class="text-gray-600 text-sm">
                                            Monitor public profiles of competitors and compare your performance with key competitors. Discover their marketing strategies and identify new opportunities to grow your business.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="relative">
                            <div class="absolute -z-10 top-10 -right-10 w-32 h-32 bg-blue-100 rounded-full opacity-50 blob-animation"></div>
                            <img src="{{ asset('img/artwork/sc-3-min.png') }}" alt="Performance Monitoring" class="w-full h-auto rounded-xl shadow-lg">
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="py-20 md:py-28 bg-gray-50 relative overflow-hidden">
            <div class="absolute inset-0 noise-bg"></div>

            <div class="container mx-auto px-4 relative">
                <div class="text-center mb-16 animate-fade-in">
                    <span class="inline-block px-4 py-2 rounded-full bg-postchat-light-red text-postchat-red font-medium text-sm mb-4">By The Numbers</span>
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Strategic Advantages</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Our platform offers a complete business ecosystem that combines e-commerce, social networks, and data analytics to maximize your growth opportunities and reduce operational costs.
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                    <!-- Stat 1 -->
                    <div class="bg-white p-8 rounded-2xl shadow-custom text-center animate-on-scroll card-hover">
                        <div class="counter text-5xl font-bold text-postchat-red mb-4" data-target="5000">0</div>
                        <h3 class="text-xl font-bold mb-2">Active Businesses</h3>
                        <p class="text-gray-600 text-sm">
                            Automatic network visibility with high exposure. Immediate opportunities for collaboration and expansion.
                        </p>
                    </div>

                    <!-- Stat 2 -->
                    <div class="bg-white p-8 rounded-2xl shadow-custom text-center animate-on-scroll card-hover" style="transition-delay: 100ms;">
                        <div class="counter text-5xl font-bold text-postchat-red mb-4" data-target="145000">0</div>
                        <h3 class="text-xl font-bold mb-2">End Customers</h3>
                        <p class="text-gray-600 text-sm">
                            In the existing network with direct access. Ready and engaged audience that demands your products and services.
                        </p>
                    </div>

                    <!-- Stat 3 -->
                    <div class="bg-white p-8 rounded-2xl shadow-custom text-center animate-on-scroll card-hover" style="transition-delay: 200ms;">
                        <div class="counter text-5xl font-bold text-postchat-red mb-4" data-target="5000000">0</div>
                        <h3 class="text-xl font-bold mb-2">Users</h3>
                        <p class="text-gray-600 text-sm">
                            High-impact social media reach. Expand your reach through integrated social networks, leveraging the viral effect.
                        </p>
                    </div>

                    <!-- Stat 4 -->
                    <div class="bg-white p-8 rounded-2xl shadow-custom text-center animate-on-scroll card-hover" style="transition-delay: 300ms;">
                        <div class="text-5xl font-bold text-postchat-red mb-4">0</div>
                        <h3 class="text-xl font-bold mb-2">Advertising Cost</h3>
                        <p class="text-gray-600 text-sm">
                            On your end, saving on the marketing budget. Eliminate traditional advertising expenses with our innovative business model.
                        </p>
                    </div>
                </div>

                <div class="flex justify-center">
                    <button class="btn-primary" data-bs-toggle="modal" data-bs-target="#connectModal">
                        Join 5,000+ Businesses
                    </button>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section id="faq" class="py-20 md:py-28 bg-gray-50 relative overflow-hidden">
            <div class="absolute inset-0 noise-bg"></div>

            <div class="container mx-auto px-4 relative">
                <div class="text-center mb-16 animate-fade-in">
                    <span class="inline-block px-4 py-2 rounded-full bg-postchat-light-red text-postchat-red font-medium text-sm mb-4">Questions & Answers</span>
                    <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6">Frequently Asked Questions</h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        Find answers to the most common questions about PostCHAT and dropshipping.
                    </p>
                </div>

                <div class="max-w-3xl mx-auto">
                    <div class="space-y-4">
                        <!-- FAQ Item 1 -->
                        <div class="bg-white rounded-xl shadow-custom overflow-hidden animate-on-scroll">
                            <button class="faq-toggle flex justify-between items-center w-full p-6 text-left">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.1.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.1.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 2 -->
                        <div class="bg-white rounded-xl shadow-custom overflow-hidden animate-on-scroll" style="transition-delay: 100ms;">
                            <button class="faq-toggle flex justify-between items-center w-full p-6 text-left">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.2.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.2.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 3 -->
                        <div class="bg-white rounded-xl shadow-custom overflow-hidden animate-on-scroll" style="transition-delay: 200ms;">
                            <button class="faq-toggle flex justify-between items-center w-full p-6 text-left">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.3.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.3.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 4 -->
                        <div class="bg-white rounded-xl shadow-custom overflow-hidden animate-on-scroll" style="transition-delay: 300ms;">
                            <button class="faq-toggle flex justify-between items-center w-full p-6 text-left">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.4.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.4.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 5 -->
                        <div class="bg-white rounded-xl shadow-custom overflow-hidden animate-on-scroll" style="transition-delay: 400ms;">
                            <button class="faq-toggle flex justify-between items-center w-full p-6 text-left">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.5.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.5.text'|trans }}</p>
                            </div>
                        </div>

                        <!-- FAQ Item 6 -->
                        <div class="bg-white rounded-xl shadow-custom overflow-hidden animate-on-scroll" style="transition-delay: 500ms;">
                            <button class="faq-toggle flex justify-between items-center w-full p-6 text-left">
                                <h3 class="text-xl font-bold">{{ 'home.faq.item.6.title'|trans }}</h3>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-postchat-red transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                            <div class="px-6 pb-6 hidden">
                                <p class="text-gray-600">{{ 'home.faq.item.6.text'|trans }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-20 md:py-28 bg-postchat-red text-white relative overflow-hidden">
            <div class="absolute inset-0 noise-bg opacity-10"></div>

            <!-- Decorative elements -->
            <div class="absolute top-0 left-0 w-64 h-64 bg-white/10 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
            <div class="absolute bottom-0 right-0 w-64 h-64 bg-white/10 rounded-full blur-3xl translate-x-1/2 translate-y-1/2"></div>

            <div class="container mx-auto px-4 relative">
                <div class="max-w-4xl mx-auto text-center animate-scale-in">
                    <span class="inline-block px-4 py-2 rounded-full bg-white/20 text-white font-medium text-sm mb-6">Start Today</span>
                    <h2 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 text-shadow">{{ 'home.cta.title'|trans }}</h2>
                    <p class="text-xl text-white/80 mb-10 max-w-3xl mx-auto">
                        Join thousands of entrepreneurs already benefiting from our revolutionary platform to build successful online businesses.
                    </p>
                    <div class="flex flex-wrap justify-center gap-4">
                        <button class="bg-white text-postchat-red font-bold py-4 px-8 rounded-xl hover:bg-gray-100 transition-all duration-300 shadow-lg" data-bs-toggle="modal" data-bs-target="#connectModal">
                            {{ 'home.cta.button'|trans }} <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                        <a href="{{ path('app_download') }}" class="bg-postchat-dark text-white font-bold py-4 px-8 rounded-xl hover:bg-gray-900 transition-all duration-300 shadow-lg relative">
                            <i class="fab fa-android mr-2"></i> {{ 'home.app.button'|trans }}
                            <span class="absolute -top-2 -right-2 bg-yellow-400 text-xs text-black px-2 py-1 rounded-full">
                                {{ 'home.app.prototype_warning'|trans }}
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer class="py-16 bg-postchat-dark text-white">
            <div class="container mx-auto px-4">
                <div class="max-w-lg mx-auto text-center mb-12">
                    <a href="{{ path('app_main_index') }}" class="inline-block mb-6">
                        <img src="{{ asset('img/logo/postchat-logo-512-min.png') }}" alt="PostCHAT" class="h-16">
                    </a>
                    <p class="text-gray-400 mb-8">{{ 'home.footer.text'|trans }}</p>
                    <div class="flex justify-center space-x-6">
                        <a href="{{ path('app_download') }}" class="text-white hover:text-postchat-red transition-colors relative">
                            <i class="fab fa-android text-xl"></i>
                            <span class="ml-2">{{ 'home.app.button'|trans }}</span>
                            <span class="absolute -top-2 -right-2 bg-yellow-400 text-xs text-black px-1 py-0.5 rounded-full">
                                {{ 'home.app.prototype_warning'|trans }}
                            </span>
                        </a>
                        <a href="https://my.terra.marketing/webchat/?p=1788388" target="_blank" class="text-white hover:text-postchat-red transition-colors">
                            <i class="fa fa-globe text-xl"></i>
                            <span class="ml-2">WebChat</span>
                        </a>
                    </div>
                </div>
                <div class="border-t border-gray-800 pt-8">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <p class="text-gray-500">&copy; {{ 'now'|date('Y') }} PostChat. {{ 'home.footer.copyright'|trans }}</p>
                        <div class="flex space-x-6 mt-4 md:mt-0">
                            <a href="#" class="text-gray-500 hover:text-white transition-colors">{{ 'home.footer.privacy'|trans }}</a>
                            <a href="https://api.post-chat.com/api/docs" target="_blank" class="text-gray-500 hover:text-white transition-colors">{{ 'home.footer.documentation'|trans }}</a>
                            <a href="#home" class="text-gray-500 hover:text-white transition-colors">
                                <i class="fas fa-arrow-up"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>

        <!-- Connect Modal -->
        <div class="modal fade" id="connectModal" tabindex="-1" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content rounded-xl overflow-hidden">
                    <div class="modal-header border-b border-gray-100">
                        <h5 class="modal-title font-bold text-xl">{{ 'header.modal.title'|trans }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-6 text-center">
                        <p class="mb-6">{{ 'header.modal.text'|trans }}</p>
                        <div class="flex justify-center space-x-4 mb-6">
                            <a href="https://my.terra.marketing/webchat/?p=1788388" target="_blank" class="btn-primary">
                                <i class="fa fa-globe mr-2"></i> WebChat
                            </a>
                        </div>
                        <p class="text-sm text-gray-500">{{ 'header.modal.terms'|trans({'privacy_url': 'https://privacy-policy.terra.marketing'})|raw }}</p>
                    </div>
                </div>
            </div>
        </div>
    </main>
{% endblock %}
