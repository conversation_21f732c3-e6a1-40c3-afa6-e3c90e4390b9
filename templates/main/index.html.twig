{% extends 'base.html.twig' %}

{% block title %}PostCHAT - {{ 'home.meta.title_suffix'|trans }}{% endblock %}

{% block body %}
    {% include 'components/header.html.twig' %}

    <main id="home" class="fix">

        <!-- gradient-position -->
        <div class="gradient-position">
            <img src="{{ asset('img/others/gradient-circle.svg') }}" style="left: -4%; top: -4%" width="500" alt="circle">
            <img src="{{ asset('img/others/gradient-circle.svg') }}" style="right: -4%; bottom: -4%" width="500" alt="circle">
        </div>
        <!-- gradient-position-end -->

        <!-- banner-area -->
        <section class="banner-area banner-padding position-relative">
            <div class="container">
                <div class="row">
                    <div class="col-lg-6">
                        <div class="banner__content" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 100;">
                            <img src="{{ asset('img/icons/circle-02.png') }}" width="24" alt="object" style="top: -25%; left: 38%" data-anime="opacity:[0, 1]; scale:[0, 1]; onview: true; delay: 400;">
                            <img src="{{ asset('img/objects/circle-01.png') }}" width="16" alt="object" style="top: 14%; left: -12%" data-anime="opacity:[0, 1]; scale:[0, 1]; onview: true; delay: 420;">
                            <img src="{{ asset('img/objects/circle-03.png') }}" width="24" alt="object" style="bottom: -16%; left: 12%" data-anime="opacity:[0, 1]; scale:[0, 1]; onview: true; delay: 440;">

                            <h2 class="title">{{ 'home.hero.title'|trans }}</h2>
                            <p class="desc">{{ 'home.hero.subtitle'|trans }}</p>
                            <div class="d-flex flex-wrap gap-3">
                                <a href="#about" class="banner__btn btn gradient-btn"><span>{{ 'home.hero.button'|trans }}</span> <i class="unicon-arrow-right"></i></a>
                                <a href="{{ path('app_download') }}" class="banner__btn btn btn-dark position-relative">
                                    <i class="fab fa-android me-2"></i> <span>{{ 'home.app.button'|trans }}</span>
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning text-dark" style="font-size: 0.6rem;">{{ 'home.app.prototype_warning'|trans }}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="banner__images" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 200;">
                            <img src="{{ asset('img/objects/3d-box-min.png') }}" width="100px" class="shape" alt="object" style="top: 67%; left: -40%" data-anime="opacity:[0, 1]; scale:[0, 1]; onview: true; delay: 460;">

                            <img src="{{ asset('img/objects/x.png') }}" width="28" class="shape" alt="object" style="top: -4%; right: 16%" data-anime="opacity:[0, 1]; scale:[0, 1]; onview: true; delay: 480;">

                            <img class="shape dashed-line has-active-light" width="300" src="{{ asset('img/others/blob-dashed.svg') }}" alt="Blob dashed" style="top: -10%; right: 17%; fill: transparent; opacity: .1;">

                            <img class="shape dashed-line has-active-dark" width="300" src="{{ asset('img/others/blob-dashed-light.svg') }}" alt="Blob dashed" style="top: -10%; right: 17%; fill: transparent; opacity: .1;">

                            <svg style="top: -17%; opacity: .3;" class="shape" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                                <path fill="#F796FF" d="M47.5,-67.2C55.9,-59.3,53.2,-37.9,56.7,-20.1C60.2,-2.3,69.9,11.8,70.8,27.3C71.7,42.9,63.8,59.9,50.6,64.4C37.3,68.9,18.6,60.8,-0.3,61.2C-19.3,61.6,-38.6,70.7,-53.5,66.7C-68.4,62.8,-78.9,45.9,-78.8,29.5C-78.7,13.2,-67.9,-2.7,-59.8,-16.8C-51.6,-31,-46,-43.3,-36.5,-50.9C-27,-58.4,-13.5,-61.1,3,-65.2C19.6,-69.4,39.1,-75.1,47.5,-67.2Z" transform="translate(100 100)"/>
                            </svg>

                            <div class="banner__images-grid">
                                <div class="left"><img src="{{ asset('img/artwork/hero-min.png') }}" alt="artwork"></div>
                            </div>

                            <div class="tg-circle-text">
                                <svg class="tg-circle-text-path tg-animation-spin" viewBox="0 0 100 100" width="120" height="120">
                                    <defs><path id="circle" d="M 50, 50 m -37, 0 a 37,37 0 1,1 74,0 a 37,37 0 1,1 -74,0"/></defs>
                                    <text font-size="11.75">
                                        <textPath xlink:href="#circle">PostChat • Logistics • PostChat • AI • Sell</textPath>
                                    </text>
                                </svg>
                                <i class="unicon-arrow-up-right"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- banner-area-end -->

        {#<!-- brand-area -->
        <div class="brand-area section-pb-65" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 300;">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-xl-10 col-12">
                        <h6 class="brand__title text-uppercase">{{ 'home.customers.title'|trans }}</h6>
                        <div class="brand__list">
                            <div class="brand__item">
                                <img src="{{ asset('img/logo/customers/townhouse27-dark.png') }}" class="has-active-light" loading="lazy" alt="Townhouse 27">
                                <img src="{{ asset('img/logo/customers/townhouse27.png') }}" class="has-active-dark" loading="lazy" alt="Townhouse 27">
                            </div>
                            <div class="brand__item">
                                <img src="{{ asset('img/logo/customers/moskova-belgrade.png') }}" class="has-active-light" loading="lazy" alt="Moskova Belgrade">
                                <img src="{{ asset('img/logo/customers/moskova-belgrade.png') }}" class="has-active-dark" loading="lazy" alt="Moskova Belgrade">
                            </div>
                            <div class="brand__item">
                                <img src="{{ asset('img/logo/customers/townhouse27-dark.png') }}" class="has-active-light" loading="lazy" alt="Townhouse 27">
                                <img src="{{ asset('img/logo/customers/townhouse27.png') }}" class="has-active-dark" loading="lazy" alt="Townhouse 27">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- brand-area-end -->

        <!-- section-divider -->
        <div class="section-divider" data-anime="opacity:[0, 1]; scale:[0, 1]; onview: true; delay: 100;">
            <img src="{{ asset('img/others/divider-01.svg') }}" loading="lazy" alt="divider">
        </div>
        <!-- section-divider-end -->#}

        <!-- about-area -->
        <section id="about" class="about-area section-pt-70 section-pb-80">
            <div class="container">
                <div class="row justify-content-center" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 100;">
                    <div class="col-xl-8 col-lg-10">
                        <div class="section__title text-center title-mb-80">
                            <h2 class="title">{{ 'home.about.title'|trans }}</h2>
                        </div>
                    </div>
                </div>
                <div class="about__row-reverse">
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="about__img" data-anime="opacity:[0, 1]; translateX:[-24, 0]; onview: -250; delay: 200;">
                                <img src="{{ asset('img/artwork/sc-3-min.png') }}" loading="lazy" alt="img">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="about__content" data-anime="opacity:[0, 1]; translateX:[24, 0]; onview: -250; delay: 300;">
                                <div class="section__title text-start">
                                    <span class="sub-title tg-text-gradient">{{ 'home.about.item.1.subtitle'|trans }}</span>
                                    <h2 class="title">{{ 'home.about.item.1.title'|trans }}</h2>
                                </div>
                                <p>{{ 'home.about.item.1.text'|trans }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="about__img" data-anime="opacity:[0, 1]; translateX:[24, 0]; onview: -250; delay: 300;">
                                <img src="{{ asset('img/artwork/sc-4-min.png') }}" loading="lazy" alt="img">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="about__content" data-anime="opacity:[0, 1]; translateX:[-24, 0]; onview: -250; delay: 400;">
                                <div class="section__title text-start">
                                    <span class="sub-title tg-text-gradient">{{ 'home.about.item.2.subtitle'|trans }}</span>
                                    <h2 class="title">{{ 'home.about.item.2.title'|trans }}</h2>
                                </div>
                                <p>{{ 'home.about.item.2.text'|trans }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="about__img" data-anime="opacity:[0, 1]; translateX:[-24, 0]; onview: -250; delay: 200;">
                                <img src="{{ asset('img/artwork/sc-3-min.png') }}" loading="lazy" alt="img">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="about__content" data-anime="opacity:[0, 1]; translateX:[24, 0]; onview: -250; delay: 300;">
                                <div class="section__title text-start">
                                    <span class="sub-title tg-text-gradient">{{ 'home.about.item.3.subtitle'|trans }}</span>
                                    <h2 class="title">{{ 'home.about.item.3.title'|trans }}</h2>
                                </div>
                                <p>{{ 'home.about.item.3.text'|trans }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="about__img" data-anime="opacity:[0, 1]; translateX:[24, 0]; onview: -250; delay: 300;">
                                <img src="{{ asset('img/artwork/sc-4-min.png') }}" loading="lazy" alt="img">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="about__content" data-anime="opacity:[0, 1]; translateX:[-24, 0]; onview: -250; delay: 400;">
                                <div class="section__title text-start">
                                    <span class="sub-title tg-text-gradient">{{ 'home.about.item.4.subtitle'|trans }}</span>
                                    <h2 class="title">{{ 'home.about.item.4.title'|trans }}</h2>
                                </div>
                                <p>{{ 'home.about.item.4.text'|trans }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="about__img" data-anime="opacity:[0, 1]; translateX:[-24, 0]; onview: -250; delay: 200;">
                                <img src="{{ asset('img/artwork/sc-3-min.png') }}" loading="lazy" alt="img">
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="about__content" data-anime="opacity:[0, 1]; translateX:[24, 0]; onview: -250; delay: 300;">
                                <div class="section__title text-start">
                                    <span class="sub-title tg-text-gradient">{{ 'home.about.item.5.subtitle'|trans }}</span>
                                    <h2 class="title">{{ 'home.about.item.5.title'|trans }}</h2>
                                </div>
                                <p>{{ 'home.about.item.5.text'|trans }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- about-area-end -->

        <!-- features-area -->
        <section id="features" class="mint-area section-pt-80 section-pb-50">
            <div class="container">
                <div class="row justify-content-center" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 200;">
                    <div class="col-xl-8 col-lg-10">
                        <div class="section__title text-center title-mb-80">
                            <h2 class="title">{{ 'home.features.title'|trans }}</h2>
                        </div>
                    </div>
                </div>
                <div class="mint__lits-wrapper" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 300;">
                    <img class="shape" width="16" src="{{ asset('img/objects/circle-01.png') }}" loading="lazy" alt="Object" style="top: -16%; left: 8%">

                    <img class="shape" width="24" src="{{ asset('img/objects/circle-02.png') }}" loading="lazy" alt="Object" style="bottom: 16%; right: -8%">

                    <img class="shape" width="28" src="{{ asset('img/objects/x.png') }}" loading="lazy" alt="Object" style="bottom: 16%; left: -8%">

                    <div class="row justify-content-center" data-anime="targets: > *; opacity:[0, 1]; translateY:[24, 0]; onview: -250; delay: anime.stagger(100);">
                        <div class="col-md-6 col-sm-9">
                            <div class="mint__item">
                                <div class="mint__icon">
                                    <img src="{{ asset('img/icons/languages.png') }}" loading="lazy" alt="icon">
                                </div>
                                <div class="mint__content">
                                    <h3 class="title">{{ 'home.features.item.1.title'|trans }}</h3>
                                    <p class="desc">{{ 'home.features.item.1.subtitle'|trans }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-9">
                            <div class="mint__item">
                                <div class="mint__icon">
                                    <img src="{{ asset('img/icons/quality.png') }}" loading="lazy" alt="icon">
                                </div>
                                <div class="mint__content">
                                    <h3 class="title">{{ 'home.features.item.2.title'|trans }}</h3>
                                    <p class="desc">{{ 'home.features.item.2.subtitle'|trans }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-9">
                            <div class="mint__item">
                                <div class="mint__icon">
                                    <img src="{{ asset('img/icons/online-shop.png') }}" loading="lazy" alt="icon">
                                </div>
                                <div class="mint__content">
                                    <h3 class="title">{{ 'home.features.item.3.title'|trans }}</h3>
                                    <p class="desc">{{ 'home.features.item.3.subtitle'|trans }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 col-sm-9">
                            <div class="mint__item">
                                <div class="mint__icon">
                                    <img src="{{ asset('img/icons/stats.png') }}" loading="lazy" alt="icon">
                                </div>
                                <div class="mint__content">
                                    <h3 class="title">{{ 'home.features.item.4.title'|trans }}</h3>
                                    <p class="desc">{{ 'home.features.item.4.subtitle'|trans }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- features-area-end -->

        <!-- section-divider -->
        <div class="section-divider" data-anime="opacity:[0, 1]; scale:[0, 1]; onview: true; delay: 100;">
            <img src="{{ asset('img/others/divider-01.svg') }}" loading="lazy" alt="divider">
        </div>
        <!-- section-divider-end -->

        <!-- fact-area -->
        <section class="fact-area section-pt-60 section-pb-30">
            <div class="container">
                <div class="fact__items-wrap position-relative">
                    <img class="shape" src="{{ asset('img/objects/3d-box-min.png') }}" loading="lazy" width="32" alt="Object" style="bottom: -32%; left: 30%" data-anime="opacity:[0, 1]; scale:[0, 1]; onview: -250; delay: 200;">

                    <img class="shape" src="{{ asset('img/objects/circle-01.png') }}" loading="lazy" width="24" alt="Object" style="top: -25%; right: 25%" data-anime="opacity:[0, 1]; scale:[0, 1]; onview: -250; delay: 200;">

                    <div class="row justify-content-center" data-anime="targets: > *; opacity:[0, 1]; scale:[0.5, 1]; onview: -250; delay: anime.stagger(100);">
                        <div class="col-lg-4 col-md-6 col-sm-8">
                            <div class="fact__item text-center">
                                <h2 class="fact__count tg-text-gradient">4,000+</h2>
                                <span class="meta">{{ 'home.numbers.item.1.title'|trans }}</span>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 col-sm-8">
                            <div class="fact__item text-center">
                                <h2 class="fact__count tg-text-gradient">2,000+</h2>
                                <span class="meta">{{ 'home.numbers.item.2.title'|trans }}</span>
                            </div>
                        </div>
                        <div class="col-lg-4 col-md-6 col-sm-8">
                            <div class="fact__item text-center">
                                <h2 class="fact__count tg-text-gradient">24/7</h2>
                                <span class="meta">{{ 'home.numbers.item.3.title'|trans }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- fact-area-end -->

        <!-- section-divider -->
        <div class="section-divider" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 100;">
            <img src="{{ asset('img/others/divider-01.svg') }}" loading="lazy" alt="divider">
        </div>
        <!-- section-divider-end -->

        <!-- faq-area -->
        <section id="faq" class="faq-area section-py-80">
            <div class="container">
                <div class="row justify-content-center" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 100;">
                    <div class="col-xl-8 col-lg-10">
                        <div class="section__title text-center title-mb-75">
                            <h2 class="title">{{ 'home.faq.title'|trans }}</h2>
                        </div>
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-xxl-7 col-xl-9 col-lg-10">
                        <div class="faq__wrapper" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 100;">
                            <div class="accordion" id="accordionFaq">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingOne">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                            {{ 'home.faq.item.1.title'|trans }}
                                        </button>
                                    </h2>
                                    <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionFaq">
                                        <div class="accordion-body">
                                            <p>{{ 'home.faq.item.1.text'|trans }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingTwo">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                            {{ 'home.faq.item.2.title'|trans }}
                                        </button>
                                    </h2>
                                    <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionFaq">
                                        <div class="accordion-body">
                                            <p>{{ 'home.faq.item.2.text'|trans }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingThree">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                            {{ 'home.faq.item.3.title'|trans }}
                                        </button>
                                    </h2>
                                    <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionFaq">
                                        <div class="accordion-body">
                                            <p>{{ 'home.faq.item.3.text'|trans }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFour">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                            {{ 'home.faq.item.4.title'|trans }}
                                        </button>
                                    </h2>
                                    <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="headingFour" data-bs-parent="#accordionFaq">
                                        <div class="accordion-body">
                                            <p>{{ 'home.faq.item.4.text'|trans }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingFive">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                                            {{ 'home.faq.item.5.title'|trans }}
                                        </button>
                                    </h2>
                                    <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="headingFive" data-bs-parent="#accordionFaq">
                                        <div class="accordion-body">
                                            <p>{{ 'home.faq.item.5.text'|trans }}</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="headingSix">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseSix" aria-expanded="false" aria-controls="collapseSix">
                                            {{ 'home.faq.item.6.title'|trans }}
                                        </button>
                                    </h2>
                                    <div id="collapseSix" class="accordion-collapse collapse" aria-labelledby="headingSix" data-bs-parent="#accordionFaq">
                                        <div class="accordion-body">
                                            <p>{{ 'home.faq.item.6.text'|trans }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- faq-area-end -->

        <!-- section-divider -->
        <div class="section-divider" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 100;">
            <img src="{{ asset('img/others/divider-01.svg') }}" loading="lazy" alt="divider">
        </div>
        <!-- section-divider-end -->

        <!-- cta-area -->
        <section class="cta-area">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-xl-6 col-lg-8">
                        <div class="cta__content text-center">
                            <img src="{{ asset('img/objects/circle-01.png') }}" loading="lazy" width="24" alt="Object" style="top: 0; left: -16%">
                            <img src="{{ asset('img/objects/x.png') }}" loading="lazy" width="24" alt="Object" style="bottom: 16%; right: -8%">
                            <img src="{{ asset('img/objects/3d-box-min.png') }}" loading="lazy" width="40" alt="Object" style="top: 0; right: -16%">
                            <img src="{{ asset('img/objects/orange-ball.png') }}" loading="lazy" width="48" alt="Object" style="bottom: 16%; left: -8%">
                            <h2 class="title">{{ 'home.cta.title'|trans }}</h2>
                            <div class="d-flex justify-content-center gap-3 flex-wrap mt-4">
                                <button class="btn gradient-btn" data-bs-toggle="modal" data-bs-target="#connectModal">{{ 'home.cta.button'|trans }}</button>
                                <a href="{{ path('app_download') }}" class="btn btn-dark position-relative">
                                    <i class="fab fa-android me-2"></i> {{ 'home.app.button'|trans }}
                                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-warning text-dark" style="font-size: 0.6rem;">{{ 'home.app.prototype_warning'|trans }}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- cta-area-end -->

        <!-- section-divider -->
        <div class="section-divider" data-anime="opacity:[0, 1]; translateY:[24, 0]; onview: true; delay: 100;">
            <img src="{{ asset('img/others/divider-01.svg') }}" loading="lazy" alt="divider">
        </div>
        <!-- section-divider-end -->

    </main>
    <footer class="footer-area section-py-80">
        <div class="container">
            <div class="footer__wrapper">
                <img src="{{ asset('img/objects/3d-box-min.png') }}" loading="lazy" width="32" alt="object" style="top: 32%; left: 16%">
                <img src="{{ asset('img/objects/x.png') }}" loading="lazy" width="16" alt="object" style="top: 8%; right: 16%">
                <img src="{{ asset('img/objects/circle-01.png') }}" loading="lazy" width="16" alt="object" style="bottom: 24%; right: 40%">
                <img src="{{ asset('img/objects/circle-03.png') }}" loading="lazy" width="24" alt="object" style="bottom: -8%; left: 30%">
                <div class="row justify-content-center">
                    <div class="col-xl-5 col-lg-7 col-md-9 col-sm-11">
                        <div class="footer__info text-center">
                            <div class="footer-logo">
                                <a href="{{ path('app_main_index') }}"><img src="{{ asset('img/logo/postchat-logo-512-min.png') }}" width="200" alt="PostChat"></a>
                            </div>
                            <p>{{ 'home.footer.text'|trans }}</p>
                            <ul class="list-wrap footer__social">
                                <li>
                                    <a href="{{ path('app_download') }}" class="position-relative">
                                        <i class="fab fa-android"></i> {{ 'home.app.button'|trans }}
                                        <span class="badge bg-warning text-dark ms-2" style="font-size: 0.6rem;">{{ 'home.app.prototype_warning'|trans }}</span>
                                    </a>
                                </li>
                                <li><a href="https://my.terra.marketing/webchat/?p=1788388" target="_blank"><i class="fa fa-globe"></i> WebChat</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="copyright__wrapper">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="copyright__text">
                                <p>&copy; {{ 'now'|date('Y') }} PostChat. {{ 'home.footer.copyright'|trans }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="copyright__menu">
                                <ul class="list-wrap">
                                    <li><a href="#">{{ 'home.footer.privacy'|trans }}</a></li>
                                    <li><a href="https://api.post-chat.com/api/docs" target="_blank">{{ 'home.footer.documentation'|trans }}</a></li>
                                    <li class="backTop">
                                        <a href="javascript:void(0)" class="scroll-to-target" data-target="html"><i class="flaticon-arrowhead-up"></i></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>
{% endblock %}
