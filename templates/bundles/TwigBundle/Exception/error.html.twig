{% extends 'base.html.twig' %}

{% block title %}{{ 'error.'~status_code~'.title'|trans|default('Error') }}{% endblock %}

{% block stylesheets %}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/css/tabler.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@tabler/icons@latest/iconfont/tabler-icons.min.css">
    {{ importmap('app') }}
    <style>
        body {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background-color: #f8fafc;
        }
        .error-container {
            max-width: 500px;
            padding: 2rem;
            text-align: center;
        }
        .error-code {
            font-size: 6rem;
            font-weight: bold;
            color: #206bc4;
            line-height: 1;
        }
        .error-icon {
            font-size: 4rem;
            color: #206bc4;
            margin-bottom: 1rem;
        }
    </style>
{% endblock %}

{% block body %}
<div class="container">
    <div class="error-container">
        <div class="error-code">{{ status_code }}</div>
        <div class="error-icon">
            {% if status_code == 404 %}
                <i class="ti ti-file-search"></i>
            {% elseif status_code == 403 %}
                <i class="ti ti-lock"></i>
            {% elseif status_code == 500 %}
                <i class="ti ti-alert-triangle"></i>
            {% else %}
                <i class="ti ti-alert-circle"></i>
            {% endif %}
        </div>
        <h1 class="h3 mb-3">{{ 'error.'~status_code~'.title'|trans|default('Error') }}</h1>
        <p class="text-secondary mb-4">
            {{ 'error.'~status_code~'.message'|trans|default('An error occurred.') }}
        </p>
        <a href="{{ path('app_landing') }}" class="btn btn-primary">
            {{ 'error.'~status_code~'.button'|trans|default('Go to Homepage') }}
        </a>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@latest/dist/js/tabler.min.js"></script>
{% endblock %}
