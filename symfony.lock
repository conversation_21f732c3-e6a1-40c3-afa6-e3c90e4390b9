{"arkounay/ux-collection": {"version": "4.0.3"}, "aws/aws-sdk-php-symfony": {"version": "2.8", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.3", "ref": "d1753f9e2a669c464b2b0618af9b0123426b67b4"}, "files": ["./config/packages/aws.yaml"]}, "doctrine/doctrine-bundle": {"version": "2.13", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "8d96c0b51591ffc26794d865ba3ee7d193438a83"}, "files": ["./config/packages/doctrine.yaml", "./src/Entity/.gitignore", "./src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["./config/packages/doctrine_migrations.yaml", "./migrations/.gitignore"]}, "dunglas/doctrine-json-odm": {"version": "1.4", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.1", "ref": "c2ab78f625df0c89af5908d50a28602ff8c4919f"}}, "kevinpapst/tabler-bundle": {"version": "1.7.0"}, "openai-php/symfony": {"version": "0.10", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "0.3", "ref": "23b35dfd25adb2c5c467a50239a9ef0aa16f9328"}}, "php-http/discovery": {"version": "1.20", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.18", "ref": "f45b5dd173a27873ab19f5e3180b2f661c21de02"}, "files": ["./config/packages/http_discovery.yaml"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": ["./.env.test", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "symfony/apache-pack": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5d454ec6cc4c700ed3d963f3803e1d427d9669fb"}, "files": ["./public/.htaccess"]}, "symfony/asset-mapper": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "5ad1308aa756d58f999ffbe1540d1189f5d7d14a"}, "files": ["./assets/app.js", "./assets/styles/app.css", "./config/packages/asset_mapper.yaml", "./importmap.php"]}, "symfony/brevo-mailer": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "7c1038ceb733175f610a913a885a7c8b552b703a"}}, "symfony/console": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["./bin/console"]}, "symfony/debug-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["./config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.5", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.4", "ref": "52e9754527a15e2b79d9a610f98185a1fe46622a"}, "files": ["./.env", "./.env.dev"]}, "symfony/form": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "7d86a6723f4a623f59e2bf966b6aad2fc461d36b"}, "files": ["./config/packages/csrf.yaml"]}, "symfony/framework-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.2", "ref": "87bcf6f7c55201f345d8895deda46d2adbdbaa89"}, "files": ["./config/packages/cache.yaml", "./config/packages/framework.yaml", "./config/preload.php", "./config/routes/framework.yaml", "./config/services.yaml", "./public/index.php", "./src/Controller/.gitignore", "./src/Kernel.php"]}, "symfony/mailer": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "09051cfde49476e3c12cd3a0e44289ace1c75a4f"}, "files": ["./config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.62", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["./config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["./config/packages/monolog.yaml"]}, "symfony/notifier": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "178877daf79d2dbd62129dd03612cb1a2cb407cc"}, "files": ["./config/packages/notifier.yaml"]}, "symfony/phpunit-bridge": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": ["./.env.test", "./bin/phpunit", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "symfony/routing": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["./config/packages/routing.yaml", "./config/routes.yaml"]}, "symfony/security-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["./config/packages/security.yaml", "./config/routes/security.yaml"]}, "symfony/stimulus-bundle": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.20", "ref": "3acc494b566816514a6873a89023a35440b6386d"}, "files": ["./assets/bootstrap.js", "./assets/controllers.json", "./assets/controllers/csrf_protection_controller.js", "./assets/controllers/hello_controller.js"]}, "symfony/translation": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["./config/packages/translation.yaml", "./translations/.gitignore"]}, "symfony/twig-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["./config/packages/twig.yaml", "./templates/base.html.twig"]}, "symfony/uid": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "0df5844274d871b37fc3816c57a768ffc60a43a5"}}, "symfony/ux-icons": {"version": "2.24", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.17", "ref": "803a3bbd5893f9584969ab8670290cdfb6a0a5b5"}, "files": ["./assets/icons/symfony.svg"]}, "symfony/ux-live-component": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.6", "ref": "73e69baf18f47740d6f58688c5464b10cdacae06"}, "files": ["./config/routes/ux_live_component.yaml"]}, "symfony/ux-turbo": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.20", "ref": "c85ff94da66841d7ff087c19cbcd97a2df744ef9"}}, "symfony/ux-twig-component": {"version": "2.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.13", "ref": "67814b5f9794798b885cec9d3f48631424449a01"}, "files": ["./config/packages/twig_component.yaml"]}, "symfony/validator": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["./config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "7.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["./config/packages/web_profiler.yaml", "./config/routes/web_profiler.yaml"]}, "symfonycasts/reset-password-bundle": {"version": "1.23", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "97c1627c0384534997ae1047b93be517ca16de43"}, "files": ["./config/packages/reset_password.yaml"]}, "symfonycasts/tailwind-bundle": {"version": "0.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "0.8", "ref": "4ea7c9488fdce8943520daf3fdc31e93e5b59c64"}, "files": ["./config/packages/symfonycasts_tailwind.yaml"]}, "symfonycasts/verify-email-bundle": {"version": "v1.17.3"}, "twig/extra-bundle": {"version": "v3.20.0"}}