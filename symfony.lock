{"doctrine/doctrine-bundle": {"version": "2.12", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "2.12", "ref": "7b1b0b637b337f6beb895589948cd119da705524"}, "files": ["./config/packages/doctrine.yaml", "./src/Entity/.gitignore", "./src/Repository/.gitignore"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.1", "ref": "1d01ec03c6ecbd67c3375c5478c9a423ae5d6a33"}, "files": ["./config/packages/doctrine_migrations.yaml", "./migrations/.gitignore"]}, "phpunit/phpunit": {"version": "9.6", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "9.6", "ref": "7364a21d87e658eb363c5020c072ecfdc12e2326"}, "files": ["./.env.test", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "symfony/apache-pack": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.0", "ref": "5d454ec6cc4c700ed3d963f3803e1d427d9669fb"}, "files": ["./public/.htaccess"]}, "symfony/asset-mapper": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "6c28c471640cc2c6e60812ebcb961c526ef8997f"}, "files": ["./assets/app.js", "./assets/styles/app.css", "./config/packages/asset_mapper.yaml", "./importmap.php"]}, "symfony/console": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "1781ff40d8a17d87cf53f8d4cf0c8346ed2bb461"}, "files": ["./bin/console"]}, "symfony/debug-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.3", "ref": "5aa8aa48234c8eb6dbdd7b3cd5d791485d2cec4b"}, "files": ["./config/packages/debug.yaml"]}, "symfony/flex": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "146251ae39e06a95be0fe3d13c807bcf3938b172"}, "files": ["./.env"]}, "symfony/framework-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "6356c19b9ae08e7763e4ba2d9ae63043efc75db5"}, "files": ["./config/packages/cache.yaml", "./config/packages/framework.yaml", "./config/preload.php", "./config/routes/framework.yaml", "./config/services.yaml", "./public/index.php", "./src/Controller/.gitignore", "./src/Kernel.php"]}, "symfony/mailer": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "df66ee1f226c46f01e85c29c2f7acce0596ba35a"}, "files": ["./config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.59", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.0", "ref": "ba1ac4e919baba5644d31b57a3284d6ba12d52ee"}, "files": ["./config/packages/messenger.yaml"]}, "symfony/monolog-bundle": {"version": "3.10", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.7", "ref": "aff23899c4440dd995907613c1dd709b6f59503f"}, "files": ["./config/packages/monolog.yaml"]}, "symfony/notifier": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "5.0", "ref": "178877daf79d2dbd62129dd03612cb1a2cb407cc"}, "files": ["./config/packages/notifier.yaml"]}, "symfony/phpunit-bridge": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "a411a0480041243d97382cac7984f7dce7813c08"}, "files": ["./.env.test", "./bin/phpunit", "./phpunit.xml.dist", "./tests/bootstrap.php"]}, "symfony/routing": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "21b72649d5622d8f7da329ffb5afb232a023619d"}, "files": ["./config/packages/routing.yaml", "./config/routes.yaml"]}, "symfony/security-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "2ae08430db28c8eb4476605894296c82a642028f"}, "files": ["./config/packages/security.yaml", "./config/routes/security.yaml"]}, "symfony/translation": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.3", "ref": "e28e27f53663cc34f0be2837aba18e3a1bef8e7b"}, "files": ["./config/packages/translation.yaml", "./translations/.gitignore"]}, "symfony/twig-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.4", "ref": "cab5fd2a13a45c266d45a7d9337e28dee6272877"}, "files": ["./config/packages/twig.yaml", "./templates/base.html.twig"]}, "symfony/validator": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "7.0", "ref": "8c1c4e28d26a124b0bb273f537ca8ce443472bfd"}, "files": ["./config/packages/validator.yaml"]}, "symfony/web-profiler-bundle": {"version": "7.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "6.1", "ref": "e42b3f0177df239add25373083a564e5ead4e13a"}, "files": ["./config/packages/web_profiler.yaml", "./config/routes/web_profiler.yaml"]}, "twig/extra-bundle": {"version": "v3.10.0"}}