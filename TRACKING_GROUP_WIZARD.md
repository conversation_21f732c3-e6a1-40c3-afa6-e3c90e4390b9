# Tracking Group Creation Wizard

## Overview

The Tracking Group Creation Wizard provides a streamlined, step-by-step process for creating new tracking groups in Magnora. This modern interface breaks down the complex process into manageable steps with clear guidance and visual feedback.

## Key Features

- **Step-by-Step Process**: Breaks down tracking group creation into 4 logical steps
- **Visual Progress Indicator**: Shows current position in the workflow
- **Intuitive Navigation**: Easy movement between steps with back/forward buttons
- **Responsive Design**: Works seamlessly on desktop and mobile devices
- **Contextual Help**: Provides guidance and examples throughout the process
- **Resource Estimation**: Shows estimated data collection based on configuration

## Wizard Steps

### Step 1: Basic Information

![Step 1: Basic Information](https://placeholder.com/step1.png)

This step collects the fundamental information about the tracking group:

- **Group Name**: Optional name for the tracking group (auto-generated if left empty)
- **Data Source**: Visual selection of the data source (Instagram, Facebook, etc.)
- **List Type**: Type of entities to track (Profiles, Pages, Groups)

**UX Improvements:**
- Card-based data source selection with visual icons and descriptions
- Clear indication of required vs. optional fields
- Helpful tooltips explaining each option

### Step 2: Profiles List

![Step 2: Profiles List](https://placeholder.com/step2.png)

This step allows users to enter the profiles, pages, or groups they want to track:

- **Profiles List**: Multi-line text input for entering usernames
- **Format Tools**: Utilities to clean and format the list
- **Real-time Count**: Shows the number of profiles entered
- **Paste Functionality**: One-click paste from clipboard

**UX Improvements:**
- Real-time validation and counting of entered profiles
- Format button to clean up lists (remove duplicates, sort, etc.)
- Example formats shown for reference
- Bulk import options for CSV and Excel files

### Step 3: Tracking Options

![Step 3: Tracking Options](https://placeholder.com/step3.png)

This step configures how the tracking group will collect and process data:

- **Category & Sub-Category**: Organizational classification
- **Maximum Posts Per Profile**: Number of posts to collect from each profile
- **Maximum Comments Per Post**: Number of comments to collect per post
- **AI Analysis**: Option to run AI analysis on collected data
- **Auto-Update**: Option to keep data updated automatically
- **Custom Fields**: Ability to add custom metadata

**UX Improvements:**
- Increment/decrement controls for numeric inputs
- Collapsible advanced options section to reduce complexity
- Visual toggles for boolean options
- Dynamic custom fields management

### Step 4: Review & Create

![Step 4: Review & Create](https://placeholder.com/step4.png)

This final step provides a comprehensive review of all settings before creation:

- **Summary Card**: Complete overview of all configured settings
- **Edit Links**: Quick links to return to specific sections for changes
- **Resource Estimates**: Shows estimated profiles, posts, and comments
- **Create Button**: Final confirmation to create the tracking group

**UX Improvements:**
- Sectioned summary for easy scanning
- Visual indicators for enabled features
- Resource estimation to set expectations
- Clear call-to-action for final creation

## Technical Implementation

### Controller

The wizard is implemented using a dedicated controller (`TrackingGroupWizardController`) that manages:

- Session-based state management between steps
- Data validation and processing
- Final tracking group creation

```php
#[Route('/app/modern/tracking-group')]
final class TrackingGroupWizardController extends AbstractController
{
    // Routes for each step of the wizard
    #[Route('/create', name: 'app_modern_tracking_group_create')]
    #[Route('/wizard/{step}', name: 'app_modern_tracking_group_wizard_step')]
    #[Route('/wizard/cancel', name: 'app_modern_tracking_group_wizard_cancel')]
    
    // Methods for handling each step and final creation
}
```

### Templates

The wizard uses a modular template structure:

- `base.html.twig`: Common layout with progress indicator
- `step1.html.twig`: Basic information form
- `step2.html.twig`: Profiles list management
- `step3.html.twig`: Tracking options configuration
- `step4.html.twig`: Review and creation summary

### JavaScript Enhancements

Client-side JavaScript provides interactive features:

- Real-time profile counting
- List formatting and cleaning
- Clipboard integration
- Custom fields management
- Advanced options toggling
- Input validation

## User Flow

1. User clicks "Add Group" button on the tracking groups page
2. User is guided through each step of the wizard
3. User can navigate back and forth between steps
4. User reviews all settings on the final step
5. Upon confirmation, the tracking group is created
6. User is redirected to the tracking groups list with a success message

## Benefits Over Previous Implementation

- **Reduced Cognitive Load**: Breaking the process into steps makes it less overwhelming
- **Better Guidance**: Contextual help and examples guide users through the process
- **Improved Validation**: Clear feedback on required fields and input validation
- **Visual Clarity**: Card-based layouts and visual indicators improve understanding
- **Enhanced Flexibility**: Advanced options for power users without cluttering the interface
- **Better Mobile Experience**: Responsive design works well on all devices

## Future Enhancements

Potential improvements for future iterations:

- **Template Selection**: Pre-configured templates for common tracking scenarios
- **Bulk Import API**: Integration with external APIs for bulk profile import
- **Preview Functionality**: Preview sample data before creation
- **Scheduling Options**: Set up scheduled tracking and reporting
- **Advanced Filtering**: More granular control over what data to collect
- **Collaboration Features**: Share and collaborate on tracking group creation
