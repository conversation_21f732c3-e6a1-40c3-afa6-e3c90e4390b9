<?php
namespace App\Helper\Social\Meta;

use App\Helper\Social\MetaHelper;

class InstagramHelper extends MetaHelper
{
    public static function getMainData(array $data): array {
        if (array_key_exists('business_discovery', $data)) {
            $data = $data['business_discovery'];
        }

        return $data;
    }

    public static function getProfileDataForEntity(array $data): array {
        $data = self::getMainData($data);

        //unset($data['id'], $data['metaId']);
        unset($data['media']);

        return $data;
    }

    public static function getFeedForEntity(array $data): array {
        $data = self::getMainData($data);
        return array_key_exists('data', $data['media']) ? $data['media']['data'] : $data['media'];
    }
}