<?php
namespace App\Helper\Social\Meta;

use App\Helper\Social\MetaHelper;

class FacebookHelper extends MetaHelper
{
    public static function getMainData(array $data): array {
        if (isset($data['data']['id'])) {
            $data = $data['data'];
        }

        return $data;
    }

    public static function getProfileDataForEntity(array $data): array {
        $data = self::getMainData($data);
        unset($data['feed'], $data['tagged']);

        return $data;
    }

    public static function getFeedForEntity(array $data): array {
        $data = self::getMainData($data);
        $data = $data['feed'] ?? [];

        if (array_key_exists('data', $data)) {
            $data = $data['data'];
        }

        return $data;
    }

    public static function getTaggedForEntity(array $data): array {
        $data = self::getMainData($data);
        $data = $data['tagged'] ?? [];

        if (array_key_exists('data', $data)) {
            $data = $data['data'];
        }

        return $data;
    }
}