<?php

namespace App\Helper\Social\Meta;

class ReactionsHelper
{
    public const string SENTIMENT_POSITIVE = 'positive';
    public const string SENTIMENT_NEGATIVE = 'negative';
    public const string SENTIMENT_NEUTRAL = 'neutral';

    public static function getFacebookReactionsKeys(): array
    {
        return ['like', 'haha', 'love', 'wow', 'care', 'sad', 'angry'];
    }

    public static function flattenReactionsCount(array &$object, bool $removeOriginals = false): array {
        $keys = self::getFacebookReactionsKeys();

        foreach ($keys as $key) {
            $k = 'reactions_' . $key;
            if (array_key_exists($k, $object)) {
                if (isset($object[$k]['summary']['total_count'])) {
                    $object[$k . '_count'] = $object[$k]['summary']['total_count'];

                    if ($removeOriginals) {
                        unset($object[$k]);
                    }
                }
            }
        }

        return $object;
    }

    public static function getSentimentFromReactions(array $object): string {
        self::flattenReactionsCount($object);

        // Get reaction counts or set them to 0 if not present
        $like   = isset($object['reactions_like_count']) ? (int) $object['reactions_like_count'] : 0;
        $love   = isset($object['reactions_love_count']) ? (int) $object['reactions_love_count'] : 0;
        $haha   = isset($object['reactions_haha_count']) ? (int) $object['reactions_haha_count'] : 0;
        $wow    = isset($object['reactions_wow_count']) ? (int) $object['reactions_wow_count'] : 0;
        $care   = isset($object['reactions_care_count']) ? (int) $object['reactions_care_count'] : 0;
        $angry  = isset($object['reactions_angry_count']) ? (int) $object['reactions_angry_count'] : 0;
        $sad  = isset($object['reactions_sad_count']) ? (int) $object['reactions_sad_count'] : 0;

        $positive = $love + $care;
        $negative = $angry + $sad;
        $neutral  = $like + $wow + $haha;

        if ($neutral > ($positive + $negative) * 1.5) {
            return self::SENTIMENT_NEUTRAL;
        }

        if ($positive > $negative) {
            return self::SENTIMENT_POSITIVE;
        }

        if ($negative > $positive) {
            return self::SENTIMENT_NEGATIVE;
        }

        return self::SENTIMENT_NEUTRAL;
    }
}