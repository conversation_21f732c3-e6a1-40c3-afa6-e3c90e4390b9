<?php

namespace App\EventListener;

use App\Entity\User;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\Attribute\AsEventListener;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Routing\RouterInterface;

final readonly class CheckDisabledUserListener
{

    public function __construct(
        private Security        $security,
        private RouterInterface $router,
    ) {}

    #[AsEventListener(event: KernelEvents::REQUEST)]
    public function onKernelRequest(RequestEvent $event): void
    {
        // Skip sub-requests
        if (!$event->isMainRequest()) {
            return;
        }

        /** @var User $user */
        $user = $this->security->getUser();

        if ($user && !$user->isEnabled()) {
            $currentRoute = $event->getRequest()->attributes->get('_route');

            // Avoid infinite loop by skipping the redirect route
            if ($currentRoute !== 'app_dashboard_disabled') {
                $url = $this->router->generate('app_dashboard_disabled');
                $event->setResponse(new RedirectResponse($url));
            }
        }
    }
}
