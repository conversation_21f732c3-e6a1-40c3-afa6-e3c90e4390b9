<?php
namespace App\EventSubscriber;

use App\Entity\OrganizationUser;
use App\Entity\User;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\Routing\RouterInterface;

readonly class OrganizationSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private Security        $security,
        private RouterInterface $router
    ) {}

    public function onKernelRequest(RequestEvent $event): void
    {
        if ($this->security->isGranted('ROLE_USER')) {
            $this->handleCurrentOrganization($event);
        }
    }

    private function handleCurrentOrganization(RequestEvent $event): void {
        /** @var User $user */
        $user = $this->security->getUser();
        $request = $event->getRequest();

        if ($request->query->has('switch-to-org')) {
            $ids = array_map(function (OrganizationUser $org) {
                return $org->getOrganization()->getId()->toRfc4122();
            }, $user->getOrganizationUsers()->toArray());

            $switchTo = $request->query->get('switch-to-org');

            if (in_array($switchTo, $ids)) {
                $request->getSession()->set('current_organization', $request->query->get('switch-to-org'));
            }

            $queryParams = $request->query->all();
            unset($queryParams['switch-to-org']);

            $url = $this->router->generate($request->attributes->get('_route'), $queryParams);
            $response = new RedirectResponse($url);
            $event->setResponse($response);
        } else {
            if (!$request->getSession()->has('current_organization') || empty($request->getSession()->get('current_organization'))) {
                $defaultOrg = $user->getOrganizationUsers()->first()->getOrganization();
                $request->getSession()->set('current_organization', $defaultOrg->getId()->toRfc4122());
            }
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => 'onKernelRequest',
        ];
    }
}
