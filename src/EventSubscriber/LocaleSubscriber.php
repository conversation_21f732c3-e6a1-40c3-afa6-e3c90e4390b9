<?php
namespace App\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\Cookie;
use Symfony\Component\HttpKernel\Event\RequestEvent;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\Routing\RouterInterface;

class LocaleSubscriber implements EventSubscriberInterface
{
    private string $defaultLocale;
    private array $supportedLocales;

    public function __construct(string $defaultLocale = 'en', array $supportedLocales = ['en', 'tr'])
    {
        $this->defaultLocale = $defaultLocale;
        $this->supportedLocales = $supportedLocales;
    }

    public function onKernelRequest(RequestEvent $event): void
    {
        $request = $event->getRequest();

        $lang = $request->query->get('lang');
        $cookieLocale = $request->cookies->get('_locale');
        $browserLocale = substr($request->getPreferredLanguage($this->supportedLocales), 0, 2);

        if ($lang && in_array($lang, $this->supportedLocales)) {
            $response = new RedirectResponse($request->getUriForPath($request->getPathInfo()));
            $response->headers->setCookie(new Cookie('_locale', $lang));
            $event->setResponse($response);
            return;
        }

        $locale = $cookieLocale ?? $browserLocale ?? $this->defaultLocale;
        $request->setLocale(in_array($locale, $this->supportedLocales) ? $locale : $this->defaultLocale);
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::REQUEST => [['onKernelRequest', 20]],
        ];
    }
}

