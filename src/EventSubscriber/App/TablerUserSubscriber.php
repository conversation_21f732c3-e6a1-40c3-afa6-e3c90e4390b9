<?php

namespace App\EventSubscriber\App;

use <PERSON><PERSON>apst\TablerBundle\Event\UserDetailsEvent;
use <PERSON><PERSON>apst\TablerBundle\Model\MenuItemModel;
use KevinPapst\TablerBundle\Model\UserInterface;
use <PERSON>ymfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

final readonly class TablerUserSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private Security $security
    ) {}

    public function onUserDetailsEvent(UserDetailsEvent $event): void
    {
        if ($this->security->getUser()) {
            /** @var UserInterface $user */
            $user = $this->security->getUser();

            $event->setUser($user);
            $event->setShowLogoutLink(true);
            $event->addLink(
                new MenuItemModel('profile', 'Settings', 'app_profile', [], 'fa fa-user')
            );

            if ($this->security->isGranted('ROLE_ADMIN')) {
                $event->addLink(
                    new MenuItemModel('users', 'Users', 'app_admin_user_list', [], 'fa fa-users')
                );
            }
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            UserDetailsEvent::class => 'onUserDetailsEvent',
        ];
    }
}
