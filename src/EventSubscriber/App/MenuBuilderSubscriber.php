<?php

namespace App\EventSubscriber\App;

use App\Entity\User;
use <PERSON><PERSON>apst\TablerBundle\Event\MenuEvent;
use <PERSON><PERSON>apst\TablerBundle\Model\MenuItemModel;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Contracts\Translation\TranslatorInterface;
use function Symfony\Component\String\u;

readonly class MenuBuilderSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private TranslatorInterface $translator,
        private readonly Security $security,
    ) {}

    public function onSetupMenu(MenuEvent $event): void
    {
        $albanianElections = new MenuItemModel('albanian-elections', 'Albanian Elections', 'app_app_albanian_elections', [], 'fas fa-check-to-slot');
        $event->addItem($albanianElections);

        /** @var User $user */
        $user = $this->security->getUser();

        if ($this->security->isGranted('ROLE_ADMIN')) {
            $trackingGroups = new MenuItemModel('tracking-groups', $this->translator->trans('menu.tracking_groups', domain: 'menu'), 'app_app_dashboard', [], 'fas fa-users');
            $profilesList = new MenuItemModel('profile-list', $this->translator->trans('menu.profile_list', domain: 'menu'), 'app_meta_profile_list', [], 'fas fa-users-gear');
            $profileTracking = new MenuItemModel('profile-tracking', $this->translator->trans('menu.profile_tracking', domain: 'menu'), 'app_app_soon', [], 'fas fa-user');
            $polls = new MenuItemModel('polls', $this->translator->trans('menu.polls', domain: 'menu'), 'app_app_soon', [], 'fas fa-chart-pie');
            $subscription = new MenuItemModel('subscription', $this->translator->trans('menu.subscription_and_credits', domain: 'menu'), 'app_app_soon', [], 'fas fa-coins');

            $event
                ->addItem($trackingGroups)
                ->addItem($profilesList)
                ->addItem($profileTracking)
                ->addItem($polls)
                ->addItem($subscription)
            ;
        }


        $this->activateByRoute(
            $event->getRequest()->get('_route'),
            $event->getItems()
        );
    }

    /**
     * @param string $route
     * @param MenuItemModel[] $items
     */
    protected function activateByRoute(string $route, array $items): void
    {
        foreach ($items as $item) {
            if ($item->hasChildren()) {
                $this->activateByRoute($route, $item->getChildren());
            } elseif ($item->getRoute() == $route) {
                $item->setIsActive(true);
            }
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            MenuEvent::class => ['onSetupMenu', 100],
        ];
    }
}
