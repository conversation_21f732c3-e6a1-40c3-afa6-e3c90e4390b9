<?php
namespace App\EventSubscriber\App;

use <PERSON><PERSON>apst\TablerBundle\Helper\ContextHelper;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\Event\ControllerEvent;
use Symfony\Component\HttpKernel\KernelEvents;

final readonly class TablerThemeOptionsSubscriber implements EventSubscriberInterface
{
    public function __construct(
        private ContextHelper $contextHelper
    ) {}
    public function onKernelController(ControllerEvent $event): void
    {
        if (!$event->isMainRequest()) {
            return;
        }

        $this->handleThemeOptions($event);
    }

    private function handleThemeOptions(ControllerEvent $event): void {
        if ($event->getRequest()->cookies->has('tablerTheme')) {
            $tablerTheme = $event->getRequest()->cookies->get('tablerTheme');
            $darkMode    = $tablerTheme === "dark";
            $this->contextHelper->setIsDarkMode($darkMode);
            if ($darkMode) {
                $this->contextHelper->setIsHeaderDark(true);
            }
        }
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::CONTROLLER => 'onKernelController',
        ];
    }
}
