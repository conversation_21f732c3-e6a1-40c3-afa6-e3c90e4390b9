<?php
namespace App\Command;

use App\Entity\Social\TrackingGroup;
use App\Entity\Social\TrackingGroup\Status;
use App\Entity\Social\TrackingHistory;
use App\Entity\Social\TrackingStatus;
use App\Repository\Social\TrackingGroupRepository;
use App\Service\Social\ProfileUpdateService;
use DateInterval;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Throwable;

#[AsCommand(
    name: 'app:social:tracking-group:update',
    description: 'Update social tracking group.',
)]
class SocialTrackingGroupUpdateCommand extends Command
{
    public function __construct(
        private readonly ProfileUpdateService    $profileUpdateService,
        private readonly EntityManagerInterface  $entityManager,
        private readonly TrackingGroupRepository $trackingGroupRepository,
        private readonly LoggerInterface         $logger
    )
    {
        @ini_set('memory_limit', '1.5G');
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('batch-size', null, InputOption::VALUE_OPTIONAL, 'Batch size for this process')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $now = new DateTimeImmutable();
        $nowMinus15Min = (clone $now)->sub(new DateInterval('PT15M'));

        $this->profileUpdateService->setIO($io);

        // Check running tasks
        $tasksRequestedPauses = $this->entityManager->getRepository(TrackingStatus::class)->findBy(['status' => Status::PauseRequested]);
        foreach ($tasksRequestedPauses as $task) {
            if ($task->getUpdatedAt() < $nowMinus15Min) {
                $task->setStatus(Status::Paused);
            }
        }

        $this->entityManager->flush();

        $tasks = $this->entityManager->getRepository(TrackingStatus::class)->findBy(['status' => Status::Processing]);
        $failedTask = null;

        foreach ($tasks as $task) {
            if ($task->getUpdatedAt() < $nowMinus15Min) {
                $task->setStatus(Status::Queued);
                $failedTask = $task->getTrackingGroup();
                break;
            }
        }

        if (count($tasks) >= 5) {
            $this->entityManager->flush();
            $tasks = $this->entityManager->getRepository(TrackingStatus::class)->findBy(['status' => Status::Processing]);
        }

        // If there's a failed task, we should give it priority to process as it might be "our fault",
        // even if there are more tasks running than the limit.
        // But let's be careful and optimize this logic somehow in the future to avoid overloads.
        if (!$failedTask && count($tasks) >= 5) {
            $io->warning('There are too much running tasks!');
            return Command::SUCCESS;
        }

        $trackingGroup = null;
        if ($failedTask) {
            // Update and make sure it's not taken!
            sleep(1);
            $trackingGroup = $this->trackingGroupRepository->find($failedTask->getId());

            if ($trackingGroup->getStatus()->getStatus() === Status::Processing) {
                $failedTask = null;
            }
        }

        if ($failedTask) {
            $trackingGroup = $failedTask;
        } else {
            $trackingGroupsQuery = $this->trackingGroupRepository->createQueryBuilder('t')
                ->addSelect('t, s')
                ->leftJoin('t.status', 's')
                ->where('s.status = :queued')
                ->orWhere('(t.keepDataUpdated = TRUE AND s.status = :waiting AND s.lastRunAt <= :oneHourAgo)')
                ->orWhere('s.status = :updateRequested')
                ->setParameter('queued', Status::Queued)
                ->setParameter('waiting', Status::WaitingNextUpdate)
                ->setParameter('updateRequested', Status::UpdateRequested)
                ->setParameter('oneHourAgo', $now->sub(new DateInterval('PT1H')))
            ;

            /** @var TrackingGroup[] $trackingGroups */
            $trackingGroups = $trackingGroupsQuery
                ->orderBy('t.createdAt', 'ASC')
                ->getQuery()
                ->getResult()
            ;

            $trackingGroup = $trackingGroups[0] ?? null;
        }

        if (!$trackingGroup) {
            $io->success('There are no pending tracking groups!');
            return Command::SUCCESS;
        }

        $isFirstRun = !($trackingGroup->getStatus()->getStatus() === Status::WaitingNextUpdate) || $trackingGroup->getStatus()->getLastRunAt() === null;
        $forceUpdate = $trackingGroup->getStatus()->getStatus() === Status::UpdateRequested;

        $io->title("Processing {$trackingGroup->getName()}");
        $trackingGroup->getStatus()->setStatus(Status::Processing);
        $this->entityManager->flush();

        $needsContinuity = !empty($trackingGroup->getStatus()->getLastProcessedProfile());
        $reachedContinuity = false;

        $latestCreditDetails = [
            'profiles' => 0,
            'posts' => 0,
            'comments' => 0
        ];

        // Get Profiles
        $profileIndex = 1;
        foreach ($trackingGroup->getList() as $key => $username) {
            $profileId = null;
            if (str_contains($username, ':')) {
                list($username, $profileId) = explode(':', $username);
            }

            $trackingGroup->setUpdatedAt();
            $trackingGroup->getStatus()->setUpdatedAt();

            if ($needsContinuity && !$reachedContinuity) {
                if ($username == $trackingGroup->getStatus()->getLastProcessedProfile()) {
                    // We reached the last successfully processed profile, so we'll process the next one.
                    $reachedContinuity = true;
                }

                $trackingGroup->getStatus()->setProgress($profileIndex++);
                continue;
            }

            $progress = new ProgressBar($output);
            $progressStarted = false;
            $maximumProgress = null;

            $progressCallback = function($i, $max) use ($io, $progress, &$progressStarted, $trackingGroup, &$maximumProgress) {
                if (!$progressStarted && $max > 0) {
                    $io->info('Getting data...');
                    $progress->start();

                    $progressStarted = true;
                }

                $curMax = $trackingGroup->getMaximumPostsPerProfile();
                $_max = $curMax > 0 ? min($curMax, $max) : $max;

                if ($maximumProgress === null) {
                    $maximumProgress = $_max;
                }

                $progress->setMaxSteps($_max);
                $progress->setProgress($i);

                $trackingGroup->setUpdatedAt();
                $trackingGroup->getStatus()->setUpdatedAt();

                $this->entityManager->flush();
            };

            $this->profileUpdateService
                ->setProfileProgressCallback($progressCallback)
                ->setPostsLimit($trackingGroup->getMaximumPostsPerProfile())
                ->setCommentsLimit($trackingGroup->getMaximumCommentsPerPost())
                ->setFilterEntityType(get_class($trackingGroup))
                ->setFilterEntityId($trackingGroup->getId())
                //->setAnalyzeProfileWithAI($trackingGroup->runProfileAIAnalysis())
                // TODO: Re-enable later
                ->setAnalyzeProfileWithAI(false)
                ->setAnalyzeMediaWithAI(false)
                // TODO: Disable later, make it dynamic
                ->setForceIgnoreComments(true)
            ;

            if ($progressStarted) {
                $progress->finish();
            }

            $io->newLine(2);

            try {
                $result = $this->profileUpdateService->update($profileId ?? $username, $trackingGroup->getDataSource(), $forceUpdate);

                $profileDetails = $result['details']['profile'];
                if (!empty($profileDetails['username'] ?? null) && !empty($profileDetails['meta_id'] ?? null)) {
                    $currentList = $trackingGroup->getList();
                    $currentList[$key] = $profileDetails['username'] . ':' . $profileDetails['meta_id'];
                    $trackingGroup->setList($currentList);
                }

                if ($result['updated']) {
                    $currentCreditDetails = $trackingGroup->getCreditDetails();
                    $currentCreditDetails['profiles'] = ($currentCreditDetails['profiles'] ?? 0) + $result['details']['used_credits']['profiles'];
                    $currentCreditDetails['posts'] = ($currentCreditDetails['posts'] ?? 0) + $result['details']['used_credits']['posts'];
                    $currentCreditDetails['comments'] = ($currentCreditDetails['comments'] ?? 0) + $result['details']['used_credits']['comments'];

                    $trackingGroup->getStatus()
                        ->setLastProcessedProfile($username)
                    ;

                     if ($isFirstRun) {
                        $trackingGroup->setCreditDetails($currentCreditDetails);
                    }

                     $latestCreditDetails = $result['details']['used_credits'];
                }
            } catch (Exception $e) {
                $this->logger->error($e->getMessage(), $e->getTrace());
                $trackingGroup->getStatus()->setLastError("[@$username] Error: " . $e->getMessage());
            }

            $trackingGroup->getStatus()->setProgress($profileIndex++);

            $this->entityManager->flush();

            try {
                $this->entityManager->refresh($trackingGroup);
            } catch (Throwable $e) {
                $this->logger->error('Error refreshing Tracking Group: ' . $e->getMessage(), $e->getTrace());
            }

            if ($trackingGroup->getStatus()->getStatus() === Status::PauseRequested) {
                $trackingGroup->getStatus()->setStatus(Status::Paused);
                break;
            }
        }

        $trackingGroup->getStatus()
            ->setLastRunAt(new DateTimeImmutable())
            ->setCurrentProfile(null)
        ;

        if ($trackingGroup->getStatus()->getStatus() !== Status::Paused) {
            $trackingGroup->getStatus()
                ->setStatus($trackingGroup->keepDataUpdated() ? Status::WaitingNextUpdate : Status::Finished)
                ->setLastProcessedProfile(null)
            ;
        }

        $trackingGroup->addTrackingHistory(
            (new TrackingHistory())
                ->setProgress(count($trackingGroup->getList()))
                ->setTotalProfiles(count($trackingGroup->getList()))
                ->setCreditDetails($latestCreditDetails)
        );

        $this->entityManager->flush();
        $io->success('Success! All done.');

        return Command::SUCCESS;
    }
}
