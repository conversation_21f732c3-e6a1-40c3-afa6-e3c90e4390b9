<?php

namespace App\Command;

use App\Service\FacebookAdsArchiveService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Uid\Uuid;

#[AsCommand(
    name: 'app:meta:ads:update',
    description: 'Updates Meta Ads.',
)]
class MetaAdsUpdateCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly FacebookAdsArchiveService $facebookAdsArchiveService
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->addOption('limit', 'l', InputOption::VALUE_OPTIONAL);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $connection = $this->entityManager->getConnection();
        $limit = is_numeric($input->getOption('limit')) ? (int) $input->getOption('limit') : null;

        $ads = $this->facebookAdsArchiveService->fetchAdsArchive('politics', ['AL'], '2025-04-01',
            searchCriteria: [
                'search_terms' => 'a,e,i,o,u'
            ],
            limit: $limit
        );

        $sqlMedia = "
            INSERT INTO meta_ad (id, meta_id, page_meta_id, type, platforms, data, created_at, updated_at)
            VALUES (:id, :meta_id, :page_meta_id, :type, :platforms, :data, NOW(), NOW())
            ON CONFLICT (meta_id) 
            DO UPDATE SET 
                data = EXCLUDED.data,
                updated_at = NOW()
            ;
        ";
        $stmtMedia = $connection->prepare($sqlMedia);
        $connection->beginTransaction();

        foreach ($ads as $ad) {
            $insertAd = [
                'id' => Uuid::v4()->toRfc4122(),
                'meta_id' => $ad['id'],
                'page_meta_id' => $ad['page_id'] ?? null,
                'type' => $ad['system_type'] ?? null,
                'platforms' => json_encode($ad['publisher_platforms'] ?? [], JSON_UNESCAPED_UNICODE)
            ];

            unset($ad['system_type']);
            $insertAd['data'] = json_encode($ad, JSON_UNESCAPED_UNICODE);

            // Execute the upsert and fetch the resulting mapping.
            $stmtMedia->executeStatement($insertAd);
        }

        $connection->commit();
        $io->success('Success!');

        return Command::SUCCESS;
    }
}
