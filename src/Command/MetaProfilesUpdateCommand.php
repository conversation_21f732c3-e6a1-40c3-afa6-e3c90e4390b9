<?php
namespace App\Command;

use App\Entity\Meta\Media;
use App\Entity\Meta\Profile;
use App\Service\MetaService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Helper\ProgressIndicator;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Throwable;
use function Symfony\Component\String\u;

#[AsCommand(
    name: 'app:meta:profiles:update',
    description: 'Add a short description for your command',
)]
class MetaProfilesUpdateCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly MetaService $meta
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('posts', 'p', InputOption::VALUE_OPTIONAL, 'Maximum number of posts', 25000)
            ->addOption('after', null, InputOption::VALUE_OPTIONAL, 'Continue after profile')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $limit = (int)$input->getOption('posts');

        foreach ($this->getSources() as $platform => $source) {
            $stream = fopen(dirname(__DIR__, 2) . '/data/source' . $source, 'r');

            $afterOption = $input->getOption('after');
            $scan = $afterOption === null;

            while (($row = fgetcsv($stream)) !== false) {
                $username = $row[0];

                if (!$scan && ($afterOption !== null && $username !== $input->getOption('after'))) {
                    $io->note("Skipping @$username...");
                    continue;
                }

                if (!$scan) {
                    $io->note("Reached @$username. Continuing...");
                    $scan = true;
                }

                $io->title("Processing @$username ($platform)");
                $profile = $this->entityManager->getRepository(Profile::class)->findOneBy(['username' => $username]);

                if (!$profile) {
                    $profile = new Profile();
                }

                $progress = new ProgressBar($output);
                $progress->start();

                $fn = function($i, $max) use ($progress) {
                    $progress->setMaxSteps($max);
                    $progress->setProgress($i);
                };

                $data = [];

                try {
                    $data = $this->meta->getInstagramData($username, $limit, progressCallback: $fn)['business_discovery'];

                    if (array_key_exists('error', $data)) {
                        if ($data['error'] && u($data['message'])->lower()->containsAny('invalid user')) {
                            $io->newLine(2);
                            $io->warning("Skipping $username as it was not found.");
                            continue;
                        }
                    }
                } catch (Throwable) {
                    $io->newLine(2);
                    $io->warning("Skipping $username because of an unknown error.");
                    continue;
                }

                $profile = $this->entityManager->getRepository(Profile::class)->findOneBy(['metaId' => $data['id']]);

                $progress->finish();
                $io->newLine(2);
                $io->success('🎉 Media processed!');

                if (!$profile) {
                    $profile = new Profile();
                }

                $insideData = $data;
                unset($insideData['id'], $insideData['metaId']);
                unset($insideData['media']);

                $profile
                    ->setMetaId($data['id'])
                    ->setUsername($data['username'])
                    ->setPlatform($platform)
                    ->setData($insideData)
                ;

                $indicator = new ProgressIndicator($output, 'verbose');
                $indicator->start('Persisting / updating media into database...');

                foreach ($data['media']['data'] as $mediaArray) {
                    $media = $this->entityManager->getRepository(Media::class)->findOneBy(['metaId' => $mediaArray['id']]);

                    if (!$media) {
                        $media = new Media();
                    }

                    $insideMedia = $mediaArray;
                    unset($insideMedia['id']);
                    $media
                        ->setMetaId($mediaArray['id'])
                        ->setData($insideMedia)
                    ;

                    $profile->addMedia($media);
                    $indicator->advance();
                }

                $io->newLine(2);
                $indicator->finish('🎉 Media updated!');

                $this->entityManager->persist($profile);
                $this->entityManager->flush();
                $this->entityManager->clear();

                $io->newLine(2);
            }

            fclose($stream);
        }

        $io->success('Success! All done!');
        return Command::SUCCESS;
    }

    private function getSources(): array {
        return [
            'instagram' => '/instagram/profiles.csv'
        ];
    }
}
