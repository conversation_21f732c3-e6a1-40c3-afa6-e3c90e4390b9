<?php

namespace App\Command;

use App\Entity\Meta\Media;
use App\Entity\Meta\Profile;
use App\Entity\Social\TrackingGroup\DataSource;
use App\Helper\Social\Meta\ReactionsHelper;
use Aws\Exception\AwsException;
use Aws\S3\S3Client;
use DateTime;
use DateTimeImmutable;
use DateTimeInterface;
use DateTimeZone;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Generator;
use GuzzleHttp\Promise;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use function Symfony\Component\String\u;

#[AsCommand(
    name: 'app:s3:migrate',
    description: 'Add a short description for your command',
)]
class S3MigrateCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly S3Client $s3,
        private readonly string $bucket
    )
    {
        @ini_set('memory_limit', '2G');
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $batchSize = 10;
        $promises = [];
        $counter = 0;

        foreach ($this->getProfiles() as $profile) {
            $io->info("Uploading {$profile['username']} to S3...");

            $key = 'social/profiles/' . str_replace(':', '/', $profile['platform']) . '/' . $profile['id'] . '.json';
            $promises[$profile['id']] = $this->s3->putObjectAsync([
                'Bucket' => $this->bucket,
                'Key'    => $key,
                'Body'   => json_encode($profile, JSON_UNESCAPED_UNICODE),
            ]);

            unset($profile);
            $counter++;

            if ($counter % $batchSize === 0) {
                $this->settleAndReport($promises, $io);
                $promises = []; // Reset for next batch
            }
        }

        // Process remaining if any
        if (!empty($promises)) {
            $this->settleAndReport($promises, $io);
        }

        $io->success('All done!');
        return self::SUCCESS;
    }

    private function settleAndReport(array $promises, SymfonyStyle $io): void
    {
        $results = Promise\Utils::settle($promises)->wait();

        foreach ($results as $profileId => $result) {
            if ($result['state'] === 'fulfilled') {
                $io->success("✅ Uploaded profile {$profileId}");
            } else {
                /** @var AwsException $reason */
                $reason = $result['reason'];
                $io->error(sprintf(
                    "❌ Failed to upload profile %s: %s",
                    $profileId,
                    $reason->getMessage()
                ));
            }
        }
    }

    /**
     * Generator to fetch profiles one by one for use in foreach loops.
     */
    private function getProfiles(): Generator
    {
        $connection = $this->entityManager->getConnection();

        // Your SQL query with correlated subqueries.
        $sql = <<<'SQL'
            SELECT 
                p.id,
                p.meta_id,
                p.username,
                p.platform,
                p.data,
                COALESCE(
                    (
                        SELECT jsonb_agg(jsonb_build_object(
                            'id', h.id,
                            'data', h.data,
                            'created_at', h.created_at
                        ))
                        FROM meta_profile_history AS h
                        WHERE h.profile_id = p.id
                    ),
                    '[]'::jsonb
                ) AS history,
                COALESCE(
                    (
                        SELECT 
                            '[' || string_agg(
                                json_build_object(
                                    'id', m.id,
                                    'meta_id', m.meta_id,
                                    'data', m.data,
                                    'origin', m.origin,
                                    'comments', (
                                        SELECT COALESCE(
                                            json_agg(
                                                json_build_object(
                                                    'id', c.id,
                                                    'meta_id', c.meta_id,
                                                    'user_meta_id', c.user_meta_id,
                                                    'data', c.data,
                                                    'created_at', c.created_at,
                                                    'updated_at', c.updated_at
                                                )
                                            ),
                                            '[]'
                                        )
                                        FROM meta_comment AS c
                                        WHERE c.media_id = m.id
                                    ),
                                    'created_at', m.created_at,
                                    'updated_at', m.updated_at
                                )::TEXT, ','
                            ) || ']'
                        FROM meta_media AS m
                        WHERE m.profile_id = p.id
                    ),
                    '[]'
                ) AS media,
                p.created_at,
                p.updated_at
            FROM meta_profile AS p;
        SQL;

        $tzAlbania = new DateTimeZone('Europe/Tirane');

        // Stream each row without loading all into memory
        foreach ($connection->iterateAssociative($sql) as $row) {
            // Decode core JSON fields
            $row['data']    = json_decode($row['data'],    true);
            $row['history'] = json_decode($row['history'] ?? '[]', true);

            // Decode nested a media array and its comments
            $row['media'] = array_map(function (array $media) {
                $media['comments'] = !is_array($media['comments']) ? json_decode($media['comments'] ?? '[]', true) : $media['comments'];
                return $media;
            }, json_decode($row['media'] ?? '[]', true));

            foreach ($row['media'] as &$media) {
                $this->transformObjectDateTimeProperties($media['data'], $tzAlbania, 'albania_', true);

                if ($row['platform'] === DataSource::Facebook->value) {
                    $media['data']['sentiment'] = ReactionsHelper::getSentimentFromReactions($media['data']);
                }

                if (array_key_exists('comments', $media['data']) && count($media['data']['comments']) > 0) {
                    foreach ($media['data']['comments'] as &$comment) {
                        $this->transformObjectDateTimeProperties($comment['data'], $tzAlbania, 'albania_', true);

                        if ($row['platform'] === DataSource::Facebook->value) {
                            $comment['data']['sentiment'] = ReactionsHelper::getSentimentFromReactions($comment['data']);
                        }
                    }
                }
            }

            yield $row;
        }
    }

    private function transformObjectDateTimeProperties(array &$object, DateTimeZone $targetTimeZone, string $prefix, bool $includeHourOnlyVar): void {
        $prefix = u($prefix)->ensureEnd('_')->toString();

        if (array_key_exists('created_time', $object)) {
            $created = new DateTimeImmutable($object['created_time']);
            $createdTarget = $created->setTimezone($targetTimeZone);
            $object[$prefix . 'created_time'] = $createdTarget->format('Y-m-d H:i:s');

            if ($includeHourOnlyVar) {
                $object[$prefix . 'created_hour'] = $createdTarget->format('H');
            }
        }

        if (array_key_exists('updated_time', $object)) {
            $updated = new DateTimeImmutable($object['updated_time']);
            $updatedTarget = $updated->setTimezone($targetTimeZone);
            $object[$prefix . 'updated_time'] = $updatedTarget->format('Y-m-d H:i:s');

            if ($includeHourOnlyVar) {
                $object[$prefix . 'updated_hour'] = $updatedTarget->format('H');
            }
        }

        if (array_key_exists('timestamp', $object)) {
            $timestamp = new DateTimeImmutable($object['timestamp']);
            $timestampTarget = $timestamp->setTimezone($targetTimeZone);
            $object[$prefix . 'timestamp'] = $timestampTarget->format('Y-m-d H:i:s');

            if ($includeHourOnlyVar) {
                $object[$prefix . 'timestamp_hour'] = $timestampTarget->format('H');
            }
        }
    }
}
