<?php

namespace App\Command;

use App\Service\MetaService;
use Exception;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

#[AsCommand(
    name: 'app:meta:auth',
    description: 'Authenticate with Meta and save long-lived tokens.',
)]
class MetaAuthCommand extends Command
{
    public function __construct(
        private readonly RouterInterface $router,
        private readonly MetaService $meta
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('platform', InputArgument::REQUIRED, 'Platform to authenticate (facebook or instagram)')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $platform = $input->getArgument('platform');
        if (!in_array($platform, ['facebook', 'instagram'])) {
            $io->error("Invalid platform. Use 'facebook' or 'instagram'.");
            return Command::FAILURE;
        }

        // Automatically generate the redirect URI using the router.
        $redirectUri = $this->router->generate('oauth_callback', referenceType: UrlGeneratorInterface::ABSOLUTE_URL);
        $io->note("Using redirect URI: {$redirectUri}");

        $oauthUrl = $this->meta->getOAuthUrl($platform, $redirectUri);
        $io->writeln("Open the following URL in your browser to authorize the app:");
        $io->writeln("<href=$oauthUrl>$oauthUrl</>");

        $code = $io->ask("After authorizing, please enter the 'code' parameter from the redirect URL: ");

        if (!$code) {
            $output->writeln("No code provided. Aborting.");
            return Command::FAILURE;
        }

        try {
            $accessToken = $this->meta->exchangeCode($platform, $code, $redirectUri);
            $output->writeln("Successfully authenticated for {$platform}.");
            $output->writeln("Access Token: {$accessToken}");
        } catch (Exception $e) {
            $output->writeln("Authentication failed: " . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
