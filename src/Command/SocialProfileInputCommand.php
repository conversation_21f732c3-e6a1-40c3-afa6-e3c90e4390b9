<?php

namespace App\Command;

use App\Entity\Social\TrackingGroup;
use App\Service\Social\ProfileUpdateService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:social:profile:input',
    description: 'Add a short description for your command',
)]
class SocialProfileInputCommand extends Command
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly ProfileUpdateService $profileUpdateService,
        private readonly LoggerInterface $logger
    )
    {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('id', InputArgument::OPTIONAL, 'The profile id')
            ->addOption('tracking-group', 'tg', InputOption::VALUE_OPTIONAL, 'Tracking group for filters')
        ;
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $id = $input->getArgument('id');

        try {
            $this->profileUpdateService
                ->setIO($io)
                ->setPostsLimit(500)
                ->setCommentsLimit(100)
                ->setAnalyzeProfileWithAI(false)
                ->setAnalyzeMediaWithAI(false)
                ->setForceIgnoreComments(true)
            ;

            if ($input->getOption('tracking-group')) {
                $trackingGroup = $this->entityManager->getRepository(TrackingGroup::class)->findOneBy(['id' => $input->getOption('tracking-group')]);
                $this->profileUpdateService
                    ->setPostsLimit($trackingGroup->getMaximumPostsPerProfile())
                    ->setFilterEntityId($trackingGroup->getId())
                    ->setFilterEntityType(get_class($trackingGroup))
                ;
            }

            $this->profileUpdateService->update($id, 'meta:facebook', true);
        } catch (Exception $e) {
            $io->error($e->getMessage());
            $this->logger->error($e->getMessage(), $e->getTrace());
        }

        return Command::SUCCESS;
    }
}
