<?php
namespace App\Command;

use App\Entity\Meta\Profile;
use App\Entity\Social\TrackingGroup;
use App\Service\Social\ProfileUpdateService;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Uid\Uuid;

#[AsCommand(
    name: 'app:social:force-update'
)]
class SocialForceUpdateCommand extends Command
{
    public function __construct(
        private readonly ProfileUpdateService $profileUpdateService,
        private readonly EntityManagerInterface $entityManager
    )
    {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->info('Starting...');

        /** @var TrackingGroup $trackingGroup */
        $trackingGroup = $this->entityManager->getRepository(TrackingGroup::class)->find(Uuid::fromString('0195f3b2-c51e-7bda-b811-0904f600bd6c'));

        $ids = [];
        $usernames = [];

        foreach ($trackingGroup->getList() as $p) {
            if (str_contains($p, ':')) {
                list($username, $profileId) = explode(':', $p);

                $ids[] = $profileId;
                $usernames[] = $username;
            } else {
                if (is_numeric(str_replace('_', '', $p))) {
                    $ids[] = $p;
                } else {
                    $usernames[] = $p;
                }
            }
        }

        /** @var Profile $profile */
        $profiles = $this->entityManager->getRepository(Profile::class)
            ->createQueryBuilder('p')
            ->where('p.metaId IN (:ids) OR p.username IN (:usernames)')
            ->setParameter('ids', $ids)
            ->setParameter('usernames', $usernames)
            ->getQuery()
            ->getResult()
        ;

        $profilesToReprocess = [];
        foreach ($profiles as $profile) {
            foreach ($profile->getMedia() as $media) {
                if (empty($media->getData())) {
                    $profilesToReprocess[] = $profile;
                    continue 2;
                }
            }
        }

        $count = count($profilesToReprocess);
        $io->info("Processing {$count} entries.");

        foreach ($profilesToReprocess as $profile) {
            $this->profileUpdateService
                ->setIO($io)
                ->setPostsLimit($trackingGroup->getMaximumPostsPerProfile())
                ->setCommentsLimit(100)
                ->setAnalyzeProfileWithAI(true)
                ->setAnalyzeMediaWithAI(false)
                ->setFilterEntityId($trackingGroup->getId())
                ->setFilterEntityType(get_class($trackingGroup))
            ;

            try {
                $this->profileUpdateService->update($profile->getMetaId(), 'meta:facebook');
            } catch (Exception $e) {
                $io->error($e->getMessage());
            }
        }

        return Command::SUCCESS;
    }
}
