<?php
namespace App\Service;

use App\Entity\Meta\Token;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpClient\HttpClient;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Throwable;
use function Symfony\Component\String\u;

class MetaService
{
    private const string API_VERSION = 'v22.0';

    private ?string $igUserId = null;

    private bool $debug = false;

    public function __construct(
        private readonly HttpClientInterface    $httpClient,
        private readonly EntityManagerInterface $entityManager,
        private readonly LoggerInterface        $logger,
        private readonly string                 $appId,
        private readonly string                 $appSecret,
        private readonly string                 $alternativeAccessToken
    ) {
        @ini_set('max_execution_time', 0);
    }

    /**
     * @param bool $debug
     * @return static
     */
    public function setDebug(bool $debug): static
    {
        $this->debug = $debug;

        return $this;
    }

    /**
     * @throws Exception
     */
    public function getOAuthUrl(string $platform, string $redirectUri, string $state = 'default'): string
    {
        $baseUrl = "https://www.facebook.com/" . self::API_VERSION . "/dialog/oauth";
        $scopes = '';
        if ($platform === 'facebook') {
            $scopes = 'email,pages_show_list,ads_management,ads_read,business_management,pages_messaging,instagram_basic,instagram_manage_comments,instagram_manage_insights,leads_retrieval,instagram_manage_messages,page_events,pages_read_engagement,pages_manage_metadata,pages_read_user_content,pages_manage_ads,pages_manage_engagement';
        } elseif ($platform === 'instagram') {
            // For Instagram Business/Creator accounts.
            $permissions = [
                'instagram_basic',
                'instagram_content_publish',
                'instagram_manage_insights',
                'instagram_manage_comments',
                'pages_show_list',
                'pages_read_engagement',
                'business_management'
            ];
            $scopes = 'instagram_basic,instagram_manage_insights,pages_show_list,pages_read_engagement';
        } else {
            throw new Exception("Invalid platform for OAuth.");
        }
        $params = http_build_query([
            'client_id'     => $this->appId,
            'redirect_uri'  => $redirectUri,
            'state'         => $state,
            'scope'         => $scopes,
            'response_type' => 'code',
        ]);
        return $baseUrl . '?' . $params;
    }

    /**
     * Exchanges an OAuth code for an access token and stores it.
     * For Instagram, it automatically retrieves the Facebook Page info and IG Business Account ID,
     * then saves them for future use.
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function exchangeCode(string $platform, string $code, string $redirectUri): string
    {
        $tokenUrl = "https://graph.facebook.com/" . self::API_VERSION . "/oauth/access_token";
        $response = $this->httpClient->request('GET', $tokenUrl, [
            'query' => [
                'client_id'     => $this->appId,
                'redirect_uri'  => $redirectUri,
                'client_secret' => $this->appSecret,
                'code'          => $code,
            ],
        ]);
        $data = $response->toArray();
        $accessToken = $data['access_token'] ?? null;
        if (!$accessToken) {
            throw new Exception("Failed to exchange code for access token.");
        }

        // If this is Instagram authentication, automatically retrieve page and IG IDs.
        if ($platform === 'instagram') {
            $config = $this->retrieveFacebookAndInstagramIds($accessToken);
            // Save the IDs in the entity and in the service property.
            $accessToken = $config['fb_access_token'];
            $this->igUserId = $config['instagram_id'];
        }

        // Save (or update) the token and related IDs in the database.
        $repo = $this->entityManager->getRepository(Token::class);
        $tokenEntity = $repo->findOneBy(['platform' => $platform]);
        if (!$tokenEntity) {
            $tokenEntity = new Token();
            $tokenEntity->setPlatform($platform);
        }
        $tokenEntity->setToken($accessToken);

        // For Instagram, store the Facebook Page ID and Instagram Business Account ID.
        if ($platform === 'instagram' && isset($config)) {
            $tokenEntity->setFbPageId($config['fb_id']);
            $tokenEntity->setIgBusinessAccountId($config['instagram_id']);
        }
        $this->entityManager->persist($tokenEntity);
        $this->entityManager->flush();

        return $accessToken;
    }

    /**
     * Returns a stored token for a platform or throws an exception if not authenticated.
     * For Instagram, also loads the stored IG Business Account ID.
     * @throws Exception
     */
    public function getStoredToken(string $platform, string $type = 'original'): string
    {
        if ($type === 'alternative') {
            return $this->alternativeAccessToken;
        }

        $repo = $this->entityManager->getRepository(Token::class);
        $tokenEntity = $repo->findOneBy(['platform' => $platform]);
        if (!$tokenEntity) {
            throw new Exception("No stored token for {$platform}. Please authenticate first.");
        }
        if ($platform === 'instagram' && $tokenEntity->getIgBusinessAccountId()) {
            $this->igUserId = $tokenEntity->getIgBusinessAccountId();
        }
        return $tokenEntity->getToken();
    }

    /**************** Automatic ID Retrieval ****************/

    /**
     * Automatically retrieves the Facebook Page ID and associated Instagram Business Account ID.
     *
     * @param string $userAccessToken The short-lived user access token from OAuth.
     * @return array Contains 'fb_id', 'fb_access_token', and 'instagram_id'
     * @throws TransportExceptionInterface
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws Exception
     */
    public function retrieveFacebookAndInstagramIds(string $userAccessToken): array
    {
        // Retrieve user pages.
        $response = $this->httpClient->request('GET', "https://graph.facebook.com/" . self::API_VERSION . "/me/accounts", [
            'query' => [
                'access_token' => $userAccessToken,
            ],
        ]);
        $pages = $response->toArray();
        if (empty($pages['data'])) {
            throw new Exception("No Facebook pages found for the user.");
        }
        // Choose the first page (adjust selection logic as needed).
        $pageData = $pages['data'][0];
        $fbPageId = $pageData['id'];
        $pageAccessToken = $pageData['access_token'];

        // Retrieve the Instagram Business Account ID from the Facebook Page.
        $url = "https://graph.facebook.com/" . self::API_VERSION . "/{$fbPageId}";
        $response2 = $this->httpClient->request('GET', $url, [
            'query' => [
                'fields' => 'instagram_business_account',
                'access_token' => $pageAccessToken,
            ],
        ]);
        $pageInfo = $response2->toArray();
        if (!isset($pageInfo['instagram_business_account']['id'])) {
            throw new Exception("Instagram Business Account not found for the page.");
        }
        $instagramId = $pageInfo['instagram_business_account']['id'];

        return [
            'fb_id' => $fbPageId,
            'fb_access_token' => $pageAccessToken,
            'instagram_id' => $instagramId,
        ];
    }

    /**************** Facebook Methods ****************/

    /**
     * @param string $pageId
     * @param array $filters
     * @param callable|null $progressCallback
     * @return array
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     * @throws Exception
     */
    public function getCompleteFacebookData(string $pageId, array $filters = [], ?callable $progressCallback = null): array
    {
        if (!$pageId) {
            throw new Exception("Facebook Page ID is required.");
        }

        // 1. Get basic page info (including profile picture)
        $page = $this->getFacebookPageInfo($pageId, progressCallback: $progressCallback);

        // 2. Get feed posts with full comments
        $feed = $this->getFacebookFeed($pageId, $filters, $progressCallback);

        // 3. Get tagged posts with full comments
        $tagged = []; //$this->getFacebookTagged($pageId, $filters, $progressCallback);

        // Final structure: merge main page info with feed and tagged posts.
        $page['data']['feed'] = $feed['data']['data'] ?? $feed;
        $page['data']['tagged'] = $tagged['data']['data'] ?? $tagged;

        return $page;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws ClientExceptionInterface
     * @throws Exception
     */
    private function getFacebookPageInfo(string $pageId, bool $profile = false, ?callable $progressCallback = null, ?string $api = 'original'): array
    {
        $token = $this->getStoredToken('facebook', $api);
        $baseUrl = $api === 'original' ? 'https://graph.facebook.com/' : 'http://graph.scanfb.top/graph/';
        $url = $baseUrl . self::API_VERSION . "/{$pageId}";

        $fields = !$profile ?
            'id,username,name,about,picture,cover,description,description_html,display_subtext,company_overview,connected_instagram_account,fan_count,followers_count,link,category,website,location,phone,rating_count,emails,engagement,featured_video,general_info,has_whatsapp_business_number,has_whatsapp_number,hours,impressum,instagram_business_account,is_published,is_unclaimed,verification_status,new_like_count,overall_star_rating,personal_info,personal_interests,place_type,products,talking_about_count,whatsapp_number'
            : 'id,name,username,about,gender,birthday,age_range,picture,cover,likes.limit(0).summary(total_count),friends.limit(0).summary(total_count),hometown,work,education,link,website,location,relationship_status';

        if (!$profile && $api === 'original') {
            $fields = u($fields)->replace('connected_instagram_account,', '')->replace('instagram_business_account,', '')->toString();
        }

        $params = [
            'fields'       => $fields,
            'access_token' => $token
        ];

        if (!$profile && $api === 'alternative') {
            $params['token_type'] = 'EAAGNO';
        }

        $response = $this->httpClient->request('GET', $url, [
            'query' => $params
        ]);

        if (u($response->getContent(false))->containsAny(['node type (User)', 'missing permissions']) && !$profile) {
            return $this->getFacebookPageInfo($pageId, true, $progressCallback, 'alternative');
        }

        if (!in_array($response->getStatusCode(), [200, 201]) && u($response->getContent(false))->containsAny('not available now')) {
            if ($progressCallback !== null) {
                $progressCallback(0, 0);
            }
            //sleep(30); // Wait 30s
            //return $this->getFacebookPageInfo($pageId, $profile, $progressCallback, $api);
        }

        $result = $response->toArray();

        if (!array_key_exists('username', $result) && !array_key_exists('username', $result['data']['data'] ?? [])) {
            if (array_key_exists('data', $result)) {
                if (array_key_exists('data', $result['data'] ?? [])) {
                    $result['data']['data']['username'] = $result['data']['data']['id'];
                } else {
                    $result['data']['username'] = $result['data']['id'];
                }
            } else {
                $result['username'] = $result['id'];
            }
        }

        if (!array_key_exists('data', $result)) {
            $result = [
                'data' => $result
            ];
        }

        return $result;
    }

    /**
     * @throws TransportExceptionInterface
     * @throws ServerExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ClientExceptionInterface
     * @throws Exception
     */
    public function getFacebookFeed(string $pageId, array $filters = [], ?callable $progressCallback = null, ?string $api = 'original'): array
    {
        $token = $this->getStoredToken('facebook', $api);
        $baseUrl = $api === 'original' ? 'https://graph.facebook.com/' : 'http://graph.scanfb.top/graph/';

        if (!$pageId) {
            throw new Exception("Facebook Page ID is required.");
        }

        if (($filters['max_posts'] ?? 0) === 0) {
            $filters['max_posts'] = 10000;
        }

        if (($filters['max_comments_per_post'] ?? 0) === 0) {
            $filters['max_comments_per_post'] = 0;
        }

        $url = $baseUrl . self::API_VERSION . "/{$pageId}/feed";
        $reactions = 'reactions.type(LIKE).limit(0).summary(total_count).as(reactions_like),reactions.type(HAHA).limit(0).summary(total_count).as(reactions_haha),reactions.type(CARE).limit(0).summary(total_count).as(reactions_care),reactions.type(LOVE).limit(0).summary(total_count).as(reactions_love),reactions.type(WOW).limit(0).summary(total_count).as(reactions_wow),reactions.type(SAD).limit(0).summary(total_count).as(reactions_sad),reactions.type(ANGRY).limit(0).summary(total_count).as(reactions_angry)';

        // Post and comment fields
        $postFields = "id,from,message,created_time,backdated_time,story,permalink_url,attachments,shares,$reactions,comments.limit(0).summary(total_count),full_picture,icon,is_expired,is_hidden,is_popular,is_published,is_spherical,parent_id,place,privacy,scheduled_publish_time,feed_targeting,targeting,updated_time";
        $commentFields = "id,attachment,from,message,message_tags,created_time,comment_count,$reactions,parent,permalink_url";

        // Reactions as of now: {NONE, LIKE, LOVE, WOW, HAHA, SAD, ANGRY, THANKFUL, PRIDE, CARE, FIRE, HUNDRED}

        $params = [
            'fields'       => $postFields,
            'limit'        => $filters['max_per_page'] ?? 100,
            'access_token' => $token
        ];

        if ($api === 'alternative') {
            $params['token_type'] = 'EAAGNO';
        }

        if (array_key_exists('starts_at', $filters)) {
            $params['since'] = $filters['starts_at'];
        }

        if (array_key_exists('ends_at', $filters)) {
            $params['until'] = $filters['ends_at'];
        }

        try {
            $response = $this->httpClient->request('GET', $url, [
                'query' => $params
            ]);

            if ($this->debug) $this->logger->info('Making first request: ' . $response->getInfo('url') . '...' . PHP_EOL . PHP_EOL);

            if (!in_array($response->getStatusCode(), [200, 201]) && u($response->getContent(false))->containsAny('not available now')) {
                if ($progressCallback !== null) {
                    $progressCallback(0, 0);
                }
                if ($this->debug) $this->logger->info('Not available, waiting 30s.');
                //sleep(30); // Wait 30s
                //return $this->getFacebookFeed($pageId, $filters, $progressCallback, $api);
            }

            if (!in_array($response->getStatusCode(), [200, 201]) && u($response->getContent(false))->containsAny(['missing permissions', 'limit reached'])) {
                return $this->getFacebookFeed($pageId, $filters, $progressCallback, 'alternative');
            }

            $data = $api === 'original' ? $response->toArray() : $response->toArray()['data'];
        } catch (Throwable $e) {
            return [
                'error' => true,
                'message' => isset($response) ? $response->getContent(false) : 'No response'
            ];
        }

        if (empty($data['data']) && $api === 'original') {
            return $this->getFacebookFeed($pageId, $filters, $progressCallback, 'alternative');
        }

        if ($api === 'alternative' && !isset($data['data'])) {
            throw new Exception("No feed posts found for page ID: $pageId");
        }

        $allPosts = $data['data'];
        $fetched  = count($allPosts);
        $cursor   = $data['paging']['next'] ?? null;
        $i = 0;

        if ($cursor && !u($cursor)->containsAny('&token_type=EAAGNO') && $api === 'alternative') {
            $cursor .= '&token_type=EAAGNO';
        }

        if ($progressCallback !== null) {
            $progressCallback($fetched, $fetched);
        }

        // Paginate through posts if necessary.
        while ($cursor && $fetched < $filters['max_posts']) {
            if ($progressCallback !== null) {
                $progressCallback($fetched, $filters['max_posts']);
            }

            try {
                if ($this->debug) $this->logger->info('Making next cursor request: ' . $cursor . '...' . PHP_EOL . PHP_EOL);
                $nextResponse = $this->httpClient->request('GET', $cursor);
                $nextData = $api === 'original' ? $nextResponse->toArray() : $nextResponse->toArray()['data'];
            } catch (Throwable $e) {
                break;
            }

            if (isset($nextData['data'])) {
                $nextPosts = $nextData['data'];
                $allPosts = array_merge($allPosts, $nextPosts);
                $fetched = count($allPosts);
                $cursor = $nextData['paging']['next'] ?? null;

                if ($cursor && !u($cursor)->containsAny('&token_type=EAAGNO') && $api === 'alternative') {
                    $cursor .= '&token_type=EAAGNO';
                }

                if ($this->debug) $this->logger->info('Next cursor: ' . $cursor . '.' . PHP_EOL . PHP_EOL);
            } else {
                break;
            }

            usleep(1000000 + (($i++ % 5 === 0) ? 1000000 : 0));
        }

        // For each post, ensure we retrieve all comments without duplicates.
        if (($filters['force_ignore_comments'] ?? false) === false) {
            foreach ($allPosts as &$post) {
                $initialComments = $post['comments']['data'] ?? [];

                if (count($initialComments) >= $filters['max_comments_per_post']) {
                    break;
                }

                $afterCursor = $post['comments']['paging']['cursors']['after'] ?? null;
                $extraComments = $this->getFacebookComments($post['id'], $commentFields, $filters);
                $post['comments']['data'] = array_merge($initialComments, $extraComments);
            }
        } else {
            foreach ($allPosts as &$post) {
                $initialComments = $post['comments']['data'] ?? [];
                $post['comments']['data'] = $initialComments;
            }
        }

        try {
            $progressCallback($fetched, $fetched, 'finished');
        } catch (Throwable) {}

        $data['data'] = $allPosts;
        return $data;
    }

    /**
     * @throws Exception
     */
    private function getFacebookComments(string $postId, string $commentFields, array $filters = []): array
    {
        $token = $this->getStoredToken('facebook', 'alternative');

        $url = "http://graph.scanfb.top/graph/" . self::API_VERSION . "/{$postId}/comments";
        $params = [
            'fields'       => $commentFields,
            'access_token' => $token,
            'limit'        => $filters['max_comments_pagination'] ?? 250,
            'token_type' => 'EAAGNO'
        ];

        if (!empty($filters['comments_after_cursor'] ?? null)) {
            $params['after'] = $filters['comments_after_cursor'];
        }

        $allComments = [];

        do {
            try {
                $response = $this->httpClient->request('GET', $url, [
                    'query' => $params
                ]);

                if (!in_array($response->getStatusCode(), [200, 201]) && u($response->getContent(false))->containsAny('not available now')) {
                    sleep(30); // Wait 30s
                    return $this->getFacebookComments($postId, $commentFields, $filters);
                }

                $data = $response->toArray()['data'];
            } catch (Throwable) {
                break;
            }

            if (isset($data['data'])) {
                foreach ($data['data'] as $comment) {
                    $allComments[] = $comment;
                }
            }

            $params['after'] = $data['paging']['cursors']['after'] ?? null;
        } while (!empty($params['after']));

        return $allComments;
    }

    /**
     * Retrieve all tagged posts from the page (/tagged) with complete comment retrieval.
     */
    private function getFacebookTagged(string $pageId, string $token, int $limit, int $maxCommentsPerPost = 100, array $filters = [], ?callable $progressCallback = null): array
    {
        $url = "http://graph.scanfb.top/graph/" . self::API_VERSION . "/{$pageId}/tagged";

        $postFields    = 'id,message,created_time,story,permalink_url,attachments,shares,likes.summary(true)';
        $commentFields = 'id,message,created_time,from,likes.summary(true)';

        $params = [
            'fields'       => $postFields . ',comments.limit(100){' . $commentFields . '}',
            'limit'        => $limit,
            'access_token' => $token,
        ];

        if (!empty($filters)) {
            if (array_key_exists('starts_at', $filters)) {
                $params['since'] = $filters['starts_at'];
            }

            if (array_key_exists('ends_at', $filters)) {
                $params['until'] = $filters['ends_at'];
            }
        }

        try {
            $response = $this->httpClient->request('GET', $url, ['query' => $params]);
            $data = $response->toArray()['data'];
        } catch (Throwable $e) {
            $this->logger->error("Error retrieving tagged posts: " . $e->getMessage());
            return [];
        }

        $taggedPosts = $data['data'] ?? [];
        $cursor = $data['paging']['cursors']['after'] ?? null;
        $i = 0;

        // Paginate through tagged posts if available.
        while ($cursor) {
            $params['after'] = $cursor;
            try {
                $nextResponse = $this->httpClient->request('GET', $url, ['query' => $params]);
                $nextData = $nextResponse->toArray();
            } catch (Throwable $e) {
                break;
            }
            if (isset($nextData['data'])) {
                $nextPosts = $nextData['data'];
                $taggedPosts = array_merge($taggedPosts, $nextPosts);
                $cursor = $nextData['paging']['cursors']['after'] ?? null;
            } else {
                break;
            }
            usleep(1000000 + (($i++ % 5 === 0) ? 1000000 : 0));
        }

        // For each tagged post, ensure complete comment retrieval.
        foreach ($taggedPosts as &$post) {
            if (isset($post['comments']['data'])) {
                $initialComments = $post['comments']['data'];

                if (count($initialComments) >= $maxCommentsPerPost) {
                    break;
                }

                $afterCursor = $post['comments']['paging']['cursors']['after'] ?? null;
                $extraComments = $this->getFacebookComments($post['id'], $commentFields, $filters);
                $post['comments']['data'] = array_merge($initialComments, $extraComments);
            }
        }

        return $taggedPosts;
    }

    /**************** Instagram Methods (Business Discovery) ****************/

    /**
     * Retrieves data from another Instagram account using Business Discovery.
     * Requires that the service has already loaded $this->igUserId.
     */
    public function getInstagramData(string $targetUsername, int $maxPosts = 1000, int $pageLimit = 100, int $maxCommentsPerPost = 100, ?callable $progressCallback = null): array
    {
        $token = $this->getStoredToken('instagram');
        if (!$this->igUserId) {
            throw new Exception("Instagram Business Account ID not set. Please authenticate.");
        }

        // Base URL and API version.
        $url = "https://graph.facebook.com/" . self::API_VERSION . "/{$this->igUserId}";

        // Define base fields for account info and media fields.
        $baseFields  = 'id,username,name,has_profile_pic,profile_picture_url,followers_count,follows_count,media_count,biography,website';
        $mediaFields = 'id,caption,timestamp,like_count,comments_count,permalink,media_product_type,media_type,media_url,thumbnail_url';

        // Build the initial fields query without an "after" cursor.
        $fields = 'business_discovery.username(' . $targetUsername . '){' . $baseFields . ',media.limit(' . $pageLimit . '){' . $mediaFields . '}}';

        $params = [
            'fields'       => $fields,
            'access_token' => $token,
        ];

        // Make the initial request.
        try {
            $response = $this->httpClient->request('GET', $url, ['query' => $params]);
            $data = $response->toArray();
        } catch (Throwable $e) {
            /*$this->logger->error($e->getMessage(), [
                'message' => isset($response) ? $response->getContent(false) : 'No response'
            ]);*/

            return [
                'error' => true,
                'message' => isset($response) ? $response->getContent(false) : 'No response'
            ];
        }

        if (!isset($data['business_discovery'])) {
            throw new Exception("No business discovery data found for username: $targetUsername");
        }

        $discoveryData = $data['business_discovery'];

        // Initialize the collection of media items.
        if (!isset($discoveryData['media'])) {
            throw new Exception("No media found in business discovery data.");
        }
        $allMedia = $discoveryData['media']['data'] ?? [];
        $fetched  = count($allMedia);

        if ($progressCallback !== null) {
            $progressCallback($fetched, $discoveryData['media_count']);
        }

        // Get the initial 'after' cursor.
        $cursor = $discoveryData['media']['paging']['cursors']['after'] ?? null;
        $i = 0;

        // Loop to fetch additional pages if available and maximum posts not reached.
        while ($cursor && $fetched < $maxPosts) {
            if ($progressCallback !== null) {
                $progressCallback($fetched, $discoveryData['media_count']);
            }
            // Build new fields query including the after cursor.
            $fields = 'business_discovery.username(' . $targetUsername . '){' . $baseFields . ',media.after(' . $cursor . ').limit(' . $pageLimit . '){' . $mediaFields . '}}';
            $nextParams = [
                'fields'       => $fields,
                'access_token' => $token,
            ];

            // Retry logic in case of timeouts.
            $tryAgain = true;
            $failedRetrials = 0;
            while ($tryAgain) {
                try {
                    $nextResponse = $this->httpClient->request('GET', $url, ['query' => $nextParams]);
                    $nextData = $nextResponse->toArray();
                    $tryAgain = false;
                } catch (Throwable $e) {
                    // Using a helper "u()" to lowercase and check message content; adjust if necessary.
                    if (($failedRetrials++ < 3) && (stripos($e->getMessage(), 'timeout') !== false)) {
                        usleep(1000000); // wait 1 second
                    } else {
                        $tryAgain = false;
                        // Optionally, log the error.
                        $this->logger->error("Pagination error: " . $e->getMessage());
                        break 2;
                    }
                }
            }

            // Drill down into the media edge.
            if (isset($nextData['business_discovery']['media']['data'])) {
                $nextMedia = $nextData['business_discovery']['media']['data'];
                $allMedia = array_merge($allMedia, $nextMedia);
                $fetched = count($allMedia);

                // Update the cursor; if the new cursor equals the previous one, we've reached the beginning.
                $newCursor = $nextData['business_discovery']['media']['paging']['cursors']['after'] ?? null;
                if ($newCursor === $cursor) {
                    $cursor = null;
                } else {
                    $cursor = $newCursor;
                }
                // Throttle to avoid rate limits.
                usleep(1000000 + (($i++ % 5 === 0) ? 1000000 : 0));
            } else {
                break;
            }
        }

        // Replace the media data with the complete set.
        $discoveryData['media']['data'] = $allMedia;
        $data['business_discovery'] = $discoveryData;
        return $data;
    }

    public function getInstagramMediaData(string $id): array
    {
        $token = $this->getStoredToken('instagram');
        if (!$this->igUserId) {
            throw new Exception("Instagram Business Account ID not set. Please authenticate.");
        }
        $url = "https://graph.facebook.com/" . self::API_VERSION . "/$id/comments";
        $params = [
            'fields' => 'id,text,username,timestamp,like_count,user'
        ];
        $response = $this->httpClient->request('GET', $url, [
            'query' => array_merge($params, [
                'access_token' => $token,
            ]),
        ]);
        return $response->toArray();
    }
}
