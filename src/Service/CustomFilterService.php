<?php
namespace App\Service;

use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;

class CustomFilterService
{
    public function __construct(
        private EntityManagerInterface $em
    )
    {}

    public function applyFilters(array $baseQuery, string $alias, string $field, array $filters): array
    {
        $connection = $this->em->getConnection();
        $params = $baseQuery['parameters'];
        $types = $baseQuery['types'] ?? [];
        $sql = $baseQuery['sql'];

        foreach ($filters as $index => $filter) {
            $key = trim($filter['key'] ?? '');
            $operator = $filter['operator'] ?? 'equals';
            $value = $filter['value'] ?? null;

            if (!$this->isValidKey($key)) continue;

            $paramKey = "filter_$index";
            $jsonCondition = $this->buildJsonCondition("$alias.$field", $operator, $paramKey, $value);

            $sql = $this->addWhereCondition($sql, $jsonCondition);
            $params = array_merge($params, $this->getParametersForOperator($operator, $paramKey, $key, $value));
        }

        return $connection->executeQuery($sql, $params, $types)->fetchAllAssociative();
    }

    private function buildJsonCondition(string $field, string $operator, string $paramKey, $value): string
    {
        return match($operator) {
            'equals' => "$field @> :{$paramKey}_contains",
            'not_equals' => "NOT $field @> :{$paramKey}_contains",
            'is_empty' => "NOT EXISTS (
                SELECT 1 FROM jsonb_array_elements($field) elem 
                WHERE elem->>'key' = :{$paramKey}_key 
                AND elem->>'value' IS NOT NULL 
                AND elem->>'value' <> '' 
                AND elem->>'value' <> 'null'
            )",
            'is_not_empty' => "EXISTS (
                SELECT 1 FROM jsonb_array_elements($field) elem 
                WHERE elem->>'key' = :{$paramKey}_key 
                AND elem->>'value' IS NOT NULL 
                AND elem->>'value' <> '' 
                AND elem->>'value' <> 'null'
            )",
            default => '1=1'
        };
    }

    private function getParametersForOperator(string $operator, string $paramKey, string $key, $value): array
    {
        return match($operator) {
            'equals', 'not_equals' => [
                "{$paramKey}_contains" => json_encode([['key' => $key, 'value' => $value]]),
            ],
            'is_empty', 'is_not_empty' => [
                "{$paramKey}_key" => $key
            ],
            default => []
        };
    }

    private function addWhereCondition(string $sql, string $condition): string
    {
        if (stripos($sql, 'WHERE') === false) {
            return "$sql WHERE $condition";
        }
        return preg_replace('/(WHERE)/i', "$1 $condition AND ", $sql, 1);
    }

    private function isValidKey(string $key): bool
    {
        return preg_match('/^[a-zA-Z0-9_-]+$/', $key) === 1;
    }
}