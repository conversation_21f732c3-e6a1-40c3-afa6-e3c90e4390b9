<?php
namespace App\Service;

use App\Repository\Meta\TokenRepository;
use Exception;
use RuntimeException;
use Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\DecodingExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Psr\Log\LoggerInterface;

class FacebookAdsArchiveService
{
    /**
     * Maximum results per API page request.
     */
    public const int MAX_LIMIT_PER_PAGE = 100;

    /**
     * @param HttpClientInterface   $httpClient
     * @param TokenRepository       $tokenRepository A repository to fetch the stored Facebook API token.
     * @param LoggerInterface       $logger
     */
    public function __construct(
        private readonly HttpClientInterface $httpClient,
        private readonly TokenRepository     $tokenRepository,
        private readonly LoggerInterface     $logger
    ) {}

    /**
     * Fetches Facebook Ads Archive data.
     *
     * One of the search criteria is mandatory:
     * - "search_terms" as a single string, OR
     * - "search_page_ids" as an array of IDs.
     *
     * @param string $type "general" or "politics"
     * @param array $countries Countries filter as required by the API (e.g. ISO code or ALL)
     * @param string $startDate Start date in Y-m-d format (or another acceptable format)
     * @param string|null $endDate Optional end date in Y-m-d format
     * @param array $searchCriteria Array containing either:
     *                                    - 'search_terms' => string, or
     *                                    - 'search_page_ids' => array
     * @param array $filters (Optional) Array of filters. Example:
     *                                    [ 'filter_required_disclaimer' => true ]
     * @param int|null $limit Optional integer limit to stop making further API calls.
     *
     * @return array
     *
     * @throws ClientExceptionInterface
     * @throws DecodingExceptionInterface
     * @throws RedirectionExceptionInterface
     * @throws ServerExceptionInterface
     * @throws TransportExceptionInterface
     *
     */
    public function fetchAdsArchive(
        string $type,
        array $countries,
        string $startDate,
        ?string $endDate = null,
        array $searchCriteria = [],
        array $filters = [],
        ?int $limit = null
    ): array {
        // Validate that at least one search criterion is provided.
        if (
            !isset($searchCriteria['search_terms']) &&
            !isset($searchCriteria['search_page_ids'])
        ) {
            throw new RuntimeException('At least one search criterion ("search_terms" or "search_page_ids") must be provided.');
        }

        // Retrieve the access token from the database.
        $accessTokenEntity = $this->tokenRepository->findOneBy(['platform' => 'facebook']);
        if (!$accessTokenEntity) {
            $this->logger->error('Facebook access token not found in database.');
            throw new RuntimeException('Access token not found');
        }
        $token = $accessTokenEntity->getToken();

        $dataCollected = [];
        $nextUrl = null;

        // Map our local "type" to the API's ad_type enum.
        $adType = (strtolower($type) === 'politics')
            ? 'POLITICAL_AND_ISSUE_ADS'
            : 'ALL';

        // Build the base query parameters.
        $queryParams = [
            'access_token'         => $token,
            'ad_active_status'     => 'ALL',
            'ad_delivery_date_min' => $startDate,
            'ad_reached_countries' => json_encode($countries),
            'ad_type'              => $adType,
            'limit'                => self::MAX_LIMIT_PER_PAGE,
            'fields'               => implode(',', [
                'id',
                'ad_creation_time',
                'ad_creative_bodies',
                'ad_creative_link_captions',
                'ad_creative_link_descriptions',
                'ad_creative_link_titles',
                'ad_delivery_start_time',
                'ad_delivery_stop_time',
                'ad_snapshot_url',
                'age_country_gender_reach_breakdown',
                'beneficiary_payers',
                'br_total_reach',
                'bylines',
                'currency',
                'delivery_by_region',
                'demographic_distribution',
                'estimated_audience_size',
                'eu_total_reach',
                'impressions',
                'languages',
                'page_id',
                'page_name',
                'publisher_platforms',
                'spend',
                'target_ages',
                'target_gender',
                'target_locations'
            ])
        ];

        if ($endDate !== null) {
            $queryParams['ad_delivery_date_max'] = $endDate;
        }

        // Add search criteria parameters.
        if (isset($searchCriteria['search_terms'])) {
            $queryParams['search_terms'] = $searchCriteria['search_terms'];
            if (isset($searchCriteria['search_type'])) {
                $queryParams['search_type'] = $searchCriteria['search_type'];
            }
        } elseif (isset($searchCriteria['search_page_ids'])) {
            $queryParams['search_page_ids'] = json_encode($searchCriteria['search_page_ids']);
        }

        // Base URL for API version 22.0.
        $baseUrl = 'https://graph.facebook.com/v22.0/ads_archive';

        do {
            $url = $nextUrl ?: $baseUrl;

            try {
                $response = $this->httpClient->request('GET', $url, [
                    'query' => $nextUrl ? [] : $queryParams
                ]);
                $result = $response->toArray();
            } catch (Exception $e) {
                $this->logger->error('Error fetching Facebook Ads Archive data: ' . $e->getMessage());
                break;
            }

            if (!isset($result['data'])) {
                $this->logger->warning('Unexpected API response format.');
                break;
            }

            $dataPage = $result['data'];

            // Filter out ads missing the required disclaimer if the filter is enabled.
            if (!empty($filters['filter_required_disclaimer'])) {
                $dataPage = array_filter($dataPage, function ($ad) {
                    if (!isset($ad['ad_creative_bodies'])) {
                        return true;
                    }
                    $bodies = $ad['ad_creative_bodies'];
                    if (!is_array($bodies)) {
                        $bodies = [$bodies];
                    }
                    foreach ($bodies as $body) {
                        if (str_contains(strtolower($body), 'this ad ran without a required disclaimer.')) {
                            return false;
                        }
                    }
                    return true;
                });
                $dataPage = array_values($dataPage);
            }

            foreach ($dataPage as &$ad) {
                if (empty($ad['system_type'] ?? null)) {
                    $ad['system_type'] = $adType;
                }
            }

            // Merge current batch.
            $dataCollected = array_merge($dataCollected, $dataPage);
            $adsFetched = count($dataPage);

            $this->logger->info(sprintf('Fetched %d ads in this batch. Total collected so far: %d', $adsFetched, count($dataCollected)));

            // Check if an optional limit is configured and reached.
            if ($limit !== null && count($dataCollected) >= $limit) {
                $this->logger->info(sprintf('Optional limit of %d ads reached. Stopping further API calls.', $limit));
                break;
            }

            // Determine if there is a next page.
            $nextUrl = $result['paging']['next'] ?? null;

            // Pause between requests (adjust as needed).
            usleep(1000000);
        } while ($nextUrl !== null);

        return $dataCollected;
    }
}