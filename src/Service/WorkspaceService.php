<?php
namespace App\Service;

use App\Entity\Organization;
use App\Entity\OrganizationUser;
use App\Entity\User;
use App\Repository\Social\TrackingGroupRepository;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\RequestStack;

readonly class WorkspaceService
{
    public function __construct(
        private Security     $security,
        private RequestStack $requestStack
    ) {}

    public function getCurrentOrganization(): ?Organization {
        if (!$this->security->isGranted('ROLE_USER')) {
            return null;
        }

        /** @var User $user */
        $user = $this->security->getUser();
        $request = $this->requestStack->getCurrentRequest();

        $currentOrgUserRelation = array_filter($user->getOrganizationUsers()->toArray(), function (OrganizationUser $item) use($user, $request) {
            return $request->getSession()->get('current_organization') == $item->getOrganization()->getId()->toRfc4122();
        });
        $currentOrgUserRelation = array_values($currentOrgUserRelation)[0];
        return $currentOrgUserRelation->getOrganization();
    }
}