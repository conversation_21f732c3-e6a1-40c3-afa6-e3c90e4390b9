<?php
namespace App\Service\Social;

use Swaggest\JsonDiff\Exception;
use Swaggest\JsonDiff\JsonDiff;

class ProfileHistoryService
{
    /**
     * @throws Exception
     */
    public function getDifference(array $oldData, array $newData): array {
        $diff = new JsonDiff($oldData, $newData, JsonDiff::REARRANGE_ARRAYS);
        $changes = [];

        foreach ($diff->getPatch() as $change) {
            if ($change->op === "replace" || $change->op === "add") {
                $keys = explode('/', trim($change->path, '/'));
                $temp = &$changes;

                // Traverse through the array to assign the value correctly
                foreach ($keys as $key) {
                    if (!isset($temp[$key])) {
                        $temp[$key] = [];
                    }
                    $temp = &$temp[$key];
                }
                $temp = $change->value;
            }
        }

        return $changes;
    }
}