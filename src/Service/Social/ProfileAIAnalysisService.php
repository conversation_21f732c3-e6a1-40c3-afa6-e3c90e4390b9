<?php
namespace App\Service\Social;

use App\Entity\Meta\Profile;
use DateTimeImmutable;
use finfo;
use OpenAI\Client;

class ProfileAIAnalysisService
{
    private ?string $language = 'en';

    public function __construct(
        private readonly Client $openai
    ) {}

    public function getLanguage(): ?string
    {
        return $this->language;
    }

    public function setLanguage(?string $language): static
    {
        $this->language = $language;

        return $this;
    }

    public function getProfileAnalysis(Profile $profile): string {
        $data = $profile->getData();

        $profilePicContent = null;
        $profileCoverContent = null;

        if (array_key_exists('profile_picture_url', $data)) {
            $profilePicContent = file_get_contents($data['profile_picture_url']);
            unset($data['profile_picture_url']);
        } else if (!empty($data['picture']['data']['url'] ?? null)) {
            $profileCoverContent = file_get_contents($data['picture']['data']['url']);
            unset($data['picture']);
        }

        if (!empty($data['cover']['source'] ?? null)) {
            $profileCoverContent = file_get_contents($data['cover']['source']);
            unset($data['cover']);
        }

        unset($data['id']);

        $jsonData = json_encode($data, JSON_UNESCAPED_UNICODE);
        $prompt = "Analyze and generate a report ({$this->getLanguageInformation(false)}) based on the following user profile data: {$jsonData}. Get the most [contextual] information out of it.";

        if ($profilePicContent) {
            $prompt .= " Include information about the first image, which is the profile picture (if it's a person, it's age, ethnicity, sex, what's on the background and all info you can find).";
        }

        if ($profileCoverContent) {
            $add = $profilePicContent ? 'second' : 'first';
            $prompt .= " Include information about the {$add} image, which is the cover picture (what it is, and describe it).";
        }

        if ($profilePicContent || $profileCoverContent) {
            $prompt .= "About the images: Analyze them in detail and return a list of keywords that describe every significant visual element. Use categories such as: location, clothing, emotional state, gestures, accessories, companions, symbolism, atmosphere, type of activity, time of day, visible props, and any other relevant details. For each category, include only one or a few precise keywords and separate all of them with commas in a single line. Do not write full sentences. Example output format: office, indoor, daytime, suit, tie, smiling, standing, handshake, flag, alone, male, formal atmosphere, natural lighting, no phone, wearing a watch, leadership message, professional photo, no children, no food. Include details like phone model (if visible), watch brand, clothing colors, position in the photo (center/side), and any symbolic or political elements that may be relevant.";
        }

        $queries = [
            [
                'type' => 'text',
                'text' => $prompt
            ]
        ];

        if ($profilePicContent) {
            $queries[] = [
                'type' => 'image_url',
                'image_url' => [
                    'url' => sprintf('data:%s;base64,%s', $this->getMimeTypeFromFileContent($profilePicContent), base64_encode($profilePicContent))
                ]
            ];
        }

        if ($profileCoverContent) {
            $queries[] = [
                'type' => 'image_url',
                'image_url' => [
                    'url' => sprintf('data:%s;base64,%s', $this->getMimeTypeFromFileContent($profileCoverContent), base64_encode($profileCoverContent))
                ]
            ];
        }

        $response = $this->openai->chat()->create([
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $queries
                ],
            ],
            'max_tokens' => 4096
        ]);

        $data = $response->toArray();
        return $data['choices'][0]['message']['content'] ?? '';
    }

    public function getFeedAnalysis(array $feed): string {
        $limitedFeed = array_slice($feed, 0, 15);

        foreach ($limitedFeed as &$post) {
            if (array_key_exists('comments', $post)) {
                $comments = $post['comments']['data'] ?? $post['comments'];
                $post['comments'] = array_slice($comments, 0, 15);
            }
        }

        $jsonData = json_encode($limitedFeed, JSON_UNESCAPED_UNICODE);
        $prompt = "Analyze ({$this->getLanguageInformation(false)}) and generate a report based on the following user recent feed data: {$jsonData}. Get the most [contextual] information out of it.";

        $response = $this->openai->chat()->create([
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $prompt
                        ]
                    ]
                ],
            ],
            'max_tokens' => 4096
        ]);

        $data = $response->toArray();
        return $data['choices'][0]['message']['content'] ?? '';
    }

    public function getHistoryAnalysis(Profile $profile, int $entriesLimit = 10): string {
        if (count($profile->getHistory()) === 0) {
            return 'No history found.';
        }

        $limitedHistory = [];
        $lastEntryDate = null;
        foreach ($profile->getHistory() as $history) {
            if (count($limitedHistory) > $entriesLimit) {
                break;
            }

            $entryDate = $history->getCreatedAt()->format('Y-m-d');
            if ($entryDate === $lastEntryDate) {
                continue;
            }

            $entry = $history->getData();
            unset($entry['id']);

            $limitedHistory[] = $entry;
            $lastEntryDate = $entryDate;
        }

        $jsonData = json_encode($limitedHistory, JSON_UNESCAPED_UNICODE);
        $prompt = "Analyze and generate a report based on the following user profile history data: {$jsonData}. Get the most [contextual] information out of it. Extract information about the trends (follower up or downs, followings, medias etc).";

        $response = $this->openai->chat()->create([
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $prompt . $this->getLanguageInformation()
                        ]
                    ]
                ],
            ],
            'max_tokens' => 4096
        ]);

        $data = $response->toArray();
        return $data['choices'][0]['message']['content'] ?? '';
    }

    public function getComplementedAnalysis(string $profile, ?string $feed = null, ?string $history = null): string {
        $prompt = "Analyze and generate a very professional report containing all necessary, important and additional information, with as much detail as you can, based on your following reports for this same user: ";
        $prompt .= PHP_EOL . $profile;

        if ($feed) {
            $prompt .= PHP_EOL . $feed;
        }

        if ($history) {
            $prompt .= PHP_EOL . $history;
        }

        $response = $this->openai->chat()->create([
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $prompt . $this->getReportInformation() . $this->getLanguageInformation()
                        ]
                    ]
                ],
            ],
            'max_tokens' => 4096
        ]);

        $data = $response->toArray();
        return $data['choices'][0]['message']['content'] ?? '';
    }

    public function getAnalysis(Profile $profile, array $feed = []): string {
        $_aBase = $this->getProfileAnalysis($profile);
        $_aFeed = $this->getFeedAnalysis($feed);
        $_aHistory = $this->getHistoryAnalysis($profile);

        return $this->getComplementedAnalysis($_aBase, $_aFeed, $_aHistory);
    }

    public function getMimeTypeFromFileContent(string $content): string
    {
        return (new finfo(FILEINFO_MIME_TYPE))->buffer($content);
    }

    private function getReportInformation(bool $initialLineBreak = true): string {
        return ($initialLineBreak ? PHP_EOL : ' ') . 'Analysis and reports made at ' . (new DateTimeImmutable())->format('Y-m-d H:i') . ' by Terra Magnora.';
    }

    private function getLanguageInformation(bool $appendSpace = true): string {
        return ($appendSpace ? ' ' : '') . 'Write it in the following language: ' . $this->getLanguage() . '. Do not output any formatting (like "#", "*", etc).';
    }
}