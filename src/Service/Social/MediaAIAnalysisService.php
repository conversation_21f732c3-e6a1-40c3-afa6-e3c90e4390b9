<?php
namespace App\Service\Social;

use App\Entity\Meta\Media;
use finfo;
use OpenAI\Client;

class MediaAIAnalysisService
{
    public const int MODE_ALL = 0;
    public const int MODE_TEXT_ONLY = 1;
    public const int MODE_MEDIA_ONLY = 2;

    private ?string $language = 'en';

    public function __construct(
        private readonly Client $openai
    ) {}

    public function getLanguage(): ?string
    {
        return $this->language;
    }

    public function setLanguage(?string $language): static
    {
        $this->language = $language;

        return $this;
    }

    public function getMediaAnalysis(array $data, array $profileData = [], int $mode = self::MODE_ALL): ?array {
        $result = [];

        if ($mode === self::MODE_ALL || $mode === self::MODE_TEXT_ONLY) {
            $result['text'] = $this->doTextAnalysis($data, $profileData);
        }

        if ($mode === self::MODE_ALL || $mode === self::MODE_MEDIA_ONLY) {
            $result['media'] = $this->doMediaAnalysis($data);
        }

        return $result;
    }

    private function doTextAnalysis(array $data, array $profileData = []): ?array {
        $mediaBody = $data['message'] ?? $data['caption'] ?? null;

        if (!$mediaBody) {
            return null;
        }

        $prompt = "Analyze this social media post content and try to give the most out of the context, as well as insights: ";
        $prompt .= json_encode($mediaBody, JSON_UNESCAPED_UNICODE);

        if (!empty($profileData)) {
            $prompt .= " Here's some insights into the profile itself: " . json_encode($profileData, JSON_UNESCAPED_UNICODE);
        }

        $prompt .= " IMPORTANT: All the content should be outputted in this language: {$this->getLanguage()}.";

        $response = $this->openai->chat()->create([
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $prompt
                        ],
                    ]
                ],
            ],
            'max_tokens' => 4096
        ]);

        $data = $response->toArray();
        return json_decode($data['choices'][0]['message']['content'], true) ?? null;
    }

    private function doMediaAnalysis(array $data): ?array {
        $type = $data['media_type'];
        $url  = $data['media_url'];

        if ($type === 'VIDEO') {
            $url = $data['thumbnail_url'];
        }

        $mediaContent = file_get_contents($url);

        if (!$mediaContent) {
            return null;
        }

        $mime = $this->getMimeTypeFromFileContent($mediaContent);

        $prompt = "Analyze this image in detail and return a list of keywords that describe every significant visual element. Use categories such as: location, clothing, emotional state, gestures, accessories, companions, symbolism, atmosphere, type of activity, time of day, visible props, and any other relevant details. For each category, include only one or a few precise keywords and separate all of them with commas in a single line. Do not write full sentences. Example output format: office, indoor, daytime, suit, tie, smiling, standing, handshake, flag, alone, male, formal atmosphere, natural lighting, phone, wearing a watch, leadership message, professional photo, children, food. Don't output negative tags, like 'no phone', for example. Include details like phone model (if visible), watch brand, clothing colors, position in the photo (center/side), and any symbolic or political elements that may be relevant. THIS IS VERY IMPORTANT: Include tags you detect, and details as well, for example: if a person is detected, what's the age range, perceived gender, number of people. If there are products, it's brand / model, and image sentiment as well. The final response should be a VALID JSON object, unformatted, only itself, in this structure: ";
        $prompt .= json_encode([
            'tags' => '[] (array)',
            'short_analysis' => 'string'
        ]);
        //$prompt .= " IMPORTANT: All the content (tags, short analysis) should be outputted in this language: {$this->getLanguage()}.";

        $response = $this->openai->chat()->create([
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'user',
                    'content' => [
                        [
                            'type' => 'text',
                            'text' => $prompt
                        ],
                        [
                            'type' => 'image_url',
                            'image_url' => [
                                'url' => sprintf('data:%s;base64,%s', $mime, base64_encode($mediaContent))
                            ]
                        ]
                    ]
                ],
            ],
            'max_tokens' => 4096
        ]);

        $data = $response->toArray();
        return json_decode($data['choices'][0]['message']['content'], true) ?? null;
    }

    public function getMimeTypeFromFileContent(string $content): string
    {
        return (new finfo(FILEINFO_MIME_TYPE))->buffer($content);
    }
}