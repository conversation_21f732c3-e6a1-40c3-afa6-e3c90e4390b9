<?php
namespace App\Service\Social;

use App\Entity\Data\AIAnalysis;
use App\Entity\Data\Filter;
use App\Entity\Meta\Comment;
use App\Entity\Meta\Media;
use App\Entity\Meta\Profile;
use App\Entity\Organization;
use App\Entity\Social\TrackingGroup\DataSource;
use App\Helper\Social\Meta\FacebookHelper;
use App\Helper\Social\Meta\InstagramHelper;
use App\Helper\Social\MetaHelper;
use App\Service\AWS\S3ProfileUploadService;
use App\Service\MetaService;
use Closure;
use DateInterval;
use DateTimeImmutable;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\EntityManagerInterface;
use OpenAI\Client;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Uid\Uuid;
use Throwable;

class ProfileUpdateService
{
    private ?SymfonyStyle $io = null;
    private string $minimumInterval = 'PT3H';

    private int $postsLimit = 0;
    private int $commentsLimit = 0;
    private bool $forceIgnoreComments = false;

    private ?string $filterEntityType = null;
    private ?Uuid $filterEntityId = null;

    private ?Organization $organization = null;

    private ?Closure $profileProgressCallback = null;

    private bool $analyzeProfileWithAI = false;

    private bool $analyzeMediaWithAI = false;

    public function __construct(
        private readonly EntityManagerInterface   $entityManager,
        private readonly MetaService              $meta,
        private readonly ProfileAIAnalysisService $profileAIAnalysisService,
        private readonly MediaAIAnalysisService   $mediaAIAnalysisService,
        private readonly LoggerInterface          $logger,
        private readonly S3ProfileUploadService   $s3ProfileUploadService
    ) {
        @ini_set('memory_limit', '1.5G');
        $this->meta->setDebug(true);
    }

    public function setIO(SymfonyStyle $io): static {
        $this->io = $io;

        return $this;
    }

    public function setMinimumInterval(string $minimumInterval): static
    {
        $this->minimumInterval = $minimumInterval;

        return $this;
    }

    public function setProfileProgressCallback(?Closure $profileProgressCallback): static
    {
        $this->profileProgressCallback = $profileProgressCallback;

        return $this;
    }

    public function getPostsLimit(): int
    {
        return $this->postsLimit;
    }

    public function setPostsLimit(int $postsLimit): static
    {
        $this->postsLimit = $postsLimit;

        return $this;
    }

    public function getCommentsLimit(): int
    {
        return $this->commentsLimit;
    }

    public function setCommentsLimit(int $commentsLimit): static
    {
        $this->commentsLimit = $commentsLimit;

        return $this;
    }

    public function forceIgnoreComments(): bool
    {
        return $this->forceIgnoreComments;
    }

    public function setForceIgnoreComments(bool $forceIgnoreComments): static
    {
        $this->forceIgnoreComments = $forceIgnoreComments;

        return $this;
    }

    public function getOrganization(): ?Organization
    {
        return $this->organization;
    }

    public function setOrganization(?Organization $organization): static
    {
        $this->organization = $organization;

        return $this;
    }

    public function getFilterEntityType(): ?string
    {
        return $this->filterEntityType;
    }

    public function setFilterEntityType(?string $filterEntityType): static
    {
        $this->filterEntityType = $filterEntityType;

        return $this;
    }

    public function getFilterEntityId(): ?Uuid
    {
        return $this->filterEntityId;
    }

    public function setFilterEntityId(?Uuid $filterEntityId): static
    {
        $this->filterEntityId = $filterEntityId;

        return $this;
    }

    public function analyzeProfileWithAI(): bool
    {
        return $this->analyzeProfileWithAI;
    }

    public function setAnalyzeProfileWithAI(bool $analyzeProfileWithAI): static
    {
        $this->analyzeProfileWithAI = $analyzeProfileWithAI;

        return $this;
    }

    public function analyzeMediasWithAI(): bool
    {
        return $this->analyzeMediaWithAI;
    }

    public function setAnalyzeMediaWithAI(bool $analyzeMediaWithAI): static
    {
        $this->analyzeMediaWithAI = $analyzeMediaWithAI;

        return $this;
    }

    public function getIdentifierType(string $identifier): string {
        return is_numeric(str_replace('_', '', $identifier)) ? 'meta_id' : 'username';
    }

    /**
     * @throws Exception
     */
    public function update(string $identifier, string $platform, bool $forceUpdate = false): array {
        $this->io?->title("Processing $identifier");

        $profile = null;
        $identifierType = $this->getIdentifierType($identifier);

        if ($identifierType === 'meta_id') {
            $profile = $this->getOrCreateProfileByMetaId($identifier, $platform);
        } else {
            $profile = $this->getOrCreateProfileByUsername($identifier, $platform);
        }

        if (!$forceUpdate && $this->isProfileUpdated($profile)) {
            $this->io?->info("Skipping $identifier as it's up to date.");

            $details = [
                'profile' => [
                    'meta_id' => $profile->getMetaId(),
                    'username' => $profile->getUsername()
                ]
            ];

            $this->entityManager->detach($profile);

            return [
                'success' => true,
                'updated' => false,
                'reason' => 'already_up_to_date',
                'details' => $details
            ];
        }

        $details = $this->createOrUpdateProfile($profile);

        if ($this->organization) {
            $uc = $details['used_credits'];
            $totalDeduction = $uc['profiles'] + $uc['posts'] + $uc['comments'];
            $this->organization->deductCredits($totalDeduction);
            $this->entityManager->flush();
        }

        return [
            'success' => true,
            'updated' => true,
            'details' => $details
        ];
    }

    public function getOrCreateProfileByUsername(string $username, string $platform): Profile {
        $profile = $this->entityManager->getRepository(Profile::class)->findOneBy(['username' => $username, 'platform' => $platform]);

        if (!$profile) {
            $profile = (new Profile())
                ->setUsername($username)
                ->setPlatform($platform)
            ;
        }

        return $profile;
    }

    public function getOrCreateProfileByMetaId(string $metaId, string $platform): Profile {
        $profile = $this->entityManager->getRepository(Profile::class)->findOneBy(['metaId' => $metaId, 'platform' => $platform]);

        if (!$profile) {
            $profile = (new Profile())
                ->setMetaId($metaId)
                ->setPlatform($platform)
            ;
        }

        return $profile;
    }

    public function isProfileUpdated(Profile $profile): bool {
        if ($profile->getUpdatedAt() === null) {
            return false;
        }

        try {
            $dateInterval = new DateInterval($this->minimumInterval);
        } catch (Throwable) {
            $dateInterval = new DateInterval('PT50M');
        }

        $shouldUpdateAfter = $profile->getUpdatedAt()->add($dateInterval);
        return (new DateTimeImmutable()) <= $shouldUpdateAfter;
    }

    /**
     * @throws Exception
     */
    public function createOrUpdateProfile(Profile $profile): array {
        $details = [
            'used_credits' => [
                'profiles' => 0,
                'posts' => 0,
                'comments' => 0
            ]
        ];

        $data = [
            'profile' => [],
            'feed' => [],
            'tagged' => []
        ];

        $pageLimit = $this->getPostsLimit() === 0 || $this->getCommentsLimit() > 100 ? 100 : $this->getPostsLimit();

        if ($profile->getPlatform() === DataSource::Facebook->value) {
            $applyFilters = [];

            if (!empty($this->getFilterEntityId()) && !empty($this->getFilterEntityType())) {
                $filters = $this->entityManager->getRepository(Filter::class)->findBy(['entityId' => $this->getFilterEntityId(), 'entityType' => $this->getFilterEntityType()]);

                if ($filters) {
                    foreach ($filters as $filter) {
                        $applyFilters[$filter->getField()] = $filter->getValue();
                    }
                }
            }

            $applyFilters['max_posts'] = $this->getPostsLimit();
            $applyFilters['max_comments_per_post'] = $this->getCommentsLimit();

            if ($applyFilters['max_comments_per_post'] === 0) {
                $applyFilters['max_comments_per_post'] = 1000;
            }

            if ($this->forceIgnoreComments()) {
                $applyFilters['force_ignore_comments'] = $this->forceIgnoreComments();
            }

            $response = $this->meta->getCompleteFacebookData($profile->getMetaId() ?? $profile->getUsername(), $applyFilters, $this->profileProgressCallback);

            $data = [
                'profile' => FacebookHelper::getProfileDataForEntity($response),
                'feed' => FacebookHelper::getFeedForEntity($response),
                'tagged' => FacebookHelper::getTaggedForEntity($response)
            ];
        } else if ($profile->getPlatform() === DataSource::Instagram->value) {
            $response = $this->meta->getInstagramData($profile->getUsername(), $this->getPostsLimit(), $pageLimit, $this->getCommentsLimit(), progressCallback: $this->profileProgressCallback);

            $data = [
                'profile' => InstagramHelper::getProfileDataForEntity($response),
                'feed' => InstagramHelper::getFeedForEntity($response),
                'tagged' => []
            ];
        }

        $dbProfile = $this->entityManager->getRepository(Profile::class)->findOneBy(['metaId' => $data['profile']['id'], 'platform' => $profile->getPlatform()]);
        if ($dbProfile) {
            $profile = $dbProfile;

            if ($profile->getData() != $data['profile']) {
                $profile->addHistory(
                    (new Profile\History())
                        ->setData($profile->getData())
                        ->setCreatedAt($profile->getUpdatedAt() ?? $profile->getCreatedAt())
                );
            }
        }

        $profile
            ->setMetaId($data['profile']['id'])
            ->setUsername($data['profile']['username'])
            ->setData($data['profile'])
        ;

        $details['profile'] = [
            'meta_id' => $data['profile']['id'],
            'username' => $data['profile']['username']
        ];
        $details['used_credits']['profiles']++;

        $this->entityManager->persist($profile);
        $this->entityManager->flush();

        $feedUpdateResult = $this->createOrUpdateProfileMedia($profile, $data['feed'], $data['tagged']);
        $this->s3ProfileUploadService->upload($profile->getMetaId(), $profile->getPlatform());
        $this->entityManager->detach($profile);

        $details['used_credits']['posts'] += $feedUpdateResult['used_credits']['posts'];
        $details['used_credits']['comments'] += $feedUpdateResult['used_credits']['comments'];

        return $details;
    }

    /**
     * @throws Exception
     */
    public function createOrUpdateProfileMedia(Profile $profile, array $feed, array $tagged = []): array {
        $result = [
            'used_credits' => [
                'posts' => 0,
                'comments' => 0
            ]
        ];

        $this->io?->info('Persisting feed data...');

        $connection = $this->entityManager->getConnection();

        $newMedia = [];
        $newComments = [];

        foreach (['owner' => $feed, 'tagged' => $tagged] as $origin => $feedArray) {
            foreach ($feedArray as $mediaArray) {
                $insideMedia = $mediaArray;

                if ((!array_key_exists('comment_count', $insideMedia) || !array_key_exists('comments_count', $insideMedia)) && array_key_exists('comments', $insideMedia)) {
                    $insideMedia['comments_count'] = $insideMedia['comments']['summary']['total_count'];
                }

                unset($insideMedia['id'], $insideMedia['comments']);

                $mediaUuid = Uuid::v4()->toRfc4122();
                $newMedia[] = [
                    'id' => $mediaUuid,
                    'meta_id' => $mediaArray['id'],
                    'profile_id' => $profile->getId()->toRfc4122(),
                    'data' => json_encode($insideMedia, JSON_UNESCAPED_UNICODE),
                    'origin' => $origin
                ];

                foreach (MetaHelper::getMediaComments($mediaArray['comments'] ?? []) as $comment) {
                    $newComments[] = [
                        'id' => Uuid::v4()->toRfc4122(),
                        'meta_id' => $comment['id'],
                        'media_id' => $mediaArray['id'],
                        'user_meta_id' => $comment['from']['id'],
                        'data' => json_encode($comment, JSON_UNESCAPED_UNICODE),
                    ];
                }
            }
        }

        // Process Meta Media Upsert
        if (!empty($newMedia)) {
            // --- Upsert Media and capture the mapping via RETURNING ---
            $sqlMedia = "
                INSERT INTO meta_media (id, meta_id, profile_id, data, origin, created_at, updated_at)
                VALUES (:id, :meta_id, :profile_id, :data, :origin, NOW(), NOW())
                ON CONFLICT (meta_id) 
                DO UPDATE SET 
                    data = EXCLUDED.data,
                    updated_at = NOW()
                ;
            ";
            $stmtMedia = $connection->prepare($sqlMedia);
            $connection->beginTransaction();
            foreach ($newMedia as $mediaParams) {
                // Execute the upsert and fetch the resulting mapping.
                $stmtMedia->executeStatement($mediaParams);
            }
            $connection->commit();
        }

        $externalMediaIds = array_unique(array_column($newMedia, 'meta_id'));
        $sqlMapping = "SELECT id, meta_id, data FROM meta_media WHERE meta_id IN (:metaIds)";
        $mediaInDb = $connection->executeQuery(
            $sqlMapping,
            ['metaIds' => $externalMediaIds],
            ['metaIds' => \Doctrine\DBAL\Connection::PARAM_STR_ARRAY]
        )->fetchAllAssociative();

        $completeMediaMapping = [];
        foreach ($mediaInDb as $row) {
            $completeMediaMapping[$row['meta_id']] = $row['id'];
        }

        // Process Meta Comment Upsert
        if (!empty($newComments)) {
            // --- Update newComments with the correct media id ---
            foreach ($newComments as &$commentParams) {
                $externalMediaId = $commentParams['media_id'];
                if (isset($completeMediaMapping[$externalMediaId])) {
                    $commentParams['media_id'] = $completeMediaMapping[$externalMediaId];
                } else {
                    // If no mapping is found, decide whether to skip this comment or throw an error.
                    throw new \RuntimeException("Media mapping not found for external media ID: $externalMediaId");
                }
            }
            unset($commentParams);

            // --- Upsert Comments using the updated media_id ---
            $sqlComment = "
                INSERT INTO meta_comment (id, meta_id, media_id, user_meta_id, data, created_at, updated_at)
                VALUES (:id, :meta_id, :media_id, :user_meta_id, :data, NOW(), NOW())
                ON CONFLICT (meta_id) 
                DO UPDATE SET 
                    data = EXCLUDED.data,
                    updated_at = NOW();
            ";
            $stmtComment = $connection->prepare($sqlComment);
            $connection->beginTransaction();
            foreach ($newComments as $commentParams) {
                $stmtComment->executeStatement($commentParams);
            }
            $connection->commit();
        }

        $result['used_credits']['posts'] += count($newMedia);
        $result['used_credits']['comments'] += count($newComments);

        if ($this->analyzeProfileWithAI) {
            $this->io?->info('Analyzing profile with AI...');

            try {
                $aiAnalysis = $this->entityManager->getRepository(AIAnalysis::class)->findOneBy(['entityId' => $profile->getId(), 'entityType' => get_class($profile)]);
                if (!$aiAnalysis) {
                    $aiAnalysis = new AIAnalysis();
                }

                $this->profileAIAnalysisService->setLanguage('sq');

                $data = [
                    'sq' => [
                        'profile' => $this->profileAIAnalysisService->getProfileAnalysis($profile),
                        'history' => $this->profileAIAnalysisService->getHistoryAnalysis($profile, 15),
                        'feed' => $this->profileAIAnalysisService->getFeedAnalysis($feed)
                    ]
                ];

                $aiAnalysis
                    ->setEntityId($profile->getId())
                    ->setEntityType(get_class($profile))
                    ->setResult('*** USE DATA INSTEAD ***')
                    ->setData($data)
                ;

                $this->entityManager->persist($aiAnalysis);
            } catch (Throwable $e) {
                $this->io?->error($e->getMessage());
            }
        }

        if ($this->analyzeMediaWithAI) {
            $this->io?->info('Analyzing media with AI...');
            $entityType = get_class(new Media());

            foreach ($mediaInDb as $media) {
                $aiAnalysis = $this->entityManager->getRepository(AIAnalysis::class)->findOneBy(['entityId' => $media['id'], 'entityType' => $entityType]);
                if (!$aiAnalysis) {
                    $aiAnalysis = new AIAnalysis();
                }

                try {
                    //$_mData = json_decode($media['data'], true);
                    //$_mData = $profile->getPlatform() === DataSource::Facebook->value ? $_mData[''] : $media['data'];

                    $this->mediaAIAnalysisService->setLanguage('sq');
                    $result = $this->mediaAIAnalysisService->getMediaAnalysis(json_decode($media['data'], true), $profile->getData(), MediaAIAnalysisService::MODE_TEXT_ONLY);

                    if ($result !== null) {
                        $data = [
                            'sq' => $result
                        ];

                        $aiAnalysis
                            ->setEntityId(Uuid::fromString($media['id']))
                            ->setEntityType($entityType)
                            ->setResult('*** USE DATA INSTEAD ***')
                            ->setData($data)
                        ;

                        $this->entityManager->persist($aiAnalysis);
                    }
                } catch (Throwable $e) {
                    $this->io?->error($e->getMessage());
                }
            }
        }

        unset($mediaInDb);
        return $result;
    }
}