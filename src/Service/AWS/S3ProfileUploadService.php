<?php
namespace App\Service\AWS;

use App\Entity\Social\TrackingGroup\DataSource;
use App\Helper\Social\Meta\ReactionsHelper;
use Aws\S3\S3Client;
use DateTimeImmutable;
use DateTimeZone;
use Doctrine\ORM\EntityManagerInterface;
use function Symfony\Component\String\u;

readonly class S3ProfileUploadService
{
    public function __construct(
        private S3Client               $s3,
        private string                 $bucket,
        private EntityManagerInterface $entityManager
    ) {}

    public function upload(string $identifier, string $platform): void {
        $profile = $this->getProfile($identifier, $platform);

        $key = 'social/profiles/' . str_replace(':', '/', $platform) . '/' . $profile['id'] . '.json';
        $this->s3->putObject([
            'Bucket' => $this->bucket,
            'Key'    => $key,
            'Body'   => json_encode($profile, JSON_UNESCAPED_UNICODE)
        ]);
    }

    private function getProfile(string $identifier, string $platform): ?array
    {
        $connection = $this->entityManager->getConnection();

        // Your SQL query with correlated subqueries.
        $sql = <<<'SQL'
            SELECT 
                p.id,
                p.meta_id,
                p.username,
                p.platform,
                p.data,
                COALESCE(
                    (
                        SELECT jsonb_agg(jsonb_build_object(
                            'id', h.id,
                            'data', h.data,
                            'created_at', h.created_at
                        ))
                        FROM meta_profile_history AS h
                        WHERE h.profile_id = p.id
                    ),
                    '[]'::jsonb
                ) AS history,
                COALESCE(
                    (
                        SELECT 
                            '[' || string_agg(
                                json_build_object(
                                    'id', m.id,
                                    'meta_id', m.meta_id,
                                    'data', m.data,
                                    'origin', m.origin,
                                    'comments', (
                                        SELECT COALESCE(
                                            json_agg(
                                                json_build_object(
                                                    'id', c.id,
                                                    'meta_id', c.meta_id,
                                                    'user_meta_id', c.user_meta_id,
                                                    'data', c.data,
                                                    'created_at', c.created_at,
                                                    'updated_at', c.updated_at
                                                )
                                            ),
                                            '[]'
                                        )
                                        FROM meta_comment AS c
                                        WHERE c.media_id = m.id
                                    ),
                                    'created_at', m.created_at,
                                    'updated_at', m.updated_at
                                )::TEXT, ','
                            ) || ']'
                        FROM meta_media AS m
                        WHERE m.profile_id = p.id
                    ),
                    '[]'
                ) AS media,
                p.created_at,
                p.updated_at
            FROM meta_profile AS p WHERE p.platform = :platform AND (p.meta_id IN (:identifier) OR p.username IN(:identifier)) LIMIT 1
        SQL;

        $tzAlbania = new DateTimeZone('Europe/Tirane');

        $row = $connection->executeQuery($sql, [
            'platform' => $platform,
            'identifier' => $identifier
        ])->fetchAssociative();

        // Stream each row without loading all into memory
        if ($row) {
            // Decode core JSON fields
            $row['data']    = json_decode($row['data'],    true);
            $row['history'] = json_decode($row['history'] ?? '[]', true);

            // Decode nested a media array and its comments
            $row['media'] = array_map(function (array $media) {
                $media['comments'] = !is_array($media['comments']) ? json_decode($media['comments'] ?? '[]', true) : $media['comments'];
                return $media;
            }, json_decode($row['media'] ?? '[]', true));

            foreach ($row['media'] as &$media) {
                $this->transformObjectDateTimeProperties($media['data'], $tzAlbania, 'albania_', true);

                if ($row['platform'] === DataSource::Facebook->value) {
                    $media['data']['sentiment'] = ReactionsHelper::getSentimentFromReactions($media['data']);
                }

                if (array_key_exists('comments', $media['data']) && count($media['data']['comments']) > 0) {
                    foreach ($media['data']['comments'] as &$comment) {
                        $this->transformObjectDateTimeProperties($comment['data'], $tzAlbania, 'albania_', true);

                        if ($row['platform'] === DataSource::Facebook->value) {
                            $comment['data']['sentiment'] = ReactionsHelper::getSentimentFromReactions($comment['data']);
                        }
                    }
                }
            }
        }

        return $row;
    }

    private function transformObjectDateTimeProperties(array &$object, DateTimeZone $targetTimeZone, string $prefix, bool $includeHourOnlyVar): void {
        $prefix = u($prefix)->ensureEnd('_')->toString();

        if (array_key_exists('created_time', $object)) {
            $created = new DateTimeImmutable($object['created_time']);
            $createdTarget = $created->setTimezone($targetTimeZone);
            $object[$prefix . 'created_time'] = $createdTarget->format('Y-m-d H:i:s');

            if ($includeHourOnlyVar) {
                $object[$prefix . 'created_hour'] = $createdTarget->format('H');
            }
        }

        if (array_key_exists('updated_time', $object)) {
            $updated = new DateTimeImmutable($object['updated_time']);
            $updatedTarget = $updated->setTimezone($targetTimeZone);
            $object[$prefix . 'updated_time'] = $updatedTarget->format('Y-m-d H:i:s');

            if ($includeHourOnlyVar) {
                $object[$prefix . 'updated_hour'] = $updatedTarget->format('H');
            }
        }

        if (array_key_exists('timestamp', $object)) {
            $timestamp = new DateTimeImmutable($object['timestamp']);
            $timestampTarget = $timestamp->setTimezone($targetTimeZone);
            $object[$prefix . 'timestamp'] = $timestampTarget->format('Y-m-d H:i:s');

            if ($includeHourOnlyVar) {
                $object[$prefix . 'timestamp_hour'] = $timestampTarget->format('H');
            }
        }
    }
}