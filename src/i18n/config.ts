export const defaultLocale = 'en';
export const locales = ['en', 'sq', 'tr'];

export type Locale = (typeof locales)[number];

export const getLocaleFromPathname = (pathname: string): Locale => {
  const segments = pathname.split('/');
  const maybeLocale = segments[1];
  
  if (locales.includes(maybeLocale as Locale)) {
    return maybeLocale as Locale;
  }
  
  return defaultLocale;
};

export const removeLocaleFromPathname = (pathname: string): string => {
  const segments = pathname.split('/');
  const maybeLocale = segments[1];
  
  if (locales.includes(maybeLocale as Locale)) {
    return '/' + segments.slice(2).join('/');
  }
  
  return pathname;
};

export const addLocaleToPathname = (pathname: string, locale: Locale): string => {
  const cleanPathname = removeLocaleFromPathname(pathname);
  
  if (locale === defaultLocale) {
    return cleanPathname;
  }
  
  return `/${locale}${cleanPathname}`;
};
