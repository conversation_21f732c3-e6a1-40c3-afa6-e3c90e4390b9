import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Poppins } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

const poppins = Poppins({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  variable: "--font-poppins",
  display: "swap",
});

export const metadata: Metadata = {
  title: "PostCHAT - Smart Sales Platform",
  description: "A modern platform for borderless, stock-free, and staff-free sales. Built on the concept of dropshipping and the empowerment of social networks.",
  keywords: ["PostCHAT", "dropshipping", "ecommerce", "online business", "social selling", "Terra platform"],
  authors: [{ name: "PostCHAT Team" }],
  openGraph: {
    title: "PostCHAT - Smart Sales Platform",
    description: "A modern platform for borderless, stock-free, and staff-free sales.",
    url: "https://post-chat.com",
    siteName: "PostCHAT",
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "PostCHAT - Smart Sales Platform",
    description: "A modern platform for borderless, stock-free, and staff-free sales.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${poppins.variable} font-sans antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
