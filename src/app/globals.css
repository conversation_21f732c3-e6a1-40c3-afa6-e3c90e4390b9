@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #ff0000;
  --primary-dark: #cc0000;
  --primary-light: #ff3333;
  --background: #ffffff;
  --foreground: #171717;
}

@layer base {
  html {
    font-family: var(--font-inter), system-ui, sans-serif;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-poppins), system-ui, sans-serif;
  }
}

@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .btn-primary {
    @apply bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-full font-medium transition-colors duration-300 inline-flex items-center;
  }

  .btn-secondary {
    @apply bg-transparent hover:bg-red-700 border-2 border-white text-white px-6 py-3 rounded-full font-medium transition-colors duration-300 inline-flex items-center;
  }

  .btn-outline {
    @apply bg-transparent hover:bg-red-50 border-2 border-red-600 text-red-600 hover:text-red-700 px-6 py-3 rounded-full font-medium transition-colors duration-300 inline-flex items-center;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
}
