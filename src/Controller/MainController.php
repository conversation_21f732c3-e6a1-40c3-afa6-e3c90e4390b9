<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\ResponseHeaderBag;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class MainController extends AbstractController
{
    #[Route('/', name: 'app_main_index')]
    public function index(): Response
    {
        return $this->render('main/modern.html.twig');
    }

    #[Route('/classic', name: 'app_main_classic')]
    public function classicIndex(): Response
    {
        return $this->render('main/index.html.twig');
    }

    #[Route('/app/download', name: 'app_download')]
    public function downloadApp(): Response
    {
        $apkPath = $this->getParameter('kernel.project_dir') . '/storage/app/postchat-v0.1.0-alpha.apk';

        if (!file_exists($apkPath)) {
            // Create storage directory if it doesn't exist
            $storageDir = $this->getParameter('kernel.project_dir') . '/storage/app';
            if (!is_dir($storageDir)) {
                mkdir($storageDir, 0777, true);
            }

            // Return a message that the APK is not yet available
            return $this->render('main/app_download.html.twig', [
                'message' => 'The APK file is not yet available. Please check back later.'
            ]);
        }

        $response = new BinaryFileResponse($apkPath);
        $response->setContentDisposition(
            ResponseHeaderBag::DISPOSITION_ATTACHMENT,
            'postchat-v0.1.0-alpha.apk'
        );

        return $response;
    }
}
