<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class ContactController extends AbstractController
{
    private array $contactInfo;

    public function __construct()
    {
        // Get contact information from environment variables
        $this->contactInfo = [
            'firstName' => $_ENV['CONTACT_FIRST_NAME'] ?? null,
            'lastName' => $_ENV['CONTACT_LAST_NAME'] ?? null,
            'businessName' => $_ENV['CONTACT_BUSINESS_NAME'] ?? 'PostCHAT',
            'phone' => $_ENV['CONTACT_PHONE'] ?? null,
            'email' => $_ENV['CONTACT_EMAIL'] ?? null,
            'website' => $_ENV['CONTACT_WEBSITE'] ?? 'https://post-chat.com',
            'channels' => json_decode($_ENV['CONTACT_CHANNELS'] ?? '["viber","whatsapp"]', true),
        ];
    }

    #[Route('/contact', name: 'app_contact')]
    public function index(): Response
    {
        return $this->render('contact/index.html.twig', [
            'guest' => $this->contactInfo,
        ]);
    }

    #[Route('/contact/vcard', name: 'app_contact_vcard')]
    public function vcard(): Response
    {
        // Create a vCard file
        $vcard = "BEGIN:VCARD\r\n";
        $vcard .= "VERSION:3.0\r\n";
        $vcard .= "N:" . $this->contactInfo['lastName'] . ";" . $this->contactInfo['firstName'] . ";;;\r\n";
        $vcard .= "FN:" . $this->contactInfo['firstName'] . " " . $this->contactInfo['lastName'] . "\r\n";
        $vcard .= "ORG:" . $this->contactInfo['businessName'] . "\r\n";
        $vcard .= "TEL;TYPE=CELL:" . $this->contactInfo['phone'] . "\r\n";
        $vcard .= "EMAIL:" . $this->contactInfo['email'] . "\r\n";
        $vcard .= "URL:" . $this->contactInfo['website'] . "\r\n";
        $vcard .= "END:VCARD\r\n";

        $response = new Response($vcard);
        $response->headers->set('Content-Type', 'text/vcard');
        $response->headers->set('Content-Disposition', 'attachment; filename="' . $this->contactInfo['firstName'] . '_' . $this->contactInfo['lastName'] . '.vcf"');

        return $response;
    }

    #[Route('/contact/card', name: 'app_contact_card')]
    public function card(): Response
    {
        // Generate a random row letter for the seat
        $rowLetter = chr(rand(65, 90)); // A-Z

        return $this->render('contact/card.html.twig', [
            'guest' => array_merge($this->contactInfo, [
                'seat' => [
                    'section' => 'A',
                    'number' => rand(1, 100),
                ],
            ]),
            'row_letter' => $rowLetter,
        ]);
    }
}
