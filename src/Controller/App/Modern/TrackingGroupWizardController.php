<?php
namespace App\Controller\App\Modern;

use App\Entity\Social\TrackingGroup;
use App\Entity\Social\TrackingGroup\DataSource;
use App\Entity\Social\TrackingGroup\ListType;
use App\Entity\Social\TrackingStatus;
use App\Form\Social\TrackingGroupType;
use App\Service\WorkspaceService;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\Attribute\Route;
use function Symfony\Component\String\u;

#[Route('/app/modern/tracking-group')]
final class TrackingGroupWizardController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly WorkspaceService $workspaceService
    ) {}

    #[Route('/create', name: 'app_modern_tracking_group_create')]
    public function create(Request $request): Response
    {
        // Redirect to the main tracking group creation page
        return $this->redirectToRoute('app_social_profile_tracking_create');
    }

    #[Route('/create-ajax', name: 'app_modern_tracking_group_create_ajax', methods: ['POST'])]
    public function createAjax(Request $request): JsonResponse
    {
        if (!$request->isXmlHttpRequest()) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid request'], 400);
        }

        try {
            $data = json_decode($request->getContent(), true);
            if (!$data) {
                throw new \Exception('Invalid data format');
            }

            // Create tracking group from the submitted data
            $trackingGroup = $this->createTrackingGroupFromData($data);

            return new JsonResponse([
                'success' => true,
                'message' => 'Tracking Group created successfully!',
                'redirect' => $this->generateUrl('app_modern_tracking_groups'),
                'id' => $trackingGroup->getId()->__toString()
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error creating tracking group: ' . $e->getMessage()
            ], 500);
        }
    }

    #[Route('/wizard/{step}', name: 'app_modern_tracking_group_wizard_step')]
    public function wizardStep(int $step, Request $request): Response
    {
        // Check if the wizard session exists
        if (!$request->getSession()->has('tracking_group_wizard')) {
            return $this->redirectToRoute('app_modern_tracking_group_create');
        }

        $wizardData = $request->getSession()->get('tracking_group_wizard');

        // Validate step number
        if ($step < 1 || $step > 4) {
            return $this->redirectToRoute('app_modern_tracking_group_wizard_step', ['step' => 1]);
        }

        // Update current step in session
        $wizardData['current_step'] = $step;
        $request->getSession()->set('tracking_group_wizard', $wizardData);

        // Handle form submission for each step
        if ($request->isMethod('POST')) {
            $formData = $request->request->all();

            switch ($step) {
                case 1: // Basic Information
                    $wizardData['data']['name'] = $formData['name'] ?? '';
                    $wizardData['data']['dataSource'] = $formData['dataSource'] ?? DataSource::Instagram->value;
                    $wizardData['data']['listType'] = $formData['listType'] ?? ListType::Profiles->value;
                    $request->getSession()->set('tracking_group_wizard', $wizardData);
                    return $this->redirectToRoute('app_modern_tracking_group_wizard_step', ['step' => 2]);

                case 2: // Profiles List
                    $rawList = $formData['list'] ?? '';
                    $list = [];

                    if (u($rawList)->containsAny(',')) {
                        $list = array_map('trim', explode(',', $rawList));
                    } else {
                        $list = array_map('trim', explode("\r\n", $rawList));
                    }

                    // Filter out empty values
                    $list = array_filter($list, fn($item) => !empty($item));

                    $wizardData['data']['list'] = $list;
                    $request->getSession()->set('tracking_group_wizard', $wizardData);
                    return $this->redirectToRoute('app_modern_tracking_group_wizard_step', ['step' => 3]);

                case 3: // Tracking Options
                    $wizardData['data']['category'] = $formData['category'] ?? 'general';
                    $wizardData['data']['subCategory'] = $formData['subCategory'] ?? '';
                    $wizardData['data']['maximumPostsPerProfile'] = (int)($formData['maximumPostsPerProfile'] ?? 10);
                    $wizardData['data']['maximumCommentsPerPost'] = (int)($formData['maximumCommentsPerPost'] ?? 0);
                    $wizardData['data']['runProfileAIAnalysis'] = isset($formData['runProfileAIAnalysis']);
                    $wizardData['data']['keepDataUpdated'] = isset($formData['keepDataUpdated']);
                    $request->getSession()->set('tracking_group_wizard', $wizardData);
                    return $this->redirectToRoute('app_modern_tracking_group_wizard_step', ['step' => 4]);

                case 4: // Review & Create
                    if (isset($formData['create'])) {
                        return $this->createTrackingGroup($wizardData['data'], $request);
                    }
                    break;
            }
        }

        // Render the appropriate template for the current step
        return $this->render("app/modern_dashboard/tracking_group/wizard/step{$step}.html.twig", [
            'wizard_data' => $wizardData,
            'total_steps' => 4
        ]);
    }

    #[Route('/wizard/cancel', name: 'app_modern_tracking_group_wizard_cancel')]
    public function cancelWizard(Request $request): Response
    {
        // Clear the wizard session data
        $request->getSession()->remove('tracking_group_wizard');

        // Redirect to the tracking groups list
        return $this->redirectToRoute('app_modern_tracking_groups');
    }

    private function createTrackingGroupFromData(array $data): TrackingGroup
    {
        // Create a new tracking group entity
        $trackingGroup = new TrackingGroup();
        $trackingGroup->setName($data['name'] ?: (new DateTimeImmutable())->format('Y-m-d-H-i-s'));
        $trackingGroup->setDataSource($data['dataSource']);
        $trackingGroup->setListType($data['listType']);
        $trackingGroup->setCategory($data['category']);
        $trackingGroup->setSubCategory($data['subCategory'] ?? '');
        $trackingGroup->setList($data['list']);
        $trackingGroup->setMaximumPostsPerProfile($data['maximumPostsPerProfile']);
        $trackingGroup->setMaximumCommentsPerPost($data['maximumCommentsPerPost']);
        $trackingGroup->setRunProfileAIAnalysis($data['runProfileAIAnalysis']);
        $trackingGroup->setKeepDataUpdated($data['keepDataUpdated']);
        $trackingGroup->setCustomFields($data['customFields'] ?? []);

        // Set organization and user
        $trackingGroup->setOwnerOrganization($this->workspaceService->getCurrentOrganization());
        $trackingGroup->setCreatedBy($this->getUser());

        // Create and set tracking status
        $trackingStatus = new TrackingStatus();
        $trackingStatus->setTrackingGroup($trackingGroup);
        $trackingStatus->setStatus(TrackingGroup\Status::Queued);
        $trackingStatus->setTotalProfiles(count($data['list']));
        $trackingStatus->setProgress(0);

        // Save to database
        $this->entityManager->persist($trackingGroup);
        $this->entityManager->persist($trackingStatus);
        $this->entityManager->flush();

        return $trackingGroup;
    }
}
