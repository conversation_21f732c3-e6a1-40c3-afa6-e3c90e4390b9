<?php
namespace App\Controller\App;

use App\Entity\User;
use App\Repository\Social\TrackingGroupRepository;
use App\Repository\Social\TrackingHistoryRepository;
use App\Service\WorkspaceService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/app/modern')]
final class ModernDashboardController extends AbstractController
{
    public function __construct(
        private readonly TrackingGroupRepository $trackingGroupRepository,
        private readonly TrackingHistoryRepository $trackingHistoryRepository,
        private readonly WorkspaceService $workspaceService
    ) {}

    #[Route('/', name: 'app_modern_dashboard')]
    public function index(Request $request): Response
    {
        // Redirect to the main dashboard
        return $this->redirectToRoute('app_app_dashboard');
    }

    #[Route('/tracking-groups', name: 'app_modern_tracking_groups')]
    public function trackingGroups(Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organization = $this->workspaceService->getCurrentOrganization();

        // Get tracking groups for the current organization
        $trackingGroups = $this->trackingGroupRepository->findBy(['ownerOrganization' => $organization]);

        return $this->render('app/modern_dashboard/tracking_groups.html.twig', [
            'trackingGroups' => $trackingGroups,
        ]);
    }

    #[Route('/profiles', name: 'app_modern_profiles')]
    public function profiles(Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organization = $this->workspaceService->getCurrentOrganization();

        // Get profiles from the repository
        $entityManager = $this->getDoctrine()->getManager();
        $profileRepository = $entityManager->getRepository(\App\Entity\Meta\Profile::class);

        // Get profiles for the current organization
        $profiles = $profileRepository->findBy(
            ['organization' => $organization],
            ['createdAt' => 'DESC'],
            100 // Limit to 100 profiles for performance
        );

        // Get platform filter from request
        $platformFilter = $request->query->get('platform', 'all');

        return $this->render('app/modern_dashboard/profiles.html.twig', [
            'profiles' => $profiles,
            'platformFilter' => $platformFilter
        ]);
    }
}
