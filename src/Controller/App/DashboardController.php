<?php
namespace App\Controller\App;

use App\Entity\Data\Filter;
use App\Entity\Social\TrackingGroup;
use App\Entity\Social\TrackingGroup\DataSource;
use App\Entity\Social\TrackingGroup\ListType;
use App\Entity\Social\TrackingStatus;
use App\Entity\User;
use App\Form\Social\TrackingGroupType;
use App\Repository\Social\TrackingGroupRepository;
use App\Service\WorkspaceService;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use function Symfony\Component\String\u;

#[Route('/app')]
final class DashboardController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ) {}

    #[Route('/albanian-elections', name: 'app_app_albanian_elections')]
    public function albanianElections(): Response
    {
        return $this->render('app/dashboard/albanian_elections.html.twig');
    }

    #[Route('/', name: 'app_app_dashboard')]
    public function index(Request $request, TrackingGroupRepository $trackingGroupRepository, WorkspaceService $workspaceService): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organization = $workspaceService->getCurrentOrganization();

        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('app_app_albanian_elections');
        }

        // Get tracking groups for the current organization
        $trackingGroups = $trackingGroupRepository->findBy(['ownerOrganization' => $organization]);

        // Get recent activity
        $recentActivity = $this->entityManager->getRepository(\App\Entity\Social\TrackingHistory::class)
            ->findRecentByOrganization($organization, 5);

        return $this->render('app/dashboard/index.html.twig', [
            'user' => $user,
            'organization' => $organization,
            'trackingGroups' => $trackingGroups,
            'recentActivity' => $recentActivity
        ]);
    }

    #[Route('/soon', name: 'app_app_soon')]
    public function soon(): Response
    {
        return $this->render('app/dashboard/soon.html.twig');
    }

    #[Route('/disabled', name: 'app_dashboard_disabled')]
    public function disabled(): Response {
        /** @var User $user */
        $user = $this->getUser();

        if ($user->isEnabled()) {
            return $this->redirectToRoute('app_app_dashboard');
        }

        return $this->render('app/dashboard/disabled.html.twig');
    }
}
