<?php
namespace App\Controller\App;

use App\Entity\Data\Filter;
use App\Entity\Social\TrackingGroup;
use App\Entity\Social\TrackingGroup\DataSource;
use App\Entity\Social\TrackingGroup\ListType;
use App\Entity\Social\TrackingStatus;
use App\Entity\User;
use App\Form\Social\TrackingGroupType;
use App\Repository\Social\TrackingGroupRepository;
use App\Service\WorkspaceService;
use DateTimeImmutable;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use function Symfony\Component\String\u;

#[Route('/app')]
final class DashboardController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ) {}

    #[Route('/albanian-elections', name: 'app_app_albanian_elections')]
    public function albanianElections(): Response
    {
        return $this->render('app/dashboard/albanian_elections.html.twig');
    }

    #[Route('/', name: 'app_app_dashboard')]
    public function index(Request $request, TrackingGroupRepository $trackingGroupRepository, WorkspaceService $workspaceService): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organization = $workspaceService->getCurrentOrganization();

        if (!$this->isGranted('ROLE_ADMIN')) {
            return $this->redirectToRoute('app_app_albanian_elections');
        }

        $socialTrackingGroup = new TrackingGroup();
        $socialTrackingGroupForm = $this->createForm(TrackingGroupType::class, $socialTrackingGroup);
        $socialTrackingGroupForm->handleRequest($request);

        if ($socialTrackingGroupForm->isSubmitted() && $socialTrackingGroupForm->isValid()) {
            if (empty($socialTrackingGroup->getName())) {
                $socialTrackingGroup->setName((new DateTimeImmutable())->format('Y-m-d-H-i-s'));
            }

            if ($socialTrackingGroup->getDataSource() === DataSource::Instagram->name) {
                $socialTrackingGroup->setListType(ListType::Profiles->name);
            }

            $rawList =  u($socialTrackingGroupForm->get('list')->getData())->trim()->toString();
            $list = [];

            if (u($rawList)->containsAny(',')) {
                $list = explode(',', $rawList);
            } else {
                $list = explode("\r\n", $rawList);
            }

            $socialTrackingGroup
                ->updateList($list)
                ->setCreatedBy($user)
                ->setOwnerOrganization($organization)
                ->setStatus(
                    (new TrackingStatus())
                        ->setStatus(TrackingGroup\Status::Queued)
                        ->setTotalProfiles(count($list))
                )
            ;

            $this->entityManager->persist($socialTrackingGroup);
            $this->entityManager->flush();

            if ($socialTrackingGroup->getDataSource() === DataSource::Facebook->value) {
                $entityType = get_class($socialTrackingGroup);
                $entityId = $socialTrackingGroup->getId();
                $_formData = $request->request->all('tracking_group');

                if (!empty($_formData['filterStartsAt'] ?? null)) {
                    $startsAt = (new Filter())
                        ->setField('starts_at')
                        ->setOperator('=')
                        ->setValue($_formData['filterStartsAt'])
                        ->setEntityId($entityId)
                        ->setEntityType($entityType)
                    ;

                    $this->entityManager->persist($startsAt);
                }

                if (!empty($_formData['filterEndsAt'] ?? null)) {
                    $endsAt = (new Filter())
                        ->setField('ends_at')
                        ->setOperator('=')
                        ->setValue($_formData['filterEndsAt'])
                        ->setEntityId($entityId)
                        ->setEntityType($entityType)
                    ;
                    $this->entityManager->persist($endsAt);
                }

                $this->entityManager->flush();
            }

            $this->addFlash('success', 'Tracking Group Created!');
            return $this->redirectToRoute('app_app_dashboard');
        }

        return $this->render('app/dashboard/index.html.twig', [
            'forms' => [
                'socialTrackingGroup' => $socialTrackingGroupForm->createView(),
            ],
            'trackingGroups' => $trackingGroupRepository->findBy(['ownerOrganization' => $organization])
        ]);
    }

    #[Route('/soon', name: 'app_app_soon')]
    public function soon(): Response
    {
        return $this->render('app/dashboard/soon.html.twig');
    }

    #[Route('/disabled', name: 'app_dashboard_disabled')]
    public function disabled(): Response {
        /** @var User $user */
        $user = $this->getUser();

        if ($user->isEnabled()) {
            return $this->redirectToRoute('app_app_dashboard');
        }

        return $this->render('app/dashboard/disabled.html.twig');
    }
}
