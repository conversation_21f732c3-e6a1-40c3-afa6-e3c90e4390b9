<?php
namespace App\Controller\App\Admin;

use App\Entity\User;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/app/admin/user', name: 'app_admin_user_')]
class UserController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ) {}

    #[Route('/list', name: 'list')]
    public function index(): Response
    {
        return $this->render('app/admin/user/list.html.twig', [
            'users' => $this->entityManager->getRepository(User::class)->findBy([], ['name' => 'ASC'])
        ]);
    }
}
