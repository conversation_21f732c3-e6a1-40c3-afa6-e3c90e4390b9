<?php

namespace App\Controller\App\Meta;

use App\Entity\Meta\Profile;
use App\Entity\Organization\CustomField;
use App\Entity\Organization\CustomFieldTemplate;
use App\Entity\Social\TrackingGroup;
use App\Form\Meta\Profile\ConfigurationType;
use App\Helper\Social\Meta\ReactionsHelper;
use App\Service\CustomFilterService;
use App\Service\Social\ProfileUpdateService;
use App\Service\WorkspaceService;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use function Symfony\Component\String\u;

#[Route('/app/meta/profile', name: 'app_meta_profile_')]
class ProfileController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly WorkspaceService $workspaceService,
        private readonly CustomFilterService $customFilterService,
        private readonly ProfileUpdateService $profileUpdateService
    ) {
        @ini_set('memory_limit', '1G');
    }

    #[Route('/list', name: 'list', methods: ['GET', 'POST'])]
    public function list(Request $request): Response
    {
        if ($request->isMethod(Request::METHOD_POST)) {
            $selectedProfiles = $request->request->all('form_selectedProfiles');
            $customFieldTemplatesKeys = $request->request->all('form_cft_key');
            $customFieldTemplatesValues = $request->request->all('form_cft_value');
            $orgCustomFields = $request->request->all('form_ocf');

            if (!empty($selectedProfiles) && ((!empty($customFieldTemplatesKeys) && !empty($customFieldTemplatesValues) || !empty($orgCustomFields)))) {
                $authorizedProfiles = $this->getProfiles();
                $currentOrganization = $this->workspaceService->getCurrentOrganization();
                $dbOrgCustomFields = empty($orgCustomFields) ? [] : $this->entityManager->getRepository(CustomField::class)->createQueryBuilder('cf')
                    ->where('cf.organization = :currentOrganization')
                    ->andWhere('cf.id IN (:ids)')
                    ->setParameter('currentOrganization', $currentOrganization)
                    ->setParameter('ids', $orgCustomFields)
                    ->getQuery()
                    ->getResult()
                ;

                foreach ($selectedProfiles as $selectedProfile) {
                    $profile = $this->entityManager->getRepository(Profile::class)->find($selectedProfile);

                    if (!$profile || !in_array($profile, $authorizedProfiles, true)) {
                        $this->addFlash('warning', "Action not authorized for profile {$profile->getUsername()}.");
                        continue;
                    }

                    $configuration = $this->entityManager->getRepository(Profile\Configuration::class)->findOneBy(['profile' => $profile, 'organization' => $currentOrganization]);
                    if (!$configuration) {
                        $configuration = (new Profile\Configuration())
                            ->setProfile($profile)
                            ->setOrganization($currentOrganization)
                        ;
                        $this->entityManager->persist($configuration);
                    }

                    if (!empty($customFieldTemplatesKeys)) {
                        $newCustomFields = array_combine($customFieldTemplatesKeys, $customFieldTemplatesValues);

                        foreach ($newCustomFields as $customFieldKey => $customFieldValue) {
                            $configuration->addOrUpdateCustomField($customFieldKey, $customFieldValue);
                        }
                    }

                    if (!empty($dbOrgCustomFields)) {
                        foreach ($dbOrgCustomFields as $ocf) {
                            $configuration->addOrganizationCustomField($ocf);
                        }
                    }
                }

                $this->entityManager->flush();
                $this->addFlash('success', 'Profiles configured.');
                return $this->redirectToRoute('app_meta_profile_list', $request->query->all());
            } else {
                $this->addFlash('warning', 'No profiles selected.');
            }
        }

        $filters['custom_fields'] = $request->query->all('filters') ?? [];

        if ($request->query->has('group-id')) {
            $filters['entities']['tg'] = $request->query->get('group-id');
        }

        return $this->render('app/meta/profile/list.html.twig', [
            'profiles' => $this->getProfiles($request->query->get('q'), $filters),
            'data' => [
                'availableCustomFields' => $this->entityManager->getRepository(CustomField::class)->findBy(['organization' => $this->workspaceService->getCurrentOrganization()]),
                'availableCustomFieldTemplates' => $this->entityManager->getRepository(CustomFieldTemplate::class)->findBy(['organization' => $this->workspaceService->getCurrentOrganization()])
            ],
            'tracking_groups' => $this->entityManager->getRepository(TrackingGroup::class)->findBy([
                'ownerOrganization' => $this->workspaceService->getCurrentOrganization()
            ], ['name' => 'ASC'])
        ]);
    }

    #[Route('/{id:profile}/view', name: 'view', methods: ['GET', 'POST'])]
    public function view(Profile $profile, Request $request): Response {
        if (!in_array($profile, $this->getProfiles(), true)) {
            return new Response(status: Response::HTTP_FORBIDDEN);
        }

        $configuration = $this->entityManager->getRepository(Profile\Configuration::class)->findOneBy(['profile' => $profile, 'organization' => $this->workspaceService->getCurrentOrganization()]);
        if (!$configuration) {
            $configuration = (new Profile\Configuration())
                ->setProfile($profile)
                ->setOrganization($this->workspaceService->getCurrentOrganization())
            ;
        }

        $customFieldsForm = $this->createForm(ConfigurationType::class, $configuration);
        $customFieldsForm->handleRequest($request);

        if ($customFieldsForm->isSubmitted() && $customFieldsForm->isValid()) {
            $this->entityManager->persist($configuration);
            $this->entityManager->flush();

            $this->addFlash('success', 'Profile configuration saved.');
            return $this->redirectToRoute('app_meta_profile_view', ['id' => $profile->getId()]);
        }

        return $this->render('app/meta/profile/view.html.twig', [
            'profile' => $profile,
            'posts' => $this->getPosts($profile),
            'configuration' => $configuration,
            'customFieldsForm' => $customFieldsForm->createView()
        ]);
    }

    #[Route('/{id:profile}/posts/load', name: 'posts_load', methods: ['GET'])]
    public function loadPosts(Profile $profile, Request $request): Response {
        if (!in_array($profile, $this->getProfiles(), true)) {
            return new Response(status: Response::HTTP_FORBIDDEN);
        }

        $posts = $this->getPosts($profile, offset:  $request->query->get('offset', 0));

        if ($profile->getPlatform() === TrackingGroup\DataSource::Facebook->value) {
            $posts = array_map(function (array $post) {
                $flatData = ReactionsHelper::flattenReactionsCount($post['data']);
                $total = 0;

                foreach (ReactionsHelper::getFacebookReactionsKeys() as $facebookReactionsKey) {
                    $total += $flatData['reactions_' . $facebookReactionsKey . '_count'] ?? 0;
                }

                $post['data']['like_count'] = $total;
                return $post;
            }, $posts);
        }

        return $this->json($posts);
    }

    #[Route('/{id:profile}/update', name: 'update')]
    public function update(Profile $profile): Response
    {
        if (!in_array($profile, $this->getProfiles(), true)) {
            return new Response(status: Response::HTTP_FORBIDDEN);
        }

        $this->profileUpdateService
            ->setAnalyzeProfileWithAI(false)
            ->setAnalyzeMediaWithAI(false)
            ->setForceIgnoreComments(true)
            ->setOrganization($this->workspaceService->getCurrentOrganization())
            ->setPostsLimit(150)
        ;

        return new StreamedResponse(function () use ($profile) {
            while (ob_get_level() > 0) {
                ob_end_clean();
            }

            ob_implicit_flush(1);  // Automatically flush the buffer after every output

            // Disable any compression in PHP (i.e., gzip)
            @ini_set('zlib.output_compression', 'Off');
            @ini_set('output_buffering', 'Off');

            function sendEvent($data, $event = null): void
            {
                if ($event) echo "event: $event\n";
                echo 'data: ' . json_encode($data) . "\n\n";
                //ob_flush();
                flush();
            }

            $this->profileUpdateService
                ->setProfileProgressCallback(function ($cur, $max, ?string $status = null) {
                    $finished = $status === 'finished';
                    $progress = 0;

                    if (!$finished && $max > 0) {
                        $progress = min(100, (int) round(($cur / $max) * 100));
                    } else if ($finished) {
                        $progress = 100;
                    }

                    sendEvent(['progress' => $progress, 'message' => $finished ? 'Finished ✅' : 'Updating... Keep this open.'], $finished ? 'done' : null);
                })
            ;

            $this->profileUpdateService->update($profile->getMetaId(), $profile->getPlatform(), true);

            sendEvent(['progress' => 100, 'message' => 'Finished ✅'], 'done');
        }, Response::HTTP_OK, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no'
        ]);
    }

    #[Route('/{id:profile}/configure', name: 'configure', methods: ['GET', 'POST'])]
    public function configure(Profile $profile, Request $request): Response
    {
        if (!in_array($profile, $this->getProfiles(), true)) {
            return new Response(status: Response::HTTP_FORBIDDEN);
        }

        $configuration = $this->entityManager->getRepository(Profile\Configuration::class)->findOneBy(['profile' => $profile, 'organization' => $this->workspaceService->getCurrentOrganization()]);
        if (!$configuration) {
            $configuration = (new Profile\Configuration())
                ->setProfile($profile)
                ->setOrganization($this->workspaceService->getCurrentOrganization())
            ;
        }

        $form = $this->createForm(ConfigurationType::class, $configuration);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($configuration);
            $this->entityManager->flush();

            $this->addFlash('success', 'Profile configuration saved.');
            return $this->redirectToRoute('app_meta_profile_configure', ['id' => $profile->getId()]);
        }

        return $this->render('app/meta/profile/configure.html.twig', [
            'configuration' => $configuration,
            'form' => $form->createView()
        ]);
    }

    private function getProfiles(?string $query = null, ?array $filters = []): array {
        $profiles = [];
        $trackingGroups = $this->entityManager->getRepository(TrackingGroup::class)->createQueryBuilder('tg');
        $tg = $filters['entities']['tg'] ?? null;

        if (!$tg) {
            $trackingGroups = $trackingGroups
                ->where('tg.ownerOrganization = :currentOrganization')
                ->setParameter('currentOrganization', $this->workspaceService->getCurrentOrganization())
            ;
        } else {
            /** @var TrackingGroup $dbTg */
            $dbTg = $this->entityManager->getRepository(TrackingGroup::class)->find($tg);

            if ($dbTg && $dbTg->getOwnerOrganization() === $this->workspaceService->getCurrentOrganization()) {
                $trackingGroups = $trackingGroups
                    ->where('tg.id = :tg')
                    ->setParameter('tg', $dbTg)
                ;
            }
        }


        $trackingGroups = $trackingGroups
            ->getQuery()
            ->getResult()
        ;

        $metaIds = [];
        /** @var TrackingGroup[] $trackingGroups */
        foreach ($trackingGroups as $trackingGroup) {
            foreach ($trackingGroup->getListIds() as $item) {
                $metaIds[] = $item;
            }
        }

        $baseQuery = [
            'sql' => "SELECT p.* FROM meta_profile p
             LEFT JOIN meta_profile_configuration c ON c.profile_id = p.id AND c.organization_id = :currentOrganization
             WHERE p.meta_id IN (:metaIds)",
            'parameters' => [
                'metaIds' => $metaIds,
                'currentOrganization' => $this->workspaceService->getCurrentOrganization()->getId()
            ],
            'types' => [
                'metaIds' => Connection::PARAM_STR_ARRAY
            ]
        ];

        $profiles = $this->customFilterService->applyFilters(
            $baseQuery,
            'c',
            'custom_fields',
            $filters['custom_fields'] ?? []
        );
        $profiles = $this->entityManager->getRepository(Profile::class)->findBy(['id' => array_column($profiles, 'id')]);

        if (!empty($query)) {
            $profiles = array_filter($profiles, function (Profile $p) use ($query) {
                $searchableInfo = $p->getUsername() . '|' . $p->getMetaId() . '|' . $p->getPlatform();

                if (array_key_exists('name', $p->getData()) && !empty($p->getData()['name'])) {
                    $searchableInfo .= '|' . $p->getData()['name'];
                }

                return u($searchableInfo)->lower()->containsAny(strtolower($query));
            });
        }

        return $profiles;
    }

    private function getPosts(Profile $profile, int $limit = 12, int $offset = 0): array {
        $sql = "
            SELECT *
            FROM meta_media m
            WHERE profile_id = :profileId
            ORDER BY COALESCE(data->>'timestamp', data->>'created_time')::timestamp DESC
            LIMIT :limit OFFSET :offset
        ";

        $stmt = $this->entityManager->getConnection()->prepare($sql);
        $posts = $stmt->executeQuery([
            'profileId' => $profile->getId()->toRfc4122(),
            'limit' => $limit,
            'offset' => $offset,
        ])->fetchAllAssociative();

       return array_map(function (array $post) {
            $post['data'] = json_decode($post['data'] ?? '[]', true);
            return $post;
        }, $posts);
    }
}