<?php
namespace App\Controller\App;

use App\Entity\Organization;
use App\Entity\OrganizationUser;
use App\Entity\User;
use App\Form\OrganizationType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/app/organization')]
final class OrganizationController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ){}

    #[Route('/', name: 'app_organization')]
    public function index(Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();

        $currentOrgUserRelation = array_filter($user->getOrganizationUsers()->toArray(), function (OrganizationUser $item) use($user, $request) {
            return $request->getSession()->get('current_organization') == $item->getOrganization()->getId()->toRfc4122();
        });
        $currentOrgUserRelation = array_values($currentOrgUserRelation);

        if (empty($currentOrgUserRelation) || $currentOrgUserRelation[0]->getRole() !== 'admin') {
            return $this->render('app/organization/index.html.twig', [
                'is_authorized' => false,
                'forms' => [
                    'organization' => $this->createForm(OrganizationType::class, new Organization())->createView()
                ]
            ]);
        }

        $organization = $currentOrgUserRelation[0]->getOrganization();
        $form = $this->createForm(OrganizationType::class, $organization);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->flush();
            $this->addFlash('success', 'Organization updated.');
            return $this->redirectToRoute('app_organization');
        }

        return $this->render('app/organization/index.html.twig', [
            'is_authorized' => true,
            'organization' => $organization,
            'forms' => [
                'current' => $form->createView(),
                'organization' => $this->createForm(OrganizationType::class, new Organization())->createView()
            ]
        ]);
    }

    #[Route('/create', name: 'app_organization_create', methods: ['POST'])]
    public function create(Request $request): Response {
        /** @var User $user */
        $user = $this->getUser();
        $organization = new Organization();

        $form = $this->createForm(OrganizationType::class, $organization);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $organization->addOrganizationUser(
                (new OrganizationUser())
                    ->setOrganization($organization)
                    ->setUser($user)
                    ->setRole('admin')
            );

            $this->entityManager->persist($organization);
            $this->entityManager->flush();

            $this->addFlash('success', "Organization created. You are now on {$organization->getName()}'s dashboard.");
            return $this->redirectToRoute('app_organization', ['switch-to-org' => $organization->getId()->toRfc4122()]);
        }

        return $this->redirectToRoute('app_organization');
    }
}
