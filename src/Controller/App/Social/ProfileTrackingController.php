<?php

namespace App\Controller\App\Social;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class ProfileTrackingController extends AbstractController
{
    #[Route('/app/social/profile/tracking', name: 'app_social_profile_tracking')]
    public function index(): Response
    {
        return $this->render('app/social/profile_tracking/index.html.twig', [
            'controller_name' => 'ProfileTrackingController',
        ]);
    }
}
