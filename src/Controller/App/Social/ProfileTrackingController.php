<?php

namespace App\Controller\App\Social;

use App\Entity\User;
use App\Repository\Social\TrackingGroupRepository;
use App\Service\WorkspaceService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class ProfileTrackingController extends AbstractController
{
    public function __construct(
        private readonly TrackingGroupRepository $trackingGroupRepository,
        private readonly WorkspaceService $workspaceService,
        private readonly EntityManagerInterface $entityManager
    ) {}

    #[Route('/app/social/profile/tracking', name: 'app_social_profile_tracking')]
    public function index(Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organization = $this->workspaceService->getCurrentOrganization();

        // Get tracking groups for the current organization
        $trackingGroups = $this->trackingGroupRepository->findBy(['ownerOrganization' => $organization]);

        return $this->render('app/social/profile_tracking/index.html.twig', [
            'trackingGroups' => $trackingGroups,
        ]);
    }

    #[Route('/app/social/profile/tracking/create', name: 'app_social_profile_tracking_create')]
    public function create(Request $request): Response
    {
        return $this->render('app/social/profile_tracking/wizard.html.twig');
    }

    #[Route('/app/social/profile/tracking/create-ajax', name: 'app_social_profile_tracking_create_ajax', methods: ['POST'])]
    public function createAjax(Request $request): Response
    {
        if (!$request->isXmlHttpRequest()) {
            return new JsonResponse(['success' => false, 'message' => 'Invalid request'], 400);
        }

        try {
            $data = json_decode($request->getContent(), true);
            if (!$data) {
                throw new \Exception('Invalid data format');
            }

            // Create a new tracking group entity
            $trackingGroup = new \App\Entity\Social\TrackingGroup();
            $trackingGroup->setName($data['name'] ?: (new \DateTimeImmutable())->format('Y-m-d-H-i-s'));
            $trackingGroup->setDataSource($data['dataSource']);
            $trackingGroup->setListType($data['listType']);
            $trackingGroup->setCategory($data['category']);
            $trackingGroup->setSubCategory($data['subCategory'] ?? '');
            $trackingGroup->setList($data['list']);
            $trackingGroup->setMaximumPostsPerProfile($data['maximumPostsPerProfile']);
            $trackingGroup->setMaximumCommentsPerPost($data['maximumCommentsPerPost']);
            $trackingGroup->setRunProfileAIAnalysis($data['runProfileAIAnalysis']);
            $trackingGroup->setKeepDataUpdated($data['keepDataUpdated']);
            $trackingGroup->setCustomFields($data['customFields'] ?? []);

            // Set organization and user
            $trackingGroup->setOwnerOrganization($this->workspaceService->getCurrentOrganization());
            $trackingGroup->setCreatedBy($this->getUser());

            // Create and set tracking status
            $trackingStatus = new \App\Entity\Social\TrackingStatus();
            $trackingStatus->setTrackingGroup($trackingGroup);
            $trackingStatus->setStatus(\App\Entity\Social\TrackingGroup\Status::Queued);
            $trackingStatus->setTotalProfiles(count($data['list']));
            $trackingStatus->setProgress(0);

            // Save to database
            $this->entityManager->persist($trackingGroup);
            $this->entityManager->persist($trackingStatus);
            $this->entityManager->flush();

            return new JsonResponse([
                'success' => true,
                'message' => 'Tracking Group created successfully!',
                'redirect' => $this->generateUrl('app_social_profile_tracking'),
                'id' => $trackingGroup->getId()->__toString()
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'message' => 'Error creating tracking group: ' . $e->getMessage()
            ], 500);
        }
    }

    #[Route('/app/social/profile/tracking/{id}/pause', name: 'app_social_tracking_group_pause')]
    public function pause(string $id): Response
    {
        $trackingGroup = $this->trackingGroupRepository->find($id);

        if (!$trackingGroup) {
            throw $this->createNotFoundException('Tracking group not found');
        }

        $trackingStatus = $trackingGroup->getStatus();
        $trackingStatus->setStatus(\App\Entity\Social\TrackingGroup\Status::Paused);

        $this->entityManager->flush();

        $this->addFlash('success', 'Tracking group paused successfully');

        return $this->redirectToRoute('app_social_profile_tracking');
    }

    #[Route('/app/social/profile/tracking/{id}/queue', name: 'app_social_tracking_group_queue')]
    public function queue(string $id): Response
    {
        $trackingGroup = $this->trackingGroupRepository->find($id);

        if (!$trackingGroup) {
            throw $this->createNotFoundException('Tracking group not found');
        }

        $trackingStatus = $trackingGroup->getStatus();
        $trackingStatus->setStatus(\App\Entity\Social\TrackingGroup\Status::Queued);

        $this->entityManager->flush();

        $this->addFlash('success', 'Tracking group queued for processing');

        return $this->redirectToRoute('app_social_profile_tracking');
    }
}
