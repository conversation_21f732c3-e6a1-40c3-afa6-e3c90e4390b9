<?php

namespace App\Controller\App\Social;

use App\Entity\User;
use App\Repository\Social\TrackingGroupRepository;
use App\Service\WorkspaceService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class ProfileTrackingController extends AbstractController
{
    public function __construct(
        private readonly TrackingGroupRepository $trackingGroupRepository,
        private readonly WorkspaceService $workspaceService,
        private readonly EntityManagerInterface $entityManager
    ) {}

    #[Route('/app/social/profile/tracking', name: 'app_social_profile_tracking')]
    public function index(Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organization = $this->workspaceService->getCurrentOrganization();

        // Get tracking groups for the current organization
        $trackingGroups = $this->trackingGroupRepository->findBy(['ownerOrganization' => $organization]);

        return $this->render('app/social/profile_tracking/index.html.twig', [
            'trackingGroups' => $trackingGroups,
        ]);
    }

    #[Route('/app/social/profile/tracking/create', name: 'app_social_profile_tracking_create')]
    public function create(Request $request): Response
    {
        return $this->render('app/social/profile_tracking/wizard.html.twig');
    }

    #[Route('/app/social/profile/tracking/{id}/pause', name: 'app_social_tracking_group_pause')]
    public function pause(string $id): Response
    {
        $trackingGroup = $this->trackingGroupRepository->find($id);

        if (!$trackingGroup) {
            throw $this->createNotFoundException('Tracking group not found');
        }

        $trackingStatus = $trackingGroup->getStatus();
        $trackingStatus->setStatus(\App\Entity\Social\TrackingGroup\Status::Paused);

        $this->entityManager->flush();

        $this->addFlash('success', 'Tracking group paused successfully');

        return $this->redirectToRoute('app_social_profile_tracking');
    }

    #[Route('/app/social/profile/tracking/{id}/queue', name: 'app_social_tracking_group_queue')]
    public function queue(string $id): Response
    {
        $trackingGroup = $this->trackingGroupRepository->find($id);

        if (!$trackingGroup) {
            throw $this->createNotFoundException('Tracking group not found');
        }

        $trackingStatus = $trackingGroup->getStatus();
        $trackingStatus->setStatus(\App\Entity\Social\TrackingGroup\Status::Queued);

        $this->entityManager->flush();

        $this->addFlash('success', 'Tracking group queued for processing');

        return $this->redirectToRoute('app_social_profile_tracking');
    }
}
