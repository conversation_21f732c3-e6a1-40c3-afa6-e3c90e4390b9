<?php
namespace App\Controller\App\Social;

use App\Entity\Data\Filter;
use App\Entity\OrganizationUser;
use App\Entity\Social\TrackingGroup;
use App\Entity\User;
use App\Form\Social\TrackingGroupType;
use App\Service\WorkspaceService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use function Symfony\Component\String\u;

#[Route('/app/social/tracking-group', name: 'app_social_tracking_group_')]
final class TrackingGroupController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly WorkspaceService $workspaceService
    ) {}

    #[Route('/{id:trackingGroup}/view', name: 'view')]
    public function view(TrackingGroup $trackingGroup): Response
    {
        if (!$this->authorized($trackingGroup)) {
            return new Response('', Response::HTTP_FORBIDDEN);
        }

        if (!$trackingGroup->getCredential()) {
            $credential = new TrackingGroup\Credential($trackingGroup);
            $trackingGroup->setCredential($credential);
            $this->entityManager->flush();
        }

        return $this->render('app/social/tracking_group/index.html.twig', [
            'trackingGroup' => $trackingGroup
        ]);
    }

    #[Route('/{id:trackingGroup}/edit', name: 'edit')]
    public function edit(TrackingGroup $trackingGroup, Request $request): Response
    {
        if (!$this->authorized($trackingGroup)) {
            return new Response('', Response::HTTP_FORBIDDEN);
        }

        $form = $this->createForm(TrackingGroupType::class, $trackingGroup, ['edit' => true]);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $rawList = $form->get('list')->getData();
            $list = u($rawList)->containsAny(',') ? explode(',', $rawList) : explode("\r\n", $rawList);
            $trackingGroup
                ->updateList($list)
            ;

            $status = $trackingGroup->getStatus()->getStatus();

            if (
                !in_array($status, [
                    TrackingGroup\Status::Processing,
                    TrackingGroup\Status::Paused,
                    TrackingGroup\Status::PauseRequested
                ])
            ) {
                $trackingGroup->getStatus()->setStatus(TrackingGroup\Status::Queued);
            }

            $trackingGroup
                ->getStatus()
                ->setTotalProfiles(count($list))
            ;

            $_formData = $request->request->all('tracking_group');

            $startsAt = $this->entityManager->getRepository(Filter::class)->findOneBy(['entityId' => $trackingGroup->getId(), 'entityType' => get_class($trackingGroup), 'field' => 'starts_at']);
            if (!empty($_formData['filterStartsAt'] ?? null)) {
                if (!$startsAt) {
                    $startsAt = (new Filter())
                        ->setField('starts_at')
                        ->setOperator('=')
                        ->setEntityId($trackingGroup->getId())
                        ->setEntityType(get_class($trackingGroup))
                    ;
                }

                $startsAt->setValue($_formData['filterStartsAt']);
                $this->entityManager->persist($startsAt);
            } else {
                if ($startsAt) {
                    $this->entityManager->remove($startsAt);
                }
            }

            $endsAt = $this->entityManager->getRepository(Filter::class)->findOneBy(['entityId' => $trackingGroup->getId(), 'entityType' => get_class($trackingGroup), 'field' => 'ends_at']);
            if (!empty($_formData['filterEndsAt'] ?? null)) {
                if (!$endsAt) {
                    $endsAt = (new Filter())
                        ->setField('ends_at')
                        ->setOperator('=')
                        ->setEntityId($trackingGroup->getId())
                        ->setEntityType(get_class($trackingGroup))
                    ;
                }

                $endsAt->setValue($_formData['filterEndsAt']);
                $this->entityManager->persist($endsAt);
            } else {
                if ($endsAt) {
                    $this->entityManager->remove($endsAt);
                }
            }

            $this->entityManager->flush();
            $this->addFlash('success', 'Tracking group edited.');

            return $this->redirectToRoute('app_social_tracking_group_view', ['id' => $trackingGroup->getId()]);
        }

        $filters = [];
        $dbFilters = $this->entityManager->getRepository(Filter::class)->findBy(['entityId' => $trackingGroup->getId(), 'entityType' => get_class($trackingGroup)]);
        foreach ($dbFilters as $dbFilter) {
            $filters[$dbFilter->getField()] = $dbFilter->getValue();
        }

        return $this->render('app/social/tracking_group/edit.html.twig', [
            'edit' => true,
            'trackingGroup' => $trackingGroup,
            'filters' => $filters,
            'form' => $form->createView()
        ]);
    }

    #[Route('/{id:trackingGroup}/delete', name: 'delete')]
    public function delete(TrackingGroup $trackingGroup): Response
    {
        if (!$this->authorized($trackingGroup)) {
            return new Response('', Response::HTTP_FORBIDDEN);
        }

        $dbFilters = $this->entityManager->getRepository(Filter::class)->findBy(['entityId' => $trackingGroup->getId(), 'entityType' => get_class($trackingGroup)]);

        foreach ($dbFilters as $dbFilter) {
            $this->entityManager->remove($dbFilter);
        }

        $this->entityManager->remove($trackingGroup);
        $this->entityManager->flush();

        return $this->redirectToRoute('app_app_dashboard');
    }

    #[Route('/{id:trackingGroup}/queue', name: 'queue')]
    public function queue(TrackingGroup $trackingGroup): Response
    {
        if (!$this->authorized($trackingGroup)) {
            return $this->redirectToRoute('app_app_dashboard');
        }

        $trackingGroup->getStatus()->setStatus(TrackingGroup\Status::Queued);
        $this->entityManager->flush();

        $this->addFlash('success', "Tracking group {$trackingGroup->getName()} queued.");
        return $this->redirectToRoute('app_app_dashboard');
    }

    #[Route('/{id:trackingGroup}/force-update', name: 'force_update')]
    public function forceUpdate(TrackingGroup $trackingGroup): Response
    {
        if (!$this->authorized($trackingGroup)) {
            return $this->redirectToRoute('app_app_dashboard');
        }

        $trackingGroup->getStatus()->setStatus(TrackingGroup\Status::UpdateRequested);
        $this->entityManager->flush();

        $this->addFlash('success', "Tracking group {$trackingGroup->getName()} will be updated soon.");
        return $this->redirectToRoute('app_app_dashboard');
    }

    #[Route('/{id:trackingGroup}/pause', name: 'pause')]
    public function pause(TrackingGroup $trackingGroup): Response
    {
        if (!$this->authorized($trackingGroup)) {
            return $this->redirectToRoute('app_app_dashboard');
        }

        if ($trackingGroup->getStatus()->getStatus() === TrackingGroup\Status::Queued) {
            $trackingGroup->getStatus()->setStatus(TrackingGroup\Status::Paused);
        } else if ($trackingGroup->getStatus()->getStatus() === TrackingGroup\Status::Processing) {
            $trackingGroup->getStatus()->setStatus(TrackingGroup\Status::PauseRequested);
        }

        $this->entityManager->flush();

        $this->addFlash('success', "Pause requested for tracking group {$trackingGroup->getName()}. It will be paused soon.");
        return $this->redirectToRoute('app_app_dashboard');
    }

    private function authorized(TrackingGroup $trackingGroup): bool {
        /** @var User $user */
        $user = $this->getUser();
        $userOrganizations = array_map(function (OrganizationUser $relation) {
            return $relation->getOrganization();
        }, $user->getOrganizationUsers()->toArray());

        return in_array($trackingGroup->getOwnerOrganization(), $userOrganizations) && $trackingGroup->getOwnerOrganization() === $this->workspaceService->getCurrentOrganization();
    }
}
