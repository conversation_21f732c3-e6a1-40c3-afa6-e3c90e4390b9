<?php

namespace App\Controller\App\Social;

use App\Entity\Meta\Profile;
use App\Entity\Organization\CustomField;
use App\Entity\Organization\CustomFieldTemplate;
use App\Entity\Social\TrackingGroup;
use App\Entity\User;
use App\Form\Meta\Profile\ConfigurationType;
use App\Helper\Social\Meta\ReactionsHelper;
use App\Repository\Meta\ProfileRepository;
use App\Service\CustomFilterService;
use App\Service\Social\ProfileUpdateService;
use App\Service\WorkspaceService;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use function Symfony\Component\String\u;

#[Route('/app/social/profile', name: 'app_social_profile_')]
final class ProfileController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly WorkspaceService $workspaceService,
        private readonly ProfileRepository $profileRepository,
        private readonly CustomFilterService $customFilterService,
        private readonly ProfileUpdateService $profileUpdateService
    ) {
        @ini_set('memory_limit', '1G');
    }

    #[Route('', name: 'index')]
    public function index(Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organization = $this->workspaceService->getCurrentOrganization();

        // Get pagination parameters
        $page = max(1, (int) $request->query->get('page', 1));
        $limit = max(1, min(100, (int) $request->query->get('limit', 20))); // Max 100 per page

        // Get filter parameters
        $search = $request->query->get('q');
        $platform = $request->query->get('platform', 'all');
        $trackingGroupId = $request->query->get('group-id');

        // Parse custom filters
        $customFilters = [];
        $filtersParam = $request->query->all('filters');
        if (is_array($filtersParam)) {
            foreach ($filtersParam as $filter) {
                if (isset($filter['key']) && !empty(trim($filter['key']))) {
                    $customFilters[] = [
                        'key' => trim($filter['key']),
                        'operator' => $filter['operator'] ?? 'equals',
                        'value' => $filter['value'] ?? null
                    ];
                }
            }
        }

        // Get profiles with pagination
        $result = $this->profileRepository->findProfilesWithPagination(
            $organization,
            $page,
            $limit,
            $search,
            $platform,
            $trackingGroupId,
            $customFilters
        );

        // Get additional data for filters and actions
        $availableCustomFields = $this->entityManager->getRepository(CustomField::class)
            ->findBy(['organization' => $organization]);

        $availableCustomFieldTemplates = $this->entityManager->getRepository(CustomFieldTemplate::class)
            ->findBy(['organization' => $organization]);

        $trackingGroups = $this->entityManager->getRepository(TrackingGroup::class)
            ->findBy(['ownerOrganization' => $organization], ['name' => 'ASC']);

        $availablePlatforms = $this->profileRepository->getAvailablePlatforms($organization);
        $profileStats = $this->profileRepository->getProfileStats($organization);

        return $this->render('app/social/profile/index.html.twig', [
            'profiles' => $result['profiles'],
            'pagination' => [
                'current_page' => $result['currentPage'],
                'total_pages' => $result['pages'],
                'total_items' => $result['total'],
                'items_per_page' => $limit,
                'has_previous' => $result['currentPage'] > 1,
                'has_next' => $result['currentPage'] < $result['pages'],
                'previous_page' => max(1, $result['currentPage'] - 1),
                'next_page' => min($result['pages'], $result['currentPage'] + 1)
            ],
            'filters' => [
                'search' => $search,
                'platform' => $platform,
                'tracking_group_id' => $trackingGroupId,
                'custom_filters' => $customFilters
            ],
            'data' => [
                'availableCustomFields' => $availableCustomFields,
                'availableCustomFieldTemplates' => $availableCustomFieldTemplates,
                'availablePlatforms' => $availablePlatforms,
                'profileStats' => $profileStats
            ],
            'tracking_groups' => $trackingGroups
        ]);
    }

    #[Route('/{id:profile}/view', name: 'view', methods: ['GET', 'POST'])]
    public function view(Profile $profile, Request $request): Response
    {
        $organization = $this->workspaceService->getCurrentOrganization();

        // Security check: ensure profile belongs to organization
        if (!$this->profileRepository->profileBelongsToOrganization($profile, $organization)) {
            throw $this->createAccessDeniedException('You do not have access to this profile.');
        }

        $configuration = $this->entityManager->getRepository(Profile\Configuration::class)
            ->findOneBy(['profile' => $profile, 'organization' => $organization]);

        if (!$configuration) {
            $configuration = (new Profile\Configuration())
                ->setProfile($profile)
                ->setOrganization($organization);
        }

        $customFieldsForm = $this->createForm(ConfigurationType::class, $configuration);
        $customFieldsForm->handleRequest($request);

        if ($customFieldsForm->isSubmitted() && $customFieldsForm->isValid()) {
            $this->entityManager->persist($configuration);
            $this->entityManager->flush();

            $this->addFlash('success', 'Profile configuration saved.');
            return $this->redirectToRoute('app_social_profile_view', ['id' => $profile->getId()]);
        }

        return $this->render('app/social/profile/view.html.twig', [
            'profile' => $profile,
            'posts' => $this->getPosts($profile),
            'configuration' => $configuration,
            'customFieldsForm' => $customFieldsForm->createView()
        ]);
    }

    #[Route('/{id:profile}/posts', name: 'posts')]
    public function posts(Profile $profile, Request $request): JsonResponse
    {
        $organization = $this->workspaceService->getCurrentOrganization();

        // Security check: ensure profile belongs to organization
        if (!$this->profileRepository->profileBelongsToOrganization($profile, $organization)) {
            throw $this->createAccessDeniedException('You do not have access to this profile.');
        }

        $offset = max(0, (int) $request->query->get('offset', 0));
        $limit = max(1, min(50, (int) $request->query->get('limit', 12))); // Max 50 posts per request

        $posts = $this->getPosts($profile, $limit, $offset);

        if ($profile->getPlatform() === TrackingGroup\DataSource::Facebook->value) {
            $posts = array_map(function (array $post) {
                $flatData = ReactionsHelper::flattenReactionsCount($post['data']);
                $total = 0;

                foreach (ReactionsHelper::getFacebookReactionsKeys() as $facebookReactionsKey) {
                    $total += $flatData['reactions_' . $facebookReactionsKey . '_count'] ?? 0;
                }

                $post['data']['like_count'] = $total;
                return $post;
            }, $posts);
        }

        return $this->json($posts);
    }

    #[Route('/{id:profile}/update', name: 'update')]
    public function update(Profile $profile): Response
    {
        $organization = $this->workspaceService->getCurrentOrganization();

        // Security check: ensure profile belongs to organization
        if (!$this->profileRepository->profileBelongsToOrganization($profile, $organization)) {
            throw $this->createAccessDeniedException('You do not have access to this profile.');
        }

        $this->profileUpdateService
            ->setAnalyzeProfileWithAI(false)
            ->setAnalyzeMediaWithAI(false)
            ->setForceIgnoreComments(true)
            ->setOrganization($organization)
            ->setPostsLimit(150);

        try {
            $this->profileUpdateService->updateProfile($profile);
            $this->addFlash('success', 'Profile updated successfully.');
        } catch (\Exception $e) {
            $this->addFlash('error', 'Failed to update profile: ' . $e->getMessage());
        }

        return $this->redirectToRoute('app_social_profile_view', ['id' => $profile->getId()]);
    }

    #[Route('/{id:profile}/configure', name: 'configure', methods: ['GET', 'POST'])]
    public function configure(Profile $profile, Request $request): Response
    {
        $organization = $this->workspaceService->getCurrentOrganization();

        // Security check: ensure profile belongs to organization
        if (!$this->profileRepository->profileBelongsToOrganization($profile, $organization)) {
            throw $this->createAccessDeniedException('You do not have access to this profile.');
        }

        $configuration = $this->entityManager->getRepository(Profile\Configuration::class)
            ->findOneBy(['profile' => $profile, 'organization' => $organization]);

        if (!$configuration) {
            $configuration = (new Profile\Configuration())
                ->setProfile($profile)
                ->setOrganization($organization);
        }

        $form = $this->createForm(ConfigurationType::class, $configuration);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->entityManager->persist($configuration);
            $this->entityManager->flush();

            $this->addFlash('success', 'Profile configuration updated.');
            return $this->redirectToRoute('app_social_profile_index');
        }

        return $this->render('app/social/profile/configure.html.twig', [
            'profile' => $profile,
            'form' => $form->createView()
        ]);
    }

    #[Route('/export', name: 'export')]
    public function export(Request $request): StreamedResponse
    {
        $organization = $this->workspaceService->getCurrentOrganization();

        // Get filter parameters (same as index)
        $search = $request->query->get('q');
        $platform = $request->query->get('platform', 'all');
        $trackingGroupId = $request->query->get('group-id');

        // Parse custom filters
        $customFilters = [];
        $filtersParam = $request->query->all('filters');
        if (is_array($filtersParam)) {
            foreach ($filtersParam as $filter) {
                if (isset($filter['key']) && !empty(trim($filter['key']))) {
                    $customFilters[] = [
                        'key' => trim($filter['key']),
                        'operator' => $filter['operator'] ?? 'equals',
                        'value' => $filter['value'] ?? null
                    ];
                }
            }
        }

        $response = new StreamedResponse();
        $response->setCallback(function () use ($organization, $search, $platform, $trackingGroupId, $customFilters) {
            $handle = fopen('php://output', 'w+');

            // CSV headers
            fputcsv($handle, [
                'ID', 'Username', 'Platform', 'Meta ID', 'Name', 'Followers', 'Following', 'Posts', 'Created At'
            ]);

            // Export in batches to handle large datasets
            $page = 1;
            $limit = 1000;

            do {
                $result = $this->profileRepository->findProfilesWithPagination(
                    $organization,
                    $page,
                    $limit,
                    $search,
                    $platform,
                    $trackingGroupId,
                    $customFilters
                );

                foreach ($result['profiles'] as $profile) {
                    $data = $profile->getData() ?? [];
                    fputcsv($handle, [
                        $profile->getId()->toRfc4122(),
                        $profile->getUsername(),
                        $profile->getPlatform(),
                        $profile->getMetaId(),
                        $data['name'] ?? '',
                        $data['followers_count'] ?? 0,
                        $data['following_count'] ?? 0,
                        $data['media_count'] ?? 0,
                        $profile->getCreatedAt()?->format('Y-m-d H:i:s')
                    ]);
                }

                $page++;
            } while ($page <= $result['pages']);

            fclose($handle);
        });

        $response->headers->set('Content-Type', 'text/csv; charset=utf-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="profiles_export_' . date('Y-m-d_H-i-s') . '.csv"');

        return $response;
    }

    #[Route('/bulk-configure', name: 'bulk_configure', methods: ['POST'])]
    public function bulkConfigure(Request $request): Response
    {
        $organization = $this->workspaceService->getCurrentOrganization();

        $selectedProfiles = $request->request->all('form_selectedProfiles');
        $customFieldTemplates = $request->request->all('form_cft_key');
        $customFieldValues = $request->request->all('form_cft_value');
        $organizationCustomFields = $request->request->all('form_ocf');

        if (empty($selectedProfiles)) {
            $this->addFlash('error', 'No profiles selected.');
            return $this->redirectToRoute('app_social_profile_index');
        }

        $profiles = $this->profileRepository->findBy(['id' => $selectedProfiles]);
        $updatedCount = 0;

        foreach ($profiles as $profile) {
            // Security check: ensure profile belongs to organization
            if (!$this->profileRepository->profileBelongsToOrganization($profile, $organization)) {
                continue;
            }

            $configuration = $this->entityManager->getRepository(Profile\Configuration::class)
                ->findOneBy(['profile' => $profile, 'organization' => $organization]);

            if (!$configuration) {
                $configuration = (new Profile\Configuration())
                    ->setProfile($profile)
                    ->setOrganization($organization);
            }

            $customFields = $configuration->getCustomFields() ?? [];

            // Add custom field templates
            if (!empty($customFieldTemplates)) {
                foreach ($customFieldTemplates as $index => $key) {
                    $value = $customFieldValues[$index] ?? '';
                    if (!empty(trim($key))) {
                        $customFields[] = ['key' => trim($key), 'value' => $value];
                    }
                }
            }

            // Add organization custom fields
            if (!empty($organizationCustomFields)) {
                foreach ($organizationCustomFields as $fieldId) {
                    $customField = $this->entityManager->getRepository(CustomField::class)->find($fieldId);
                    if ($customField && $customField->getOrganization() === $organization) {
                        $customFields[] = [
                            'key' => $customField->getKey(),
                            'value' => $customField->getValue()
                        ];
                    }
                }
            }

            $configuration->setCustomFields($customFields);
            $this->entityManager->persist($configuration);
            $updatedCount++;
        }

        $this->entityManager->flush();

        $this->addFlash('success', "Updated configuration for {$updatedCount} profiles.");
        return $this->redirectToRoute('app_social_profile_index');
    }

    private function getPosts(Profile $profile, int $limit = 12, int $offset = 0): array
    {
        $sql = "
            SELECT *
            FROM meta_media m
            WHERE profile_id = :profileId
            ORDER BY COALESCE(data->>'timestamp', data->>'created_time')::timestamp DESC
            LIMIT :limit OFFSET :offset
        ";

        $stmt = $this->entityManager->getConnection()->prepare($sql);
        $posts = $stmt->executeQuery([
            'profileId' => $profile->getId()->toRfc4122(),
            'limit' => $limit,
            'offset' => $offset,
        ])->fetchAllAssociative();

        return array_map(function (array $post) {
            $post['data'] = json_decode($post['data'] ?? '[]', true);
            return $post;
        }, $posts);
    }
}
