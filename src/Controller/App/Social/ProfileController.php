<?php

namespace App\Controller\App\Social;

use App\Entity\User;
use App\Service\WorkspaceService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class ProfileController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly WorkspaceService $workspaceService
    ) {}

    #[Route('/app/social/profile', name: 'app_social_profile')]
    public function index(Request $request): Response
    {
        /** @var User $user */
        $user = $this->getUser();
        $organization = $this->workspaceService->getCurrentOrganization();
        
        // Get profiles from the repository
        $profileRepository = $this->entityManager->getRepository(\App\Entity\Meta\Profile::class);
        
        // Get profiles for the current organization
        $profiles = $profileRepository->findBy(
            ['organization' => $organization],
            ['createdAt' => 'DESC'],
            100 // Limit to 100 profiles for performance
        );
        
        // Get platform filter from request
        $platformFilter = $request->query->get('platform', 'all');
        
        return $this->render('app/social/profile/index.html.twig', [
            'profiles' => $profiles,
            'platformFilter' => $platformFilter
        ]);
    }
}
