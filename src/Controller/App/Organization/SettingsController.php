<?php

namespace App\Controller\App\Organization;

use App\Entity\Meta\Profile\Configuration;
use App\Entity\Organization\CustomField;
use App\Entity\Organization\CustomFieldTemplate;
use App\Form\Organization\CustomFieldTemplateType;
use App\Form\Organization\CustomFieldType;
use App\Service\WorkspaceService;
use Arkounay\Bundle\UxCollectionBundle\Form\UxCollectionType;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormError;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Uid\Uuid;

#[Route('/app/organization/settings', name: 'app_organization_settings_')]
final class SettingsController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly WorkspaceService $workspace
    ) {}

    #[Route('/custom-fields', name: 'custom_fields')]
    public function customFields(Request $request): Response
    {
        $org          = $this->workspace->getCurrentOrganization();
        $repo         = $this->entityManager->getRepository(CustomField::class);
        $existing     = $repo->findBy(['organization' => $org], ['key' => 'ASC']);

        // 1) index existing by ID and by key
        $byId  = [];
        $byKey = [];
        foreach ($existing as $cf) {
            $byId[$cf->getId()->toRfc4122()]     = $cf;
            $byKey[$cf->getKey()]                = $cf;
        }

        $form = $this->createFormBuilder()
            ->add('fields', UxCollectionType::class, [
                'entry_type'        => CustomFieldType::class,
                'entry_options'     => ['allow_edit' => true],
                'label'             => 'Custom Fields',
                'data'              => $existing,
                'allow_add'         => true,
                'allow_delete'      => true,
                'by_reference'      => false,
                'required'          => false,
                'display_sort_buttons' => false,
                'min' => 0
            ])
            ->add('submit', SubmitType::class, ['label' => 'Save'])
            ->getForm();

        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            /** @var CustomField[] $submitted */
            $submitted   = $form->get('fields')->getData();
            $submittedIds = array_filter(array_map(
                fn(CustomField $f) => $f->getId()?->toRfc4122(),
                $submitted
            ));

            // 2) figure out which IDs were dropped
            $removedIds = array_diff(array_keys($byId), $submittedIds);

            // 3) remove those entities
            foreach ($removedIds as $rid) {
                $this->entityManager->remove($byId[$rid]);
            }

            // 4) validate & persist/merge
            foreach ($submitted as $f) {
                $key = trim($f->getKey());
                if (empty($key)) {
                    $form->get('fields')
                        ->addError(new FormError('Key cannot be empty'));
                    continue;
                }

                $fid = $f->getId()?->toRfc4122();
                if ($fid) {
                    // → updating an existing
                    $entity = $byId[$fid] ?? null;
                    if (!$entity) {
                        $form->get('fields')
                            ->addError(new FormError('Invalid field.'));
                        continue;
                    }

                    // if the key changed, make sure no _other_ field (not removed) uses it
                    if ($entity->getKey() !== $key) {
                        if (isset($byKey[$key]) && !in_array($byKey[$key]->getId()->toRfc4122(), $removedIds, true)) {
                            $form->get('fields')
                                ->addError(new FormError(
                                    sprintf('A field with key "%s" already exists.', $key)
                                ));
                            continue;
                        }
                    }

                    // merge changes
                    $entity
                        ->setKey($key)
                        ->setValue($f->getValue());

                } else {
                    // → brand‑new field
                    if (isset($byKey[$key]) && !in_array($byKey[$key]->getId()?->toRfc4122(), $removedIds, true)) {
                        $form->get('fields')
                            ->addError(new FormError(
                                sprintf('A field with key "%s" already exists.', $key)
                            ));
                        continue;
                    }
                    $f->setOrganization($org);
                    $this->entityManager->persist($f);
                }
            }

            if ($form->isValid()) {
                $this->entityManager->flush();
                $this->addFlash('success', 'Custom Fields Saved!');

                return $this->redirectToRoute('app_organization_settings_custom_fields');
            }
        }

        return $this->render('app/organization/settings/custom_fields.html.twig', [
            'form' => $form->createView(),
        ]);
    }

    #[Route('/custom-field-templates', name: 'custom_field_templates')]
    public function customFieldTemplates(Request $request): Response
    {
        $customFieldTemplates = $this->entityManager->getRepository(CustomFieldTemplate::class)->findBy(['organization' => $this->workspace->getCurrentOrganization()], ['key' => 'ASC']);
        $form = $this->createFormBuilder()
            ->add('fields', UxCollectionType::class, [
                'entry_type' => CustomFieldTemplateType::class,
                'label' => 'Custom Fields',
                'data' => $customFieldTemplates,
                'allow_add' => true,
                'allow_delete' => true,
                'display_sort_buttons' => false,
                'min' => 0,
                'required' => false,
                'by_reference' => false
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'Save'
            ])
            ->getForm()
        ;
        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            $postedFields = $form->get('fields')->getData();

            // Removed fields
            $removedFields = [];
            foreach ($customFieldTemplates as $cf) {
                if (!in_array($cf, $postedFields, true)) {
                    if ($cf->getOrganization() === $this->workspace->getCurrentOrganization()) {
                        $removedFields[] = $cf;
                        $this->entityManager->remove($cf);
                    } else {
                        $form->addError(new FormError('Not authorized.'));
                        $this->addFlash('error', 'Not authorized.');
                    }
                }
            }

            foreach ($postedFields as $field) {
                $dbField = $this->entityManager->getRepository(CustomFieldTemplate::class)->findOneBy(['key' => $field->getKey(), 'organization' => $this->workspace->getCurrentOrganization()]);

                if (!$dbField) {
                    $field->setOrganization($this->workspace->getCurrentOrganization());
                    $this->entityManager->persist($field);
                } else {
                    if ($dbField->getOrganization() !== $this->workspace->getCurrentOrganization()) {
                        $form->addError(new FormError('Not authorized.'));
                        $this->addFlash('error', 'Not authorized.');
                    } else {
                        // If the field was removed, then we allow to create a new one with same key.
                        if ($dbField->getId() !== $field->getId() && !in_array($dbField, $removedFields, true)) {
                            $errorMessage = "A field with key \"{$field->getKey()}\" already exists!";
                            $form->get('fields')->addError(new FormError($errorMessage));
                            $this->addFlash('error', $errorMessage);
                        } else {
                            $dbField
                                ->setKey($field->getKey())
                                ->setValue($field->getValue())
                                ->setOrganization($this->workspace->getCurrentOrganization())
                            ;
                        }
                    }
                }
            }

            if ($form->isValid()) {
                $this->entityManager->flush();
                $this->addFlash('success', 'Custom Field Templates Saved!');
            }

            return $this->redirectToRoute('app_organization_settings_custom_field_templates');
        }

        return $this->render('app/organization/settings/custom_field_templates.html.twig', [
            'form' => $form->createView(),
        ]);
    }
}
