<?php
namespace App\Controller\API\Social;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use function Symfony\Component\String\u;

#[Route('/api/social/meta/ads', name: 'app_api_social_meta_ads_')]
class MetaAdsController extends AbstractController
{
    private const string TOKEN = 'ea1e7c434c0bbcfdf6a633ac8def03973ba5d784294b982f25df6f7e97e7024f597c704e834d000cd8eba4564cd6f52ffb13eb2cbe1498f067b703bc6aa4633f';

    public function __construct(
        private readonly EntityManagerInterface $entityManager
    ) {}

    #[Route('/elections', name: 'elections', methods: ['GET'])]
    public function elections(Request $request): Response
    {
        $token = u($request->headers->get('authorization'))->lower()->after('bearer ')->trim()->toString();
        if (hash('sha512', $token) !== self::TOKEN) {
            return $this->json([], Response::HTTP_UNAUTHORIZED);
        }

        // Get the DBAL connection from Doctrine.
        $connection = $this->entityManager->getConnection();
        // Disable SQL logger for performance.
        $connection->getConfiguration()->setSQLLogger(null);

        return new StreamedResponse(function () use ($connection) {
            $stmt = $connection->executeQuery('
                SELECT id, meta_id, page_meta_id, type, platforms, data
                FROM meta_ad
                ORDER BY created_at DESC
            ');

            echo '[';
            $first = true;

            while ($row = $stmt->fetchAssociative()) {
                if (!$first) {
                    echo ',';
                } else {
                    $first = false;
                }

                if (!empty($row['platforms'])) {
                    $row['platforms'] = json_decode($row['platforms'], true);
                }

                if (!empty($row['data'])) {
                    $row['data'] = json_decode($row['data'], true);
                }

                echo json_encode($row, JSON_UNESCAPED_UNICODE);
                ob_flush();
                flush();
            }

            echo ']';

            ob_flush();
            flush();
        }, Response::HTTP_OK, ['Content-Type' => 'application/json']);
    }
}