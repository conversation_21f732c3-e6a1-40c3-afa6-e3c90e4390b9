<?php
namespace App\Controller\API\Social;

use App\Entity\Data\Filter;
use App\Entity\Meta\Media;
use App\Entity\Meta\Profile;
use App\Entity\Social\TrackingGroup;
use App\Helper\Social\Meta\ReactionsHelper;
use Aws\S3\S3Client;
use Doctrine\DBAL\ArrayParameterType;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use GuzzleHttp\Promise;
use PDO;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;
use Symfony\Component\Uid\Uuid;
use Throwable;
use function Symfony\Component\String\u;

#[Route('/api/social/tracking-group', name: 'app_api_social_tracking_group_')]
class TrackingGroupController extends AbstractController
{
    private float $startTime;

    public function __construct(
        private readonly S3Client $s3,
        private readonly EntityManagerInterface $entityManager,
        private readonly LoggerInterface $logger,
        private readonly string $bucket = 'magnora',
    ){
        @ini_set('memory_limit', '4G');
        @ini_set('max_execution_time', 0);

        $this->startTime = microtime(true);
    }

    #[Route('/{id:trackingGroup}/all', name: 'all')]
    public function all(TrackingGroup $trackingGroup, Request $request): Response
    {
        $token = u($request->headers->get('authorization'))->lower()->after('bearer ')->trim()->toString();
        if (!$trackingGroup->getCredential() || $token !== $trackingGroup->getCredential()->getToken()) {
            return $this->json([], Response::HTTP_UNAUTHORIZED);
        }

        $logDirName = dirname(__DIR__, 4) . '/';
        $__startTime = microtime(true);
        $__clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $__url = $request->getUri();

        $__log = function(string $msg) use (&$__startTime, $__clientIp, $__url, $logDirName) {
            $now = microtime(true);
            $formattedTime = date('Y-m-d H:i:s') . sprintf('.%03d', ($now - floor($now)) * 1000);
            $elapsed = round(($now - $__startTime) * 1000); // in ms
            $logLine = "[$formattedTime] +{$elapsed}ms [IP: {$__clientIp}] [URL: {$__url}]: $msg\n\n";
            file_put_contents($logDirName . '/runtime.log', $logLine, FILE_APPEND);
        };

        $__log("Started processing request");

        // Get the DBAL connection from Doctrine.
        $connection = $this->entityManager->getConnection();
        // Disable SQL logger for performance.
        $connection->getConfiguration()->setSQLLogger(null);

        $platform = $trackingGroup->getDataSource();
        $ids = $trackingGroup->getListIds();
        $usernames = $trackingGroup->getListForDisplay();
        $organizationId = $trackingGroup->getOwnerOrganization()->getId()->toRfc4122();

        $connection = $this->entityManager->getConnection();

        // Combined query: profile id, custom_fields, and org custom fields
        $rows = $connection->executeQuery(
            <<<SQL
        SELECT
            p.id AS profile_id,
            pc.custom_fields,
            ocf.key AS org_cf_key,
            ocf.value AS org_cf_value
        FROM meta_profile p
        LEFT JOIN meta_profile_configuration pc
            ON pc.profile_id = p.id AND pc.organization_id = :orgId
        LEFT JOIN meta_profile_configuration_organization_custom_field pc_ocf
            ON pc_ocf.configuration_id = pc.id
        LEFT JOIN organization_custom_field ocf
            ON ocf.id = pc_ocf.custom_field_id
        WHERE p.platform = :platform
            AND (p.meta_id IN(:ids) OR p.username IN(:usernames))
        SQL,
            [
                'platform' => $platform,
                'ids' => $ids,
                'usernames' => $usernames,
                'orgId' => $organizationId,
            ],
            [
                'platform' => PDO::PARAM_STR,
                'ids' => Connection::PARAM_STR_ARRAY,
                'usernames' => Connection::PARAM_STR_ARRAY,
                'orgId' => PDO::PARAM_STR,
            ]
        )->fetchAllAssociative();

        if (empty($rows)) {
            return new StreamedResponse(fn() => print json_encode(['data' => [], 'errors' => []]));
        }

        $__log("Queries done");

        // Organize merged results
        $profileIds = [];
        $customFields = [];
        $orgCustomFields = [];

        foreach ($rows as $row) {
            $id = $row['profile_id'];
            $profileIds[$id] = $id;

            if (!isset($customFields[$id]) && $row['custom_fields']) {
                $_rcfs = json_decode($row['custom_fields'], true) ?? [];;
                $_tcfs = [];

                foreach ($_rcfs as $rcf) {
                    $_tcfs[$rcf['key']] = $rcf['value'];
                }

                $customFields[$id] = $_tcfs;
            }

            if ($row['org_cf_key']) {
                $orgCustomFields[$id][$row['org_cf_key']] = $row['org_cf_value'];
            }
        }

        $__log("DB data transformed, starting s3");

        // Prepare async S3 fetches
        $promises = [];
        foreach (array_unique(array_keys($profileIds)) as $id) {
            $key = sprintf('social/profiles/%s/%s.json', str_replace(':', '/', $platform), $id);
            $promises[$id] = $this->s3->getObjectAsync([
                'Bucket' => $this->bucket,
                'Key' => $key,
            ]);
        }

        // Stream response
        return new StreamedResponse(function () use ($promises, $customFields, $orgCustomFields, $__log) {
            $first = true;
            $results = Promise\Utils::settle($promises)->wait();

            // Disable compression if enabled
            if (function_exists('apache_setenv')) {
                @apache_setenv('no-gzip', '1');
            }

            @ini_set('zlib.output_compression', 'Off');
            @ini_set('implicit_flush', '1');

            // Clear all existing output buffers
            while (ob_get_level() > 0) {
                @ob_end_flush();
            }

            // Force flushing to be active
            @ob_implicit_flush(true);

            $__log("Streaming results");

            echo '[';
            foreach ($results as $id => $res) {
                if ($res['state'] === 'fulfilled') {
                    $body = (string)$res['value']['Body'];
                    $profile = json_decode($body, true);
                    $profile['custom_fields'] =
                        ($customFields[$id] ?? []) +
                        ($orgCustomFields[$id] ?? []);

                    if (!$first) echo ',';
                    echo json_encode($profile, JSON_UNESCAPED_UNICODE);
                    $first = false;

                    if (ob_get_level() > 0) {
                        ob_flush();
                    }
                    flush();
                }

                unset($res, $body, $profile);
            }

            $__log("Finished");
            echo ']';
        }, Response::HTTP_OK, ['Content-Type' => 'application/json']);
    }

    #[Route('/{id:trackingGroup}/profiles', name: 'profiles')]
    public function profiles(TrackingGroup $trackingGroup, Request $request): Response {
        $token = u($request->headers->get('authorization'))->lower()->after('bearer ')->trim()->toString();
        if (!$trackingGroup->getCredential() || $token !== $trackingGroup->getCredential()->getToken()) {
            return $this->json([], Response::HTTP_UNAUTHORIZED);
        }

        $connection = $this->entityManager->getConnection();

        $sql = "
            SELECT
                p.id,
                p.meta_id,
                p.username,
                p.platform,
                p.data,
                p.created_at,
                p.updated_at
            FROM meta_profile p
            WHERE p.platform = :platform 
              AND (p.meta_id IN (:ids) OR p.username IN(:usernames))
        ";

        $this->log('Getting profiles');

        $params = [
            'platform' => $trackingGroup->getDataSource(),
            'ids' => $trackingGroup->getListIds(),
            'usernames' => $trackingGroup->getListForDisplay(),
        ];

        $types = [
            'platform' => \PDO::PARAM_STR,
            'ids' => Connection::PARAM_STR_ARRAY,
            'usernames' => Connection::PARAM_STR_ARRAY,
        ];

        $profiles = $connection->fetchAllAssociative($sql, $params, $types);

        // Decode JSON field 'data' and return as JSON response
        foreach ($profiles as &$profile) {
            $profile['data'] = json_decode($profile['data'], true);
            unset($profile['data']['id']);
            $profile = array_merge($profile, $profile['data']);
            unset($profile['id'], $profile['data'], $profile['created_at'], $profile['updated_at']);

            if ($trackingGroup->getDataSource() === TrackingGroup\DataSource::Facebook->value) {
                if (array_key_exists('likes', $profile)) {
                    $profile['likes_count'] = ($profile['likes']['summary']['total_count'] ?? null);
                    unset($profile['likes']);
                }

                if (array_key_exists('friends', $profile)) {
                    $profile['friends_count'] = ($profile['friends']['summary']['total_count'] ?? null);
                    unset($profile['friends']);
                }

                if (!array_key_exists('fan_count', $profile)) {
                    if (array_key_exists('likes_count', $profile) && array_key_exists('friends_count', $profile)) {
                        $profile['fan_count'] = $profile['likes_count'] + $profile['friends_count'];
                    } else if (array_key_exists('likes_count', $profile)) {
                        $profile['fan_count'] = $profile['likes_count'];
                    } else if (array_key_exists('friends_count', $profile)) {
                        $profile['fan_count'] = $profile['friends_count'];
                    }
                }
            }
        }

        $this->log('Serving profiles');

        return $this->json($profiles);
    }

    #[Route('/{id:trackingGroup}/profiles-history', name: 'profile-history')]
    public function profileHistory(TrackingGroup $trackingGroup, Request $request): Response
    {
        $token = u($request->headers->get('authorization'))->lower()->after('bearer ')->trim()->toString();
        if (!$trackingGroup->getCredential() || $token !== $trackingGroup->getCredential()->getToken()) {
            return $this->json([], Response::HTTP_UNAUTHORIZED);
        }

        $connection = $this->entityManager->getConnection();

        // Base SQL to join history entries to their profile
        $baseSql = "
        SELECT
            h.id,
            p.id            AS profile_id,
            p.meta_id       AS profile_meta_id,
            h.data,
            h.created_at
        FROM meta_profile_history h
        JOIN meta_profile p
          ON h.profile_id = p.id
        WHERE p.platform = :platform
          AND (p.meta_id IN (:ids) OR p.username IN (:usernames))
    ";

        $params = [
            'platform'  => $trackingGroup->getDataSource(),
            'ids'       => $trackingGroup->getListIds(),
            'usernames' => $trackingGroup->getListForDisplay(),
        ];
        $types = [
            'platform'  => \PDO::PARAM_STR,
            'ids'       => Connection::PARAM_STR_ARRAY,
            'usernames' => Connection::PARAM_STR_ARRAY,
        ];

        $this->log('Getting profiles history');

        return new StreamedResponse(function() use ($connection, $baseSql, $params, $types) {
            echo '[';
            $first = true;

            // open transaction + cursor
            $connection->beginTransaction();
            $cursor = 'history_cursor_' . uniqid();
            $connection->executeQuery(
                "DECLARE $cursor CURSOR FOR $baseSql",
                $params,
                $types
            );

            $batchSize = 500;
            while (true) {
                $rows = $connection->fetchAllAssociative("FETCH $batchSize FROM $cursor");
                if (empty($rows)) {
                    break;
                }

                foreach ($rows as $row) {
                    // decode the JSON field
                    $row['data'] = json_decode($row['data'], true);
                    unset($row['data']['id']);
                    $row = array_merge($row, $row['data']);
                    unset($row['data']);

                    if (! $first) {
                        echo ',';
                    }
                    echo json_encode($row, JSON_UNESCAPED_UNICODE);
                    $first = false;
                }

                // flush each batch
                if (ob_get_level() > 0) {
                    ob_flush();
                }
                flush();
            }

            // close JSON array
            echo ']';

            $this->log('Served profiles history');

            // cleanup cursor + commit
            $connection->executeStatement("CLOSE $cursor");
            $connection->commit();
        }, Response::HTTP_OK, ['Content-Type' => 'application/json']);
    }

    #[Route('/{id:trackingGroup}/posts', name: 'posts')]
    public function posts(TrackingGroup $trackingGroup, Request $request): Response {
        $token = u($request->headers->get('authorization'))->lower()->after('bearer ')->trim()->toString();
        if (!$trackingGroup->getCredential() || $token !== $trackingGroup->getCredential()->getToken()) {
            return $this->json([], Response::HTTP_UNAUTHORIZED);
        }

        // Get the DBAL connection from Doctrine.
        $connection = $this->entityManager->getConnection();
        // Disable SQL logger for performance.
        $connection->getConfiguration()->setSQLLogger(null);

        $platform = $trackingGroup->getDataSource();
        $ids = $trackingGroup->getListIds();
        $usernames = $trackingGroup->getListForDisplay();
        $organizationId = $trackingGroup->getOwnerOrganization()->getId()->toRfc4122();

        $connection = $this->entityManager->getConnection();

        // Combined query: profile id, custom_fields, and org custom fields
        $rows = $connection->executeQuery(
            <<<SQL
            SELECT
                p.id AS profile_id
            FROM meta_profile p
            WHERE p.platform = :platform
                AND (p.meta_id IN(:ids) OR p.username IN(:usernames))
            SQL,
            [
                'platform' => $platform,
                'ids' => $ids,
                'usernames' => $usernames,
                'orgId' => $organizationId,
            ],
            [
                'platform' => PDO::PARAM_STR,
                'ids' => Connection::PARAM_STR_ARRAY,
                'usernames' => Connection::PARAM_STR_ARRAY,
                'orgId' => PDO::PARAM_STR,
            ]
        )->fetchAllAssociative();

        if (empty($rows)) {
            return new StreamedResponse(fn() => print json_encode(['data' => [], 'errors' => []]));
        }

        // Organize merged results
        $profileIds = [];

        foreach ($rows as $row) {
            $id = $row['profile_id'];
            $profileIds[$id] = $id;
        }

        $this->log('Fetching posts from S3');

        // Prepare async S3 fetches
        $promises = [];
        foreach (array_unique(array_keys($profileIds)) as $id) {
            $key = sprintf('social/profiles/%s/%s.json', str_replace(':', '/', $platform), $id);
            $promises[$id] = $this->s3->getObjectAsync([
                'Bucket' => $this->bucket,
                'Key' => $key,
            ]);
        }

        $applyFilters = [];
        $filters = $this->entityManager->getRepository(Filter::class)->findBy(['entityId' => $trackingGroup->getId(), 'entityType' => get_class($trackingGroup)]);

        if ($filters) {
            foreach ($filters as $filter) {
                $applyFilters[$filter->getField()] = $filter->getValue();
            }
        }

        $startsAt = $applyFilters['starts_at'] ?? null;
        $endsAt = $applyFilters['ends_at'] ?? null;

        if ($startsAt) {
            $startsAt = strtotime($startsAt);
        }

        if ($endsAt) {
            $endsAt = strtotime($endsAt);
        }

        // Stream response
        return new StreamedResponse(function () use ($promises, $startsAt, $endsAt, $trackingGroup) {
            $first = true;
            $results = Promise\Utils::settle($promises)->wait();

            // Disable compression if enabled
            if (function_exists('apache_setenv')) {
                @apache_setenv('no-gzip', '1');
            }

            @ini_set('zlib.output_compression', 'Off');
            @ini_set('implicit_flush', '1');

            // Clear all existing output buffers
            while (ob_get_level() > 0) {
                @ob_end_flush();
            }

            // Force flushing to be active
            @ob_implicit_flush(true);

            $count = 0;
            echo '[';
            foreach ($results as $id => $res) {
                if ($res['state'] === 'fulfilled') {
                    $body = (string)$res['value']['Body'];
                    $profile = json_decode($body, true);
                    $media = $profile['media'];

                    unset($profile['data']['id']);
                    $profile = array_merge($profile, $profile['data']);

                    if ($trackingGroup->getDataSource() === TrackingGroup\DataSource::Facebook->value) {
                        if (array_key_exists('likes', $profile)) {
                            $profile['likes_count'] = ($profile['likes']['summary']['total_count'] ?? null);
                            unset($profile['likes']);
                        }

                        if (array_key_exists('friends', $profile)) {
                            $profile['friends_count'] = ($profile['friends']['summary']['total_count'] ?? null);
                            unset($profile['friends']);
                        }

                        if (!array_key_exists('fan_count', $profile)) {
                            if (array_key_exists('likes_count', $profile) && array_key_exists('friends_count', $profile)) {
                                $profile['fan_count'] = $profile['likes_count'] + $profile['friends_count'];
                            } else if (array_key_exists('likes_count', $profile)) {
                                $profile['fan_count'] = $profile['likes_count'];
                            } else if (array_key_exists('friends_count', $profile)) {
                                $profile['fan_count'] = $profile['friends_count'];
                            }
                        }
                    }

                    foreach ($media as $mediaItem) {
                        if (array_key_exists('created_time', $mediaItem['data'])) {
                            if ($startsAt && strtotime($mediaItem['data']['created_time']) < $startsAt) {
                                continue;
                            }

                            if ($endsAt && strtotime($mediaItem['data']['created_time']) > $endsAt) {
                                continue;
                            }
                        }

                        unset($mediaItem['comments']);
                        $mediaItem = array_merge($mediaItem, $mediaItem['data']);
                        unset($mediaItem['data']);

                        ReactionsHelper::flattenReactionsCount($mediaItem, true);

                        $result = array_merge($mediaItem, ['profile_id' => $profile['id'], 'profile_meta_id' => $profile['meta_id'], 'profile_username' => $profile['username']]);
                        unset($result['id'], $result['origin'], $result['from'], $result['icon'], $result['is_expired'], $result['is_published'], $result['is_spherical'], $result['privacy'], $result['created_at'], $result['updated_at']);

                        // TODO: Remove later
                        if (array_key_exists('followers_count', $profile)) {
                            $result['followers_count'] = $profile['followers_count'];
                        }

                        if (array_key_exists('fan_count', $profile)) {
                            $result['fan_count'] = $profile['fan_count'];
                        }

                        if (!$first) echo ',';
                        echo json_encode($result, JSON_UNESCAPED_UNICODE);
                        $first = false;

                        if ($count % 250 === 0) {
                            if (ob_get_level() > 0) {
                                ob_flush();
                            }
                            flush();
                        }

                        if ($count % 50000 === 0) {
                            $this->log('Served ' . $count . ' posts');
                        }

                        unset($mediaItem, $result);
                        $count++;
                    }
                }

                unset($res, $body, $profile, $media);
                if (ob_get_level() > 0) {
                    ob_flush();
                }
                flush();
            }

            $this->log('Served ' . $count . ' posts');
            echo ']';
        }, Response::HTTP_OK, ['Content-Type' => 'application/json']);
    }

    #[Route('/{id:trackingGroup}/posts-comments', name: 'posts_comments')]
    public function postsComments(TrackingGroup $trackingGroup, Request $request): Response {
        $token = u($request->headers->get('authorization'))->lower()->after('bearer ')->trim()->toString();
        if (!$trackingGroup->getCredential() || $token !== $trackingGroup->getCredential()->getToken()) {
            return $this->json([], Response::HTTP_UNAUTHORIZED);
        }

        // Get the DBAL connection from Doctrine.
        $connection = $this->entityManager->getConnection();
        // Disable SQL logger for performance.
        $connection->getConfiguration()->setSQLLogger(null);

        $platform = $trackingGroup->getDataSource();
        $ids = $trackingGroup->getListIds();
        $usernames = $trackingGroup->getListForDisplay();
        $organizationId = $trackingGroup->getOwnerOrganization()->getId()->toRfc4122();

        $connection = $this->entityManager->getConnection();

        // Combined query: profile id, custom_fields, and org custom fields
        $rows = $connection->executeQuery(
            <<<SQL
            SELECT
                p.id AS profile_id
            FROM meta_profile p
            WHERE p.platform = :platform
                AND (p.meta_id IN(:ids) OR p.username IN(:usernames))
            SQL,
            [
                'platform' => $platform,
                'ids' => $ids,
                'usernames' => $usernames,
                'orgId' => $organizationId,
            ],
            [
                'platform' => PDO::PARAM_STR,
                'ids' => Connection::PARAM_STR_ARRAY,
                'usernames' => Connection::PARAM_STR_ARRAY,
                'orgId' => PDO::PARAM_STR,
            ]
        )->fetchAllAssociative();

        if (empty($rows)) {
            return new StreamedResponse(fn() => print json_encode(['data' => [], 'errors' => []]));
        }

        // Organize merged results
        $profileIds = [];

        foreach ($rows as $row) {
            $id = $row['profile_id'];
            $profileIds[$id] = $id;
        }

        $this->log('Fetching posts comments from S3');

        // Prepare async S3 fetches
        $promises = [];
        foreach (array_unique(array_keys($profileIds)) as $id) {
            $key = sprintf('social/profiles/%s/%s.json', str_replace(':', '/', $platform), $id);
            $promises[$id] = $this->s3->getObjectAsync([
                'Bucket' => $this->bucket,
                'Key' => $key,
            ]);
        }

        // Stream response
        return new StreamedResponse(function () use ($promises) {
            $first = true;
            $results = Promise\Utils::settle($promises)->wait();

            // Disable compression if enabled
            if (function_exists('apache_setenv')) {
                @apache_setenv('no-gzip', '1');
            }

            @ini_set('zlib.output_compression', 'Off');
            @ini_set('implicit_flush', '1');

            // Clear all existing output buffers
            while (ob_get_level() > 0) {
                @ob_end_flush();
            }

            // Force flushing to be active
            @ob_implicit_flush(true);

            $this->log('Serving posts comments');

            $count = 0;
            echo '[';
            foreach ($results as $id => $res) {
                if ($res['state'] === 'fulfilled') {
                    $body = (string)$res['value']['Body'];
                    $profile = json_decode($body, true);
                    $media = $profile['media'];

                    foreach ($media as $mediaItem) {
                        foreach (($mediaItem['comments'] ?? []) as $comment) {
                            unset($comment['data']['id']);
                            $comment = array_merge($comment, $comment['data']);
                            unset($comment['data']);

                            ReactionsHelper::flattenReactionsCount($comment, true);

                            $result = array_merge($comment, [
                                'media_id' => $mediaItem['id'],
                                'media_meta_id' => $mediaItem['meta_id'],
                                'media_owner_profile_id' => $profile['id'],
                                'media_owner_profile_meta_id' => $profile['meta_id'],
                                'media_owner_profile_username' => $profile['username'],
                            ]);

                            if (!$first) echo ',';
                            echo json_encode($result, JSON_UNESCAPED_UNICODE);
                            $first = false;

                            if ($count % 500 === 0) {
                                if (ob_get_level() > 0) {
                                    ob_flush();
                                }
                                flush();
                            }

                            if ($count % 50000 === 0) {
                                $this->log('Served ' . $count . ' posts comments');
                            }

                            unset($comment, $result);
                            $count++;
                        }

                        unset($mediaItem, $comments);
                    }
                }

                unset($res, $body, $profile, $media);
                if (ob_get_level() > 0) {
                    ob_flush();
                }
                flush();
            }

            $this->log('Served ' . $count . ' posts comments');
            echo ']';
        }, Response::HTTP_OK, ['Content-Type' => 'application/json']);
    }

    private function log(string $msg): void {
        $logDirName = dirname(__DIR__, 4) . '/';
        $__startTime = $this->startTime;
        $__clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $__url = ($_SERVER['REQUEST_SCHEME'] ?? 'https') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];

        $__log = function(string $msg) use (&$__startTime, $__clientIp, $__url, $logDirName) {
            $now = microtime(true);
            $formattedTime = date('Y-m-d H:i:s') . sprintf('.%03d', ($now - floor($now)) * 1000);
            $elapsed = round(($now - $__startTime) * 1000); // in ms
            $logLine = "[$formattedTime] +{$elapsed}ms [IP: {$__clientIp}] [URL: {$__url}]: $msg\n\n";
            file_put_contents($logDirName . '/runtime.log', $logLine, FILE_APPEND);
        };
        $__log($msg);
    }
}
