<?php

declare(strict_types=1);

namespace App\Controller\API\Social;

use App\Entity\Social\TrackingGroup;
use Aws\Exception\AwsException;
use Aws\S3\S3Client;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\EntityManagerInterface;
use Exception;
use GuzzleHttp\Promise;
use PDO;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\Routing\Attribute\Route;

#[Route('/api/social/test')]
class TestController extends AbstractController
{
    public function __construct(
        private readonly EntityManagerInterface $entityManager,
        private readonly S3Client $s3,
        private readonly LoggerInterface $logger,
        private readonly string $bucket
    ) {
        @ini_set('memory_limit', '2G');
    }

    #[Route('/{id:tg}', methods: ['GET'])]
    public function index(TrackingGroup $tg, Request $request): Response
    {
        if ($request->headers->get('authorization') !== 'Bearer #M@gT01S3') {
            return new Response(status: Response::HTTP_UNAUTHORIZED);
        }

        $platform = $tg->getDataSource();
        $ids = $tg->getListIds();
        $usernames = $tg->getListForDisplay();
        $organizationId = $tg->getOwnerOrganization()->getId()->toRfc4122();

        $connection = $this->entityManager->getConnection();

        // Start S3 timing
        $s3Start = microtime(true);

        // Combined query: profile id, custom_fields, and org custom fields
        $rows = $connection->executeQuery(
            <<<SQL
        SELECT
            p.id AS profile_id,
            pc.custom_fields,
            ocf.key AS org_cf_key,
            ocf.value AS org_cf_value
        FROM meta_profile p
        LEFT JOIN meta_profile_configuration pc
            ON pc.profile_id = p.id AND pc.organization_id = :orgId
        LEFT JOIN meta_profile_configuration_organization_custom_field pc_ocf
            ON pc_ocf.configuration_id = pc.id
        LEFT JOIN organization_custom_field ocf
            ON ocf.id = pc_ocf.custom_field_id
        WHERE p.platform = :platform
            AND (p.meta_id IN(:ids) OR p.username IN(:usernames))
        SQL,
            [
                'platform' => $platform,
                'ids' => $ids,
                'usernames' => $usernames,
                'orgId' => $organizationId,
            ],
            [
                'platform' => PDO::PARAM_STR,
                'ids' => Connection::PARAM_STR_ARRAY,
                'usernames' => Connection::PARAM_STR_ARRAY,
                'orgId' => PDO::PARAM_STR,
            ]
        )->fetchAllAssociative();

        if (empty($rows)) {
            return new StreamedResponse(fn() => print json_encode(['data' => [], 'errors' => []]));
        }

        // Organize merged results
        $profileIds = [];
        $customFields = [];
        $orgCustomFields = [];

        foreach ($rows as $row) {
            $id = $row['profile_id'];
            $profileIds[$id] = $id;

            if (!isset($customFields[$id]) && $row['custom_fields']) {
                $_rcfs = json_decode($row['custom_fields'], true) ?? [];;
                $_tcfs = [];

                foreach ($_rcfs as $rcf) {
                    $_tcfs[$rcf['key']] = $rcf['value'];
                }

                $customFields[$id] = $_tcfs;
            }

            if ($row['org_cf_key']) {
                $orgCustomFields[$id][$row['org_cf_key']] = $row['org_cf_value'];
            }
        }

        // Prepare async S3 fetches
        $promises = [];
        foreach (array_unique(array_keys($profileIds)) as $id) {
            $key = sprintf('social/profiles/%s/%s.json', str_replace(':', '/', $platform), $id);
            $promises[$id] = $this->s3->getObjectAsync([
                'Bucket' => $this->bucket,
                'Key' => $key,
            ]);
        }

        // Stream response
        return new StreamedResponse(function () use ($promises, $customFields, $orgCustomFields, $s3Start) {
            $first = true;

            $results = Promise\Utils::settle($promises)->wait();
            $s3TotalTime = microtime(true) - $s3Start;

            $this->logger->info(sprintf("⏱️ Total S3 fetch time: %.2f seconds", $s3TotalTime));

            $responseStart = microtime(true);

            // Disable compression if enabled
            if (function_exists('apache_setenv')) {
                @apache_setenv('no-gzip', '1');
            }

            @ini_set('zlib.output_compression', 'Off');
            @ini_set('implicit_flush', '1');

            // Clear all existing output buffers
            while (ob_get_level() > 0) {
                @ob_end_flush();
            }

            // Force flushing to be active
            @ob_implicit_flush(true);

            echo '[';
            foreach ($results as $id => $res) {
                $t0 = microtime(true);

                if ($res['state'] === 'fulfilled') {
                    $body = (string)$res['value']['Body'];
                    $profile = json_decode($body, true);
                    $profile['custom_fields'] =
                        ($customFields[$id] ?? []) +
                        ($orgCustomFields[$id] ?? []);

                    if (!$first) echo ',';
                    echo json_encode($profile, JSON_UNESCAPED_UNICODE);
                    $first = false;

                    if (ob_get_level() > 0) {
                        ob_flush();
                    }
                    flush();
                }

                $this->logger->info("Processed profile $id in " . (microtime(true) - $t0) . 's');
                unset($res, $body, $profile);
            }

            echo ']';
            $this->logger->info('Total stream time: ' . (microtime(true) - $responseStart) . 's');
        }, Response::HTTP_OK, ['Content-Type' => 'application/json']);
    }
}
