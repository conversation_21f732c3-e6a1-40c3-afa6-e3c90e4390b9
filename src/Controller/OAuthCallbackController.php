<?php

namespace App\Controller;

use App\Service\MetaService;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;

final class OAuthCallbackController extends AbstractController
{
    #[Route('/oauth/callback', name: 'oauth_callback', methods: ['GET'])]
    public function callback(Request $request, MetaService $meta): Response
    {
        return new Response("Code: <strong>" . $request->query->get('code') . "</strong>");
    }
}
