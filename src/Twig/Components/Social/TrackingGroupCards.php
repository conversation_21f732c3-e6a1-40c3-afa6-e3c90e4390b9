<?php
namespace App\Twig\Components\Social;

use App\Entity\Social\TrackingGroup;
use App\Repository\Social\TrackingGroupRepository;
use App\Service\WorkspaceService;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\Attribute\LiveAction;
use Symfony\UX\LiveComponent\Attribute\LiveProp;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent]
class TrackingGroupCards
{
    use DefaultActionTrait;

    #[LiveProp(writable: true)]
    public string $filter = 'all';

    public function __construct(
        private readonly Security $security,
        private readonly TrackingGroupRepository $trackingGroupRepository,
        private readonly RequestStack $requestStack,
        private readonly WorkspaceService $workspaceService,
    ) {}

    /**
     * @return TrackingGroup[]
     */
    #[LiveAction]
    public function changeFilter(string $filter): void
    {
        $this->filter = $filter;
    }

    public function getList(): array {
        if (!$this->security->isGranted('ROLE_USER')) {
            return [];
        }

        $organization = $this->workspaceService->getCurrentOrganization();
        $groups = $this->trackingGroupRepository->findBy(['ownerOrganization' => $organization], ['createdAt' => 'DESC']);

        if ($this->filter === 'all') {
            return $groups;
        }

        return array_filter($groups, function(TrackingGroup $group) {
            $status = $group->getStatus()->getStatus();

            return match($this->filter) {
                'active' => in_array($status->value, ['processing', 'queued', 'update_requested']),
                'completed' => in_array($status->value, ['finished', 'waiting_next_update']),
                'paused' => in_array($status->value, ['paused', 'pause_requested']),
                default => true,
            };
        });
    }
}
