<?php
namespace App\Twig\Components\Social;

use App\Entity\Social\TrackingGroup;
use App\Repository\Social\TrackingGroupRepository;
use App\Service\WorkspaceService;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\UX\LiveComponent\Attribute\AsLiveComponent;
use Symfony\UX\LiveComponent\DefaultActionTrait;

#[AsLiveComponent]
class TrackingGroupList
{
    use DefaultActionTrait;

    public function __construct(
        private readonly Security $security,
        private readonly TrackingGroupRepository $trackingGroupRepository,
        private readonly RequestStack $requestStack,
        private readonly WorkspaceService $workspaceService,
    ) {}

    /**
     * @return TrackingGroup[]
     */
    public function getList(): array {
        if (!$this->security->isGranted('ROLE_USER')) {
            return [];
        }

        $organization = $this->workspaceService->getCurrentOrganization();
        return $this->trackingGroupRepository->findBy(['ownerOrganization' => $organization], ['createdAt' => 'ASC']);
    }
}