<?php
namespace App\Twig\Components\Social;

use App\Entity\Social\TrackingGroup;
use App\Repository\Meta\ProfileRepository;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent]
class TrackingGroupStats
{
    public TrackingGroup $trackingGroup;
    
    public function __construct(
        private readonly ProfileRepository $profileRepository
    ) {}
    
    public function getProfileCount(): int
    {
        return count($this->trackingGroup->getList());
    }
    
    public function getCompletedCount(): int
    {
        return $this->trackingGroup->getStatus()->getProgress();
    }
    
    public function getPercentage(): int
    {
        $total = $this->trackingGroup->getStatus()->getTotalProfiles();
        if ($total <= 0) {
            return 0;
        }
        
        return (int) (($this->trackingGroup->getStatus()->getProgress() / $total) * 100);
    }
    
    public function getLastUpdated(): ?\DateTimeImmutable
    {
        return $this->trackingGroup->getStatus()->getLastRunAt();
    }
}
