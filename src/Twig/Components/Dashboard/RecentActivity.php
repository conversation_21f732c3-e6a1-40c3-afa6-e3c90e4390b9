<?php
namespace App\Twig\Components\Dashboard;

use App\Entity\Social\TrackingHistory;
use App\Repository\Social\TrackingHistoryRepository;
use App\Service\WorkspaceService;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent]
class RecentActivity
{
    public function __construct(
        private readonly Security $security,
        private readonly TrackingHistoryRepository $trackingHistoryRepository,
        private readonly WorkspaceService $workspaceService,
    ) {}

    /**
     * @return TrackingHistory[]
     */
    public function getRecentActivity(): array
    {
        if (!$this->security->isGranted('ROLE_USER')) {
            return [];
        }

        $organization = $this->workspaceService->getCurrentOrganization();
        return $this->trackingHistoryRepository->findRecentByOrganization($organization, 5);
    }
}
