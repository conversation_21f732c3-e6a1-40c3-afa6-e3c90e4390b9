<?php
namespace App\Twig\Components\Dashboard;

use App\Entity\Social\TrackingGroup\Status;
use App\Repository\Social\TrackingGroupRepository;
use App\Repository\Social\TrackingHistoryRepository;
use App\Service\WorkspaceService;
use DateInterval;
use DateTimeImmutable;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent]
class ActivityChart
{
    public function __construct(
        private readonly Security $security,
        private readonly TrackingGroupRepository $trackingGroupRepository,
        private readonly TrackingHistoryRepository $trackingHistoryRepository,
        private readonly WorkspaceService $workspaceService,
    ) {}

    public function getChartData(): array
    {
        if (!$this->security->isGranted('ROLE_USER')) {
            return [
                'labels' => [],
                'datasets' => []
            ];
        }

        $organization = $this->workspaceService->getCurrentOrganization();
        $groups = $this->trackingGroupRepository->findBy(['ownerOrganization' => $organization]);
        
        // Get last 7 days
        $days = [];
        $today = new DateTimeImmutable();
        for ($i = 6; $i >= 0; $i--) {
            $date = $today->sub(new DateInterval("P{$i}D"));
            $days[] = $date->format('Y-m-d');
        }
        
        // Prepare data structure
        $data = [
            'labels' => array_map(function($day) {
                return (new DateTimeImmutable($day))->format('M d');
            }, $days),
            'active' => array_fill(0, 7, 0),
            'completed' => array_fill(0, 7, 0),
            'total' => array_fill(0, 7, 0)
        ];
        
        // Count groups by status for each day
        foreach ($groups as $group) {
            $createdAt = $group->getCreatedAt()->format('Y-m-d');
            $status = $group->getStatus()->getStatus();
            
            for ($i = 0; $i < count($days); $i++) {
                $day = $days[$i];
                
                // If group was created on or before this day
                if ($createdAt <= $day) {
                    $data['total'][$i]++;
                    
                    if ($status === Status::Finished || $status === Status::WaitingNextUpdate) {
                        $data['completed'][$i]++;
                    } elseif ($status === Status::Processing || $status === Status::Queued || $status === Status::UpdateRequested) {
                        $data['active'][$i]++;
                    }
                }
            }
        }
        
        return $data;
    }
}
