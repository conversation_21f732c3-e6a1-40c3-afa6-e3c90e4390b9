<?php
namespace App\Twig\Components\Dashboard;

use App\Entity\Social\TrackingGroup\Status;
use App\Repository\Social\TrackingGroupRepository;
use App\Service\WorkspaceService;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent]
class StatsSummary
{
    public function __construct(
        private readonly Security $security,
        private readonly TrackingGroupRepository $trackingGroupRepository,
        private readonly WorkspaceService $workspaceService,
    ) {}

    public function getTotalGroups(): int
    {
        if (!$this->security->isGranted('ROLE_USER')) {
            return 0;
        }

        $organization = $this->workspaceService->getCurrentOrganization();
        return count($this->trackingGroupRepository->findBy(['ownerOrganization' => $organization]));
    }

    public function getActiveGroups(): int
    {
        if (!$this->security->isGranted('ROLE_USER')) {
            return 0;
        }

        $organization = $this->workspaceService->getCurrentOrganization();
        $groups = $this->trackingGroupRepository->findBy(['ownerOrganization' => $organization]);
        
        $activeCount = 0;
        foreach ($groups as $group) {
            $status = $group->getStatus()->getStatus();
            if ($status === Status::Processing || $status === Status::Queued || $status === Status::UpdateRequested) {
                $activeCount++;
            }
        }
        
        return $activeCount;
    }

    public function getCompletedGroups(): int
    {
        if (!$this->security->isGranted('ROLE_USER')) {
            return 0;
        }

        $organization = $this->workspaceService->getCurrentOrganization();
        $groups = $this->trackingGroupRepository->findBy(['ownerOrganization' => $organization]);
        
        $completedCount = 0;
        foreach ($groups as $group) {
            $status = $group->getStatus()->getStatus();
            if ($status === Status::Finished || $status === Status::WaitingNextUpdate) {
                $completedCount++;
            }
        }
        
        return $completedCount;
    }
}
