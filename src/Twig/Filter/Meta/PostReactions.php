<?php
namespace App\Twig\Filter\Meta;

use App\Entity\Meta\Media;
use App\Entity\Social\TrackingGroup\DataSource;
use App\Helper\Social\Meta\ReactionsHelper;
use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use function Symfony\Component\String\u;

class PostReactions extends AbstractExtension
{
    public function getFilters(): array
    {
        return [
            new TwigFilter('meta_post_reactions_count', [$this, 'processReactionsCount'], ['is_safe' => ['html']]),
        ];
    }

    public function processReactionsCount(mixed $input = null): string
    {
        $data = [];

        if ($input instanceof Media) {
            $data = $input->getData();
        }

        if (is_array($input)) {
            $data = $input;

            if (array_key_exists('data', $data)) {
                $data = $data['data'];
            }
        }

        if (array_key_exists('like_count', $data)) {
            return $data['like_count'];
        } else {
            $flatData = ReactionsHelper::flattenReactionsCount($data);
            $total = 0;

            foreach (ReactionsHelper::getFacebookReactionsKeys() as $facebookReactionsKey) {
                $total += $flatData['reactions_' . $facebookReactionsKey . '_count'] ?? 0;
            }

            return $total;
        }
    }
}