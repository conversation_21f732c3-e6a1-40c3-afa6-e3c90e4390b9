<?php
namespace App\Twig\Filter\Meta;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use function Symfony\Component\String\u;

class ProfileBio extends AbstractExtension
{
    public function getFilters(): array
    {
        return [
            new TwigFilter('meta_profile_bio', [$this, 'processBio'], ['is_safe' => ['html']]),
        ];
    }

    public function processBio(?string $text, string $platform): string
    {
        $platform = u($platform)->lower()->containsAny('instagram') ? 'instagram' : 'facebook';

        $processed = preg_replace(
            '/@([\w.]+)/',
            '<a href="https://' . $platform . '.com/$1" target="_blank" rel="noreferrer noopener nofollow">@$1</a>',
            $text
        );

        return nl2br($processed);
    }
}