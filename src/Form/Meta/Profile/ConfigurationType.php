<?php

namespace App\Form\Meta\Profile;

use App\Entity\Meta\Profile;
use App\Entity\Meta\Profile\Configuration;
use App\Entity\Organization;
use App\Form\KeyValueType;
use App\Form\Organization\CustomFieldType;
use <PERSON><PERSON>nay\Bundle\UxCollectionBundle\Form\UxCollectionType;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class ConfigurationType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('customFields', UxCollectionType::class, [
                'entry_type' => KeyValueType::class,
                'label' => 'Custom Fields',
                'allow_add' => true,
                'allow_delete' => true,
                'display_sort_buttons' => false,
                'min' => 0,
                'required' => false
            ])
            ->add('organizationCustomFields', UxCollectionType::class, [
                'entry_type' => CustomFieldType::class,
                'label' => 'Static Custom Fields',
                'allow_add' => true,
                'allow_delete' => true,
                'display_sort_buttons' => false,
                'min' => 0,
                'required' => false,
                'by_reference' => false
            ])
            ->add('submit', SubmitType::class, [
                'label' => 'Submit',
                'row_attr' => [
                    'class' => 'd-flex w-100 justify-content-end'
                ]
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Configuration::class,
        ]);
    }
}
