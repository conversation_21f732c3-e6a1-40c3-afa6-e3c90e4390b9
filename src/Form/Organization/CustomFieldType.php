<?php

namespace App\Form\Organization;

use App\Entity\Organization;
use App\Entity\Organization\CustomField;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\HiddenType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CustomFieldType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $disabled = !$options['allow_edit'];
        $attr = $disabled ? ['readonly' => 'readonly'] : [];

        $builder
            ->add('key', TextType::class, [
                'row_attr' => [
                    'class' => 'col-6 pe-2'
                ],
                'disabled' => $disabled,
                'attr' => $attr,
                'required' => true
            ])
            ->add('value', TextType::class, [
                'row_attr' => [
                    'class' => 'col-6'
                ],
                'disabled' => $disabled,
                'attr' => $attr,
                'required' => true
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => CustomField::class,
            'allow_edit' => false
        ]);
    }
}
