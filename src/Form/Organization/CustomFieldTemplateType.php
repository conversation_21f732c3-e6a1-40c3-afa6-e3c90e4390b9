<?php

namespace App\Form\Organization;

use App\Entity\Organization;
use App\Entity\Organization\CustomFieldTemplate;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class CustomFieldTemplateType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('key', TextType::class, [
                'row_attr' => [
                    'class' => 'col-6 pe-2'
                ]
            ])
            ->add('value', TextType::class, [
                'label' => 'Default Value (optional)',
                'row_attr' => [
                    'class' => 'col-6'
                ],
                'required' => false
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => CustomFieldTemplate::class,
        ]);
    }
}
