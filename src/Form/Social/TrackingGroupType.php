<?php
namespace App\Form\Social;

use App\Entity\Social\TrackingGroup;
use App\Entity\Social\TrackingGroup\DataSource;
use App\Form\KeyValueType;
use Arkounay\Bundle\UxCollectionBundle\Form\UxCollectionType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\Extension\Core\Type\CollectionType;
use Symfony\Component\Form\Extension\Core\Type\NumberType;
use Symfony\Component\Form\Extension\Core\Type\TextareaType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class TrackingGroupType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('name', TextType::class, [
                'label' => 'Name' . $options['edit'] ? '' : ' (optional)',
                'attr' => [
                    'placeholder' => 'Tracking group name',
                ],
                'required' => $options['edit']
            ])
        ;

        if (!$options['edit']) {
            $builder
                ->add('dataSource', ChoiceType::class, [
                    'label' => 'Data Source',
                    'choices' => [
                        'Facebook' => DataSource::Facebook->value,
                        'Instagram' => DataSource::Instagram->value
                    ],
                    'attr' => [
                        'class' => 'form-selectgroup-input'
                    ],
                    'expanded' => true,
                    'multiple' => false
                ])
                ->add('listType', ChoiceType::class, [
                    'label' => 'List Type',
                    'choices' => [
                        'Pages' => 'meta:pages',
                        'Profiles' => 'meta:profiles',
                        'Groups' => 'meta:groups',
                    ],
                    'data' => 'meta:profiles',
                    'required' => true
                ])
            ;
        }

        $builder
            ->add('category', ChoiceType::class, [
                'label' => 'Category',
                'choices' => [
                    'General' => 'general',
                    'Media' => 'media',
                    'Politics' => 'politics'
                ],
                'empty_data' => 'general',
            ])
            ->add('subCategory', TextType::class, [
                'label' => 'Sub Category',
                'required' => false
            ])
            ->add('list', TextAreaType::class, [
                'label' => 'Profiles',
                'mapped' => false,
                'required' => false
            ])
            ->add('maximumPostsPerProfile', NumberType::class, [
                'label' => 'Maximum posts per profile',
            ])
            ->add('maximumCommentsPerPost', NumberType::class, [
                'label' => 'Maximum comments per post',
                'empty_data' => 0
            ])
            ->add('runProfileAIAnalysis', CheckboxType::class, [
                'label' => 'Run Profile AI analysis',
                'required' => false
            ])
            ->add('keepDataUpdated', CheckboxType::class, [
                'label' => 'Keep data updated after first run',
                'required' => false
            ])
            ->add('customFields', UxCollectionType::class, [
                'entry_type' => KeyValueType::class,
                'label' => 'Custom Fields',
                'allow_add' => true,
                'allow_delete' => true,
                'display_sort_buttons' => false,
                'min' => 0,
                'required' => false
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => TrackingGroup::class,
            'allow_extra_fields' => true,
            'edit' => false,
        ]);
    }
}
