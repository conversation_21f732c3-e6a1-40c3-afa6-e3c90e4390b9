"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { FaImage, FaPalette, FaFileAlt, FaShareAlt, FaRobot } from 'react-icons/fa';

const AIFeatures: React.FC = () => {
  const features = [
    {
      icon: <FaImage className="text-4xl text-red-600" />,
      title: "Photo",
      description: "Use the product photo from your supplier or your personal gallery. The system automatically optimizes the quality and resolution of the photo for better display on social media."
    },
    {
      icon: <FaPalette className="text-4xl text-red-600" />,
      title: "Design",
      description: "Generates marketing designs based on current trends and market analysis. AI selects colors, fonts, and graphic elements that attract more attention from your target audience."
    },
    {
      icon: <FaFileAlt className="text-4xl text-red-600" />,
      title: "Description",
      description: "Creates engaging and persuasive text in the language of the market you are targeting. The text includes SEO-optimized keywords and effective calls to action that increase engagement and conversions."
    },
    {
      icon: <FaShareAlt className="text-4xl text-red-600" />,
      title: "Post",
      description: "Ready to publish on all social networks like Facebook, Instagram, TikTok, and more. You can schedule posts for maximum activity times and get detailed statistics on performance."
    }
  ];

  return (
    <section id="ai-features" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">AI-Generated Social Media Posts</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our platform uses artificial intelligence to automatically generate professional and effective social media posts. 
            The process is simple and fast, saving you valuable time.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100"
            >
              <div className="mb-4">{feature.icon}</div>
              <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="mt-16 bg-gray-50 p-8 rounded-xl"
        >
          <div className="flex flex-col md:flex-row items-center gap-8">
            <div className="md:w-1/3 flex justify-center">
              <div className="relative">
                <div className="absolute inset-0 bg-red-200 rounded-full blur-xl opacity-50"></div>
                <div className="relative bg-white p-6 rounded-full shadow-xl">
                  <FaRobot className="text-6xl text-red-600" />
                </div>
              </div>
            </div>
            <div className="md:w-2/3">
              <h3 className="text-2xl font-bold mb-4">Magnora's Artificial Intelligence Assistant</h3>
              <p className="text-gray-600 mb-4">
                An integrated AI assistant that analyzes product and competitive data, providing deep and summarized insights into your market performance.
              </p>
              <ul className="space-y-2">
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  <span>Continuously monitors sales trends, customer behavior, and competitor strategies</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  <span>Features a dynamic dashboard with performance reports and improvement suggestions</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  <span>Helps you make informed decisions by forecasting future trends</span>
                </li>
                <li className="flex items-start">
                  <span className="text-red-600 mr-2">•</span>
                  <span>Provides proactive alerts on significant market changes</span>
                </li>
              </ul>
            </div>
          </div>
        </motion.div>

        <div className="text-center mt-12">
          <p className="text-lg text-gray-700 max-w-3xl mx-auto mb-8">
            With our advanced AI system, every product gets a professional presentation without the need for designers or marketing specialists. 
            This saves you valuable time and resources while simultaneously increasing the effectiveness of your digital marketing.
          </p>
        </div>
      </div>
    </section>
  );
};

export default AIFeatures;
