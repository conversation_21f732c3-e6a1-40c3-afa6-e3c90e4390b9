"use client";

import React, { useState } from 'react';
import { FaGlobe, FaChevronDown } from 'react-icons/fa';

// Available languages
const locales = ['en', 'sq', 'tr'];

interface LanguageSelectorProps {
  translations: {
    label: string;
    [key: string]: string;
  };
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({ translations }) => {
  const [currentLocale, setCurrentLocale] = useState('en');
  const [isOpen, setIsOpen] = useState(false);

  const handleLanguageChange = (locale: string) => {
    setCurrentLocale(locale);
    setIsOpen(false);
    // In a real app, this would also change the app's language context
    console.log(`Language changed to: ${locale}`);
  };

  return (
    <div className="relative">
      <button
        className="flex items-center space-x-1 text-sm font-medium"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <FaGlobe className="mr-1" />
        <span>{translations[currentLocale]}</span>
        <FaChevronDown className={`transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
          <div className="py-1" role="menu" aria-orientation="vertical">
            {locales.map((locale) => (
              <button
                key={locale}
                className={`block w-full text-left px-4 py-2 text-sm ${
                  locale === currentLocale ? 'bg-gray-100 text-gray-900' : 'text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => handleLanguageChange(locale)}
                role="menuitem"
              >
                {translations[locale]}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
