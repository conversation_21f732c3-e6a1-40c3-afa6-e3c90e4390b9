"use client";

import React from 'react';
import Link from 'next/link';

interface LogoProps {
  variant?: 'default' | 'white';
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ variant = 'default', className = '' }) => {
  const textColor = variant === 'white' ? 'text-white' : 'text-red-600';

  return (
    <Link href="/" className={`flex items-center font-bold text-2xl ${textColor} ${className}`}>
      <span className="mr-2 bg-red-600 text-white px-2 py-1 rounded">Post</span>
      <span>CHAT</span>
    </Link>
  );
};

export default Logo;
