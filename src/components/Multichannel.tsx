"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { FaShareAlt, FaComments, FaRobot, FaShoppingCart } from 'react-icons/fa';
import Image from 'next/image';
import { useLanguage } from '@/context/LanguageContext';

const Multichannel: React.FC = () => {
  const { locale, translations } = useLanguage();
  const steps = [
    {
      icon: <FaShareAlt className="text-4xl text-red-600" />,
      title: "Publication",
      description: "The product is published on your social media pages and e-commerce platform. All product details, prices, and options are automatically synchronized."
    },
    {
      icon: <FaComments className="text-4xl text-red-600" />,
      title: "Interaction",
      description: "The customer comments or sends a message with questions about the product. Our system integrates all communications from different platforms into a single interface for ease of use."
    },
    {
      icon: <FaRobot className="text-4xl text-red-600" />,
      title: "Automated Response",
      description: "The PostCHAT bot provides detailed information about the product, availability, prices, and shipping policies. It can respond in multiple languages and handle complex questions without human intervention."
    },
    {
      icon: <FaShoppingCart className="text-4xl text-red-600" />,
      title: "Order",
      description: "The link is sent, and the payment is processed securely. The customer receives an immediate confirmation, and the internal systems are automatically notified to start the order preparation process."
    }
  ];

  return (
    <section id="multichannel" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">{translations[locale].multichannelTitle}</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {translations[locale].multichannelSubtitle}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
          <div>
            <div className="relative">
              <div className="absolute inset-0 bg-red-100 rounded-xl blur-xl opacity-50"></div>
              <div className="relative bg-white p-6 rounded-xl shadow-xl overflow-hidden">
                <div className="flex justify-between items-center mb-4 border-b pb-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-red-600 flex items-center justify-center text-white">
                      PC
                    </div>
                    <span className="ml-2 font-bold">PostCHAT</span>
                  </div>
                  <div className="text-sm text-gray-500">Multichannel Platform</div>
                </div>

                <div className="space-y-4 mb-6">
                  {/* Order Status Inquiry */}
                  <div className="flex items-start">
                    <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs mr-2 flex-shrink-0">
                      FB
                    </div>
                    <div className="bg-blue-50 p-3 rounded-lg rounded-tl-none">
                      <p className="text-sm font-medium text-gray-700">Sarah Johnson</p>
                      <p className="text-sm">Hi! I ordered a smartwatch yesterday (order #PC12345). Can you tell me when it will be shipped?</p>
                      <p className="text-xs text-gray-500 mt-1">10:23 AM · Facebook Messenger</p>
                    </div>
                  </div>

                  {/* AI Response */}
                  <div className="flex justify-end">
                    <div className="bg-red-50 p-3 rounded-lg rounded-tr-none max-w-xs">
                      <p className="text-sm font-medium text-red-600">PostCHAT AI</p>
                      <p className="text-sm">Hello Sarah! I've checked your order #PC12345 for the smartwatch. It's currently being processed and will be shipped within 24 hours. You'll receive a tracking number as soon as it ships.</p>
                      <p className="text-xs text-gray-500 mt-1">10:24 AM · Automated Response</p>
                    </div>
                  </div>

                  {/* Address Update Request */}
                  <div className="flex items-start">
                    <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center text-white text-xs mr-2 flex-shrink-0">
                      WA
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg rounded-tl-none">
                      <p className="text-sm font-medium text-gray-700">Michael Chen</p>
                      <p className="text-sm">I need to change my delivery address for order #PC54321. Is that still possible?</p>
                      <p className="text-xs text-gray-500 mt-1">11:05 AM · WhatsApp</p>
                    </div>
                  </div>

                  {/* AI Response */}
                  <div className="flex justify-end">
                    <div className="bg-red-50 p-3 rounded-lg rounded-tr-none max-w-xs">
                      <p className="text-sm font-medium text-red-600">PostCHAT AI</p>
                      <p className="text-sm">Hi Michael! Yes, you can still update your address as your order hasn't been shipped yet. Please provide the new delivery address, and I'll update it immediately.</p>
                      <p className="text-xs text-gray-500 mt-1">11:06 AM · Automated Response</p>
                    </div>
                  </div>

                  {/* Product Inquiry */}
                  <div className="flex items-start">
                    <div className="w-8 h-8 rounded-full bg-pink-500 flex items-center justify-center text-white text-xs mr-2 flex-shrink-0">
                      IG
                    </div>
                    <div className="bg-pink-50 p-3 rounded-lg rounded-tl-none">
                      <p className="text-sm font-medium text-gray-700">Elena Rodriguez</p>
                      <p className="text-sm">Does the wireless headphone come with a charging case? And what's the battery life?</p>
                      <p className="text-xs text-gray-500 mt-1">11:42 AM · Instagram DM</p>
                    </div>
                  </div>

                  {/* AI Response with Product Details */}
                  <div className="flex justify-end">
                    <div className="bg-red-50 p-3 rounded-lg rounded-tr-none max-w-xs">
                      <p className="text-sm font-medium text-red-600">PostCHAT AI</p>
                      <p className="text-sm">Hello Elena! Yes, our wireless headphones come with a compact charging case. The headphones have 6 hours of battery life, and the case provides an additional 24 hours. Would you like to see our color options?</p>
                      <p className="text-xs text-gray-500 mt-1">11:43 AM · Automated Response</p>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium">All channels in one place</div>
                    <div className="flex space-x-2">
                      <div className="w-6 h-6 rounded-full bg-blue-500"></div>
                      <div className="w-6 h-6 rounded-full bg-pink-500"></div>
                      <div className="w-6 h-6 rounded-full bg-green-500"></div>
                      <div className="w-6 h-6 rounded-full bg-yellow-500"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-8">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="flex items-start"
              >
                <div className="bg-white p-3 rounded-full shadow-md mr-4">
                  {step.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold mb-2">{step.title}</h3>
                  <p className="text-gray-600">{step.description}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <div className="text-center mt-16">
          <p className="text-lg text-gray-700 max-w-3xl mx-auto mb-6">
            This optimized process saves time, reduces operational costs, and significantly improves the customer experience,
            thereby increasing the likelihood of repeat purchases and positive recommendations.
          </p>
          <div className="flex flex-wrap justify-center gap-4 mt-8">
            <div className="bg-white p-4 rounded-lg shadow-md border border-gray-100 max-w-xs">
              <h3 className="font-bold text-lg mb-2 text-red-600">{translations[locale].forEcommerce}</h3>
              <p className="text-gray-600">{translations[locale].forEcommerceDesc}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-md border border-gray-100 max-w-xs">
              <h3 className="font-bold text-lg mb-2 text-red-600">{translations[locale].forCarriers}</h3>
              <p className="text-gray-600">{translations[locale].forCarriersDesc}</p>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-md border border-gray-100 max-w-xs">
              <h3 className="font-bold text-lg mb-2 text-red-600">{translations[locale].forServices}</h3>
              <p className="text-gray-600">{translations[locale].forServicesDesc}</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Multichannel;
