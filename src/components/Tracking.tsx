"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { FaMapMarkerAlt, FaBell, FaBox, FaCheckCircle, FaTruck, FaShoppingCart, FaShip } from 'react-icons/fa';
import { useLanguage } from '@/context/LanguageContext';

const Tracking: React.FC = () => {
  const features = [
    {
      icon: <FaMapMarkerAlt className="text-4xl text-red-600" />,
      title: "Tracking",
      description: "Customers can see the real-time location of their order through our dedicated app. Our platform provides detailed information on location, expected arrival time, and current delivery status."
    },
    {
      icon: <FaBell className="text-4xl text-red-600" />,
      title: "Notification",
      description: "The system automatically sends a unique delivery code message as soon as the order is ready for delivery. Notifications are sent via SMS and email, including information on the expected date and time of delivery."
    },
    {
      icon: <FaBox className="text-4xl text-red-600" />,
      title: "Delivery",
      description: "The courier delivers the package and records the secure verification code. This process ensures delivery security and eliminates the possibility of fraud, ensuring the product is delivered to the right customer."
    },
    {
      icon: <FaCheckCircle className="text-4xl text-red-600" />,
      title: "Confirmation",
      description: "After delivery, the customer has the opportunity to confirm receipt of the order and provide feedback. This data is used to continuously improve our service quality and address any potential concerns."
    }
  ];

  const logisticsSteps = [
    {
      icon: <FaShoppingCart className="text-3xl text-white" />,
      title: "Order",
      description: "Automatically sent to the supplier through our integrated system. All product details, quantities, and delivery addresses are transferred without manual intervention."
    },
    {
      icon: <FaBox className="text-3xl text-white" />,
      title: "Preparation",
      description: "The supplier marks \"Ready\" on the platform after verifying availability and preparing the products. This includes quality checking and secure packaging for transport."
    },
    {
      icon: <FaShip className="text-3xl text-white" />,
      title: "Export",
      description: "Shipments are prepared for export, including all necessary customs documentation. Products are grouped according to destination to optimize transportation costs and delivery times."
    },
    {
      icon: <FaBell className="text-3xl text-white" />,
      title: "Notification",
      description: "The customs agency and post office are informed about upcoming shipments. Clients receive a notification with the tracking number and estimated time of arrival for their order."
    }
  ];

  return (
    <section id="tracking" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Tracking and Customer Notification</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Our advanced order tracking system provides full transparency and real-time information for our customers.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-20">
          <div>
            <div className="relative">
              <div className="absolute inset-0 bg-red-100 rounded-xl blur-xl opacity-50"></div>
              <div className="relative bg-white p-6 rounded-xl shadow-xl overflow-hidden">
                <div className="flex justify-between items-center mb-6 border-b pb-4">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-full bg-red-600 flex items-center justify-center text-white">
                      PC
                    </div>
                    <span className="ml-2 font-bold">PostCHAT Tracking</span>
                  </div>
                  <div className="text-sm text-gray-500">Order #PC12345</div>
                </div>

                <div className="relative pb-8">
                  <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>

                  <div className="relative flex items-start mb-6">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-500 text-white z-10">
                      <FaCheckCircle />
                    </div>
                    <div className="ml-4">
                      <h4 className="text-sm font-bold">Order Confirmed</h4>
                      <p className="text-xs text-gray-500">May 15, 2023 - 10:30 AM</p>
                    </div>
                  </div>

                  <div className="relative flex items-start mb-6">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-500 text-white z-10">
                      <FaCheckCircle />
                    </div>
                    <div className="ml-4">
                      <h4 className="text-sm font-bold">Processing</h4>
                      <p className="text-xs text-gray-500">May 16, 2023 - 09:15 AM</p>
                    </div>
                  </div>

                  <div className="relative flex items-start mb-6">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-green-500 text-white z-10">
                      <FaCheckCircle />
                    </div>
                    <div className="ml-4">
                      <h4 className="text-sm font-bold">Shipped</h4>
                      <p className="text-xs text-gray-500">May 17, 2023 - 02:45 PM</p>
                    </div>
                  </div>

                  <div className="relative flex items-start">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-red-600 text-white z-10">
                      <FaTruck />
                    </div>
                    <div className="ml-4">
                      <h4 className="text-sm font-bold">In Transit</h4>
                      <p className="text-xs text-gray-500">Estimated delivery: May 20, 2023</p>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="flex justify-between items-center">
                    <div className="text-sm font-medium">Tracking Number:</div>
                    <div className="text-sm font-mono bg-gray-100 px-2 py-1 rounded">PC12345XYZ</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100"
              >
                <div className="flex items-start">
                  <div className="mr-4">{feature.icon}</div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <div className="bg-gradient-to-r from-red-600 to-red-800 rounded-xl p-8 text-white">
          <h3 className="text-2xl font-bold mb-6 text-center">Logistics, Shipping and Support</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {logisticsSteps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white/10 backdrop-blur-sm p-6 rounded-lg"
              >
                <div className="bg-red-700 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                  {step.icon}
                </div>
                <h4 className="text-xl font-bold mb-2">{step.title}</h4>
                <p className="text-red-100 text-sm">{step.description}</p>
              </motion.div>
            ))}
          </div>

          <p className="text-center mt-8 text-red-100">
            This optimized process ensures full transparency for our clients and partners, minimizing delays and increasing customer satisfaction.
            Our digital systems monitor every step, enabling quick resolution of any potential issues.
          </p>
        </div>

        <div className="text-center mt-12">
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Our tracking system is fully integrated with the platforms of our logistics partners, providing accurate and up-to-date information
            at every step of the delivery process, from the warehouse to the customer's doorstep.
          </p>
        </div>
      </div>
    </section>
  );
};

export default Tracking;
