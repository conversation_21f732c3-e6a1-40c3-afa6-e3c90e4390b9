<?php

namespace App\Repository\Social;

use App\Entity\Organization;
use App\Entity\Social\TrackingHistory;
use DateTimeImmutable;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<TrackingHistory>
 */
class TrackingHistoryRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, TrackingHistory::class);
    }

    /**
     * Find tracking history for an organization within a date range
     *
     * @param Organization $organization
     * @param DateTimeImmutable $startDate
     * @param DateTimeImmutable $endDate
     * @return TrackingHistory[]
     */
    public function findByOrganizationAndDateRange(Organization $organization, DateTimeImmutable $startDate, DateTimeImmutable $endDate): array
    {
        return $this->createQueryBuilder('th')
            ->join('th.trackingGroup', 'tg')
            ->andWhere('tg.ownerOrganization = :organization')
            ->andWhere('th.createdAt >= :startDate')
            ->andWhere('th.createdAt <= :endDate')
            ->setParameter('organization', $organization)
            ->setParameter('startDate', $startDate)
            ->setParameter('endDate', $endDate)
            ->orderBy('th.createdAt', 'ASC')
            ->getQuery()
            ->getResult()
        ;
    }

    /**
     * Find recent tracking history for an organization
     *
     * @param Organization $organization
     * @param int $limit
     * @return TrackingHistory[]
     */
    public function findRecentByOrganization(Organization $organization, int $limit = 10): array
    {
        return $this->createQueryBuilder('th')
            ->join('th.trackingGroup', 'tg')
            ->andWhere('tg.ownerOrganization = :organization')
            ->setParameter('organization', $organization)
            ->orderBy('th.createdAt', 'DESC')
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult()
        ;
    }
}
