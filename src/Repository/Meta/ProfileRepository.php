<?php

namespace App\Repository\Meta;

use App\Entity\Meta\Profile;
use App\Entity\Organization;
use App\Entity\Social\TrackingGroup;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Profile>
 */
class ProfileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Profile::class);
    }

    /**
     * Find profiles with pagination, filtering, and security checks
     *
     * @param Organization $organization
     * @param int $page
     * @param int $limit
     * @param string|null $search
     * @param string|null $platform
     * @param string|null $trackingGroupId
     * @param array $customFilters
     * @return array{profiles: Profile[], total: int, pages: int, currentPage: int}
     */
    public function findProfilesWithPagination(
        Organization $organization,
        int $page = 1,
        int $limit = 20,
        ?string $search = null,
        ?string $platform = null,
        ?string $trackingGroupId = null,
        array $customFilters = []
    ): array {
        // Get meta IDs from tracking groups that belong to the organization
        $metaIds = $this->getMetaIdsForOrganization($organization, $trackingGroupId);

        if (empty($metaIds)) {
            return [
                'profiles' => [],
                'total' => 0,
                'pages' => 0,
                'currentPage' => $page
            ];
        }

        // If we have custom filters, use a different approach
        if (!empty($customFilters)) {
            return $this->findProfilesWithCustomFilters($organization, $page, $limit, $search, $platform, $trackingGroupId, $customFilters, $metaIds);
        }

        // Standard query without custom filters
        $qb = $this->createQueryBuilder('p')
            ->select('p')
            ->where('p.metaId IN (:metaIds)')
            ->setParameter('metaIds', $metaIds, Connection::PARAM_STR_ARRAY);

        // Apply platform filter
        if ($platform && $platform !== 'all') {
            $qb->andWhere('p.platform = :platform')
               ->setParameter('platform', $platform);
        }

        // Apply search filter
        if ($search) {
            $qb->andWhere('(
                LOWER(p.username) LIKE :search OR
                LOWER(p.metaId) LIKE :search OR
                LOWER(p.platform) LIKE :search OR
                LOWER(CAST(p.data AS TEXT)) LIKE :search
            )')
            ->setParameter('search', '%' . strtolower($search) . '%');
        }

        // Order by creation date (newest first)
        $qb->orderBy('p.createdAt', 'DESC');

        // Count total results (remove ORDER BY for counting to avoid GROUP BY issues)
        $countQb = clone $qb;
        $countQb->select('COUNT(p.id)')
                ->resetDQLPart('orderBy');
        $total = (int) $countQb->getQuery()->getSingleScalarResult();

        // Apply pagination
        $qb->setFirstResult(($page - 1) * $limit)
           ->setMaxResults($limit);

        $profiles = $qb->getQuery()->getResult();

        return [
            'profiles' => $profiles,
            'total' => $total,
            'pages' => (int) ceil($total / $limit),
            'currentPage' => $page
        ];
    }

    /**
     * Find profiles with custom filters using a separate approach
     */
    private function findProfilesWithCustomFilters(
        Organization $organization,
        int $page,
        int $limit,
        ?string $search,
        ?string $platform,
        ?string $trackingGroupId,
        array $customFilters,
        array $metaIds
    ): array {
        $profileIds = $this->getProfileIdsWithCustomFilters($metaIds, $organization, $customFilters, $search, $platform);

        if (empty($profileIds)) {
            return [
                'profiles' => [],
                'total' => 0,
                'pages' => 0,
                'currentPage' => $page
            ];
        }

        // Get profiles by IDs with proper ordering
        $qb = $this->createQueryBuilder('p')
            ->select('p')
            ->where('p.id IN (:profileIds)')
            ->setParameter('profileIds', $profileIds, Connection::PARAM_STR_ARRAY)
            ->orderBy('p.createdAt', 'DESC');

        $total = count($profileIds);

        // Apply pagination
        $qb->setFirstResult(($page - 1) * $limit)
           ->setMaxResults($limit);

        $profiles = $qb->getQuery()->getResult();

        return [
            'profiles' => $profiles,
            'total' => $total,
            'pages' => (int) ceil($total / $limit),
            'currentPage' => $page
        ];
    }

    /**
     * Get available platforms for the organization
     */
    public function getAvailablePlatforms(Organization $organization): array
    {
        $metaIds = $this->getMetaIdsForOrganization($organization);

        if (empty($metaIds)) {
            return [];
        }

        // Use raw SQL to avoid any potential GROUP BY issues
        $placeholders = str_repeat('?,', count($metaIds) - 1) . '?';
        $sql = "
            SELECT DISTINCT p.platform
            FROM meta_profile p
            WHERE p.meta_id IN ($placeholders)
            ORDER BY p.platform ASC
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery($metaIds)->fetchAllAssociative();

        return array_column($result, 'platform');
    }

    /**
     * Get profile statistics for the organization
     */
    public function getProfileStats(Organization $organization): array
    {
        $metaIds = $this->getMetaIdsForOrganization($organization);

        if (empty($metaIds)) {
            return [
                'total' => 0,
                'by_platform' => []
            ];
        }

        // Use raw SQL to avoid GROUP BY issues with Doctrine's default ordering
        $placeholders = str_repeat('?,', count($metaIds) - 1) . '?';
        $sql = "
            SELECT p.platform, COUNT(p.id) as count
            FROM meta_profile p
            WHERE p.meta_id IN ($placeholders)
            GROUP BY p.platform
            ORDER BY count DESC
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $platformStats = $stmt->executeQuery($metaIds)->fetchAllAssociative();

        $total = array_sum(array_column($platformStats, 'count'));

        return [
            'total' => $total,
            'by_platform' => $platformStats
        ];
    }

    /**
     * Check if a profile belongs to the organization (security check)
     */
    public function profileBelongsToOrganization(Profile $profile, Organization $organization): bool
    {
        $metaIds = $this->getMetaIdsForOrganization($organization);
        return in_array($profile->getMetaId(), $metaIds, true);
    }

    /**
     * Get meta IDs from tracking groups that belong to the organization
     */
    private function getMetaIdsForOrganization(Organization $organization, ?string $trackingGroupId = null): array
    {
        $qb = $this->getEntityManager()
            ->getRepository(TrackingGroup::class)
            ->createQueryBuilder('tg')
            ->where('tg.ownerOrganization = :organization')
            ->setParameter('organization', $organization);

        if ($trackingGroupId) {
            $qb->andWhere('tg.id = :trackingGroupId')
               ->setParameter('trackingGroupId', $trackingGroupId);
        }

        $trackingGroups = $qb->getQuery()->getResult();

        $metaIds = [];
        /** @var TrackingGroup[] $trackingGroups */
        foreach ($trackingGroups as $trackingGroup) {
            foreach ($trackingGroup->getListIds() as $item) {
                $metaIds[] = $item;
            }
        }

        return array_unique($metaIds);
    }

    /**
     * Get profile IDs that match custom field filters using raw SQL for JSONB operations
     */
    private function getProfileIdsWithCustomFilters(array $metaIds, Organization $organization, array $customFilters, ?string $search = null, ?string $platform = null): array
    {
        $placeholders = str_repeat('?,', count($metaIds) - 1) . '?';
        $sql = "SELECT DISTINCT p.id FROM meta_profile p
                LEFT JOIN meta_profile_configuration c ON c.profile_id = p.id AND c.organization_id = ?
                WHERE p.meta_id IN ($placeholders)";

        $parameters = array_merge([$organization->getId()->toRfc4122()], $metaIds);
        $paramIndex = count($parameters);

        // Apply platform filter
        if ($platform && $platform !== 'all') {
            $sql .= " AND p.platform = ?";
            $parameters[] = $platform;
        }

        // Apply search filter
        if ($search) {
            $searchTerm = '%' . strtolower($search) . '%';
            $sql .= " AND (
                LOWER(p.username) LIKE ? OR
                LOWER(p.meta_id) LIKE ? OR
                LOWER(p.platform) LIKE ? OR
                LOWER(CAST(p.data AS TEXT)) LIKE ?
            )";
            $parameters = array_merge($parameters, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }

        // Apply custom field filters
        foreach ($customFilters as $index => $filter) {
            $key = trim($filter['key'] ?? '');
            $operator = $filter['operator'] ?? 'equals';
            $value = $filter['value'] ?? null;

            if (!$this->isValidKey($key)) {
                continue;
            }

            switch ($operator) {
                case 'equals':
                    $sql .= " AND c.custom_fields @> ?";
                    $parameters[] = json_encode([['key' => $key, 'value' => $value]]);
                    break;
                case 'not_equals':
                    $sql .= " AND NOT c.custom_fields @> ?";
                    $parameters[] = json_encode([['key' => $key, 'value' => $value]]);
                    break;
                case 'is_empty':
                    $sql .= " AND NOT EXISTS (
                        SELECT 1 FROM jsonb_array_elements(c.custom_fields) elem
                        WHERE elem->>'key' = ?
                        AND elem->>'value' IS NOT NULL
                        AND elem->>'value' <> ''
                        AND elem->>'value' <> 'null'
                    )";
                    $parameters[] = $key;
                    break;
                case 'is_not_empty':
                    $sql .= " AND EXISTS (
                        SELECT 1 FROM jsonb_array_elements(c.custom_fields) elem
                        WHERE elem->>'key' = ?
                        AND elem->>'value' IS NOT NULL
                        AND elem->>'value' <> ''
                        AND elem->>'value' <> 'null'
                    )";
                    $parameters[] = $key;
                    break;
            }
        }

        $result = $this->getEntityManager()
            ->getConnection()
            ->executeQuery($sql, $parameters)
            ->fetchAllAssociative();

        return array_column($result, 'id');
    }

    private function isValidKey(string $key): bool
    {
        return preg_match('/^[a-zA-Z0-9_-]+$/', $key) === 1;
    }
}
