<?php

namespace App\Repository\Meta;

use App\Entity\Meta\Profile;
use App\Entity\Organization;
use App\Entity\Social\TrackingGroup;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\ORM\Tools\Pagination\Paginator;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<Profile>
 */
class ProfileRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Profile::class);
    }

    /**
     * Find profiles with pagination, filtering, and security checks
     *
     * @param Organization $organization
     * @param int $page
     * @param int $limit
     * @param string|null $search
     * @param string|null $platform
     * @param string|null $trackingGroupId
     * @param array $customFilters
     * @return array{profiles: Profile[], total: int, pages: int, currentPage: int}
     */
    public function findProfilesWithPagination(
        Organization $organization,
        int $page = 1,
        int $limit = 20,
        ?string $search = null,
        ?string $platform = null,
        ?string $trackingGroupId = null,
        array $customFilters = []
    ): array {
        // Get meta IDs from tracking groups that belong to the organization
        $metaIds = $this->getMetaIdsForOrganization($organization, $trackingGroupId);

        if (empty($metaIds)) {
            return [
                'profiles' => [],
                'total' => 0,
                'pages' => 0,
                'currentPage' => $page
            ];
        }

        // If we have custom filters, use a different approach
        if (!empty($customFilters)) {
            return $this->findProfilesWithCustomFilters($organization, $page, $limit, $search, $platform, $trackingGroupId, $customFilters, $metaIds);
        }

        // Standard query without custom filters
        $qb = $this->createQueryBuilder('p')
            ->select('p')
            ->where('p.metaId IN (:metaIds)')
            ->setParameter('metaIds', $metaIds, Connection::PARAM_STR_ARRAY);

        // Apply platform filter
        if ($platform && $platform !== 'all') {
            $qb->andWhere('p.platform = :platform')
               ->setParameter('platform', $platform);
        }

        // Apply search filter
        if ($search) {
            $qb->andWhere('(
                LOWER(p.username) LIKE :search OR
                LOWER(p.metaId) LIKE :search OR
                LOWER(p.platform) LIKE :search OR
                LOWER(CAST(p.data AS TEXT)) LIKE :search
            )')
            ->setParameter('search', '%' . strtolower($search) . '%');
        }

        // Order by creation date (newest first)
        $qb->orderBy('p.createdAt', 'DESC');

        // Count total results
        $countQb = clone $qb;
        $countQb->select('COUNT(p.id)');
        $total = (int) $countQb->getQuery()->getSingleScalarResult();

        // Apply pagination
        $qb->setFirstResult(($page - 1) * $limit)
           ->setMaxResults($limit);

        $profiles = $qb->getQuery()->getResult();

        return [
            'profiles' => $profiles,
            'total' => $total,
            'pages' => (int) ceil($total / $limit),
            'currentPage' => $page
        ];
    }

    /**
     * Find profiles with custom filters using a separate approach
     */
    private function findProfilesWithCustomFilters(
        Organization $organization,
        int $page,
        int $limit,
        ?string $search,
        ?string $platform,
        ?string $trackingGroupId,
        array $customFilters,
        array $metaIds
    ): array {
        $profileIds = $this->getProfileIdsWithCustomFilters($metaIds, $organization, $customFilters, $search, $platform);

        if (empty($profileIds)) {
            return [
                'profiles' => [],
                'total' => 0,
                'pages' => 0,
                'currentPage' => $page
            ];
        }

        // Get profiles by IDs with proper ordering
        $qb = $this->createQueryBuilder('p')
            ->select('p')
            ->where('p.id IN (:profileIds)')
            ->setParameter('profileIds', $profileIds, Connection::PARAM_STR_ARRAY)
            ->orderBy('p.createdAt', 'DESC');

        $total = count($profileIds);

        // Apply pagination
        $qb->setFirstResult(($page - 1) * $limit)
           ->setMaxResults($limit);

        $profiles = $qb->getQuery()->getResult();

        return [
            'profiles' => $profiles,
            'total' => $total,
            'pages' => (int) ceil($total / $limit),
            'currentPage' => $page
        ];
    }

    /**
     * Get available platforms for the organization
     */
    public function getAvailablePlatforms(Organization $organization): array
    {
        $metaIds = $this->getMetaIdsForOrganization($organization);

        if (empty($metaIds)) {
            return [];
        }

        return $this->createQueryBuilder('p')
            ->select('DISTINCT p.platform')
            ->where('p.metaId IN (:metaIds)')
            ->setParameter('metaIds', $metaIds)
            ->orderBy('p.platform', 'ASC')
            ->getQuery()
            ->getSingleColumnResult();
    }

    /**
     * Get profile statistics for the organization
     */
    public function getProfileStats(Organization $organization): array
    {
        $metaIds = $this->getMetaIdsForOrganization($organization);

        if (empty($metaIds)) {
            return [
                'total' => 0,
                'by_platform' => []
            ];
        }

        // Use raw SQL to avoid GROUP BY issues with Doctrine's default ordering
        $sql = "
            SELECT p.platform, COUNT(p.id) as count
            FROM meta_profile p
            WHERE p.meta_id IN (:metaIds)
            GROUP BY p.platform
            ORDER BY count DESC
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $platformStats = $stmt->executeQuery([
            'metaIds' => $metaIds
        ], [
            'metaIds' => Connection::PARAM_STR_ARRAY
        ])->fetchAllAssociative();

        $total = array_sum(array_column($platformStats, 'count'));

        return [
            'total' => $total,
            'by_platform' => $platformStats
        ];
    }

    /**
     * Check if a profile belongs to the organization (security check)
     */
    public function profileBelongsToOrganization(Profile $profile, Organization $organization): bool
    {
        $metaIds = $this->getMetaIdsForOrganization($organization);
        return in_array($profile->getMetaId(), $metaIds, true);
    }

    /**
     * Get meta IDs from tracking groups that belong to the organization
     */
    private function getMetaIdsForOrganization(Organization $organization, ?string $trackingGroupId = null): array
    {
        $qb = $this->getEntityManager()
            ->getRepository(TrackingGroup::class)
            ->createQueryBuilder('tg')
            ->where('tg.ownerOrganization = :organization')
            ->setParameter('organization', $organization);

        if ($trackingGroupId) {
            $qb->andWhere('tg.id = :trackingGroupId')
               ->setParameter('trackingGroupId', $trackingGroupId);
        }

        $trackingGroups = $qb->getQuery()->getResult();

        $metaIds = [];
        /** @var TrackingGroup[] $trackingGroups */
        foreach ($trackingGroups as $trackingGroup) {
            foreach ($trackingGroup->getListIds() as $item) {
                $metaIds[] = $item;
            }
        }

        return array_unique($metaIds);
    }

    /**
     * Get profile IDs that match custom field filters using raw SQL for JSONB operations
     */
    private function getProfileIdsWithCustomFilters(array $metaIds, Organization $organization, array $customFilters, ?string $search = null, ?string $platform = null): array
    {
        $sql = "SELECT DISTINCT p.id FROM meta_profile p
                LEFT JOIN meta_profile_configuration c ON c.profile_id = p.id AND c.organization_id = :organizationId
                WHERE p.meta_id IN (:metaIds)";

        $parameters = [
            'metaIds' => $metaIds,
            'organizationId' => $organization->getId()->toRfc4122()
        ];

        $types = [
            'metaIds' => Connection::PARAM_STR_ARRAY
        ];

        // Apply platform filter
        if ($platform && $platform !== 'all') {
            $sql .= " AND p.platform = :platform";
            $parameters['platform'] = $platform;
        }

        // Apply search filter
        if ($search) {
            $sql .= " AND (
                LOWER(p.username) LIKE :search OR
                LOWER(p.meta_id) LIKE :search OR
                LOWER(p.platform) LIKE :search OR
                LOWER(CAST(p.data AS TEXT)) LIKE :search
            )";
            $parameters['search'] = '%' . strtolower($search) . '%';
        }

        // Apply custom field filters
        foreach ($customFilters as $index => $filter) {
            $key = trim($filter['key'] ?? '');
            $operator = $filter['operator'] ?? 'equals';
            $value = $filter['value'] ?? null;

            if (!$this->isValidKey($key)) {
                continue;
            }

            $paramKey = "filter_$index";
            $jsonCondition = $this->buildJsonCondition('c.custom_fields', $operator, $paramKey, $value);
            $sql .= " AND $jsonCondition";

            $filterParams = $this->getParametersForOperator($operator, $paramKey, $key, $value);
            $parameters = array_merge($parameters, $filterParams);
        }

        $result = $this->getEntityManager()
            ->getConnection()
            ->executeQuery($sql, $parameters, $types)
            ->fetchAllAssociative();

        return array_column($result, 'id');
    }

    private function buildJsonCondition(string $field, string $operator, string $paramKey, $value): string
    {
        return match($operator) {
            'equals' => "$field @> :{$paramKey}_contains",
            'not_equals' => "NOT $field @> :{$paramKey}_contains",
            'is_empty' => "NOT EXISTS (
                SELECT 1 FROM jsonb_array_elements($field) elem
                WHERE elem->>'key' = :{$paramKey}_key
                AND elem->>'value' IS NOT NULL
                AND elem->>'value' <> ''
                AND elem->>'value' <> 'null'
            )",
            'is_not_empty' => "EXISTS (
                SELECT 1 FROM jsonb_array_elements($field) elem
                WHERE elem->>'key' = :{$paramKey}_key
                AND elem->>'value' IS NOT NULL
                AND elem->>'value' <> ''
                AND elem->>'value' <> 'null'
            )",
            default => '1=1'
        };
    }

    private function getParametersForOperator(string $operator, string $paramKey, string $key, $value): array
    {
        return match($operator) {
            'equals', 'not_equals' => [
                "{$paramKey}_contains" => json_encode([['key' => $key, 'value' => $value]]),
            ],
            'is_empty', 'is_not_empty' => [
                "{$paramKey}_key" => $key
            ],
            default => []
        };
    }

    private function isValidKey(string $key): bool
    {
        return preg_match('/^[a-zA-Z0-9_-]+$/', $key) === 1;
    }
}
