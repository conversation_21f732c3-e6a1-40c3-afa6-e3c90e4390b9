<?php
namespace App\Entity;

use App\Entity\Organization\CustomField;
use App\Entity\Organization\CustomFieldTemplate;
use App\Entity\Social\ProfileTracking;
use App\Entity\Social\TrackingGroup;
use App\Repository\OrganizationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: OrganizationRepository::class)]
#[ORM\HasLifecycleCallbacks]
class Organization
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $stripeCustomerId = null;

    #[ORM\Column]
    private ?int $credits = 0;

    /**
     * @var Collection<int, OrganizationUser>
     */
    #[ORM\OneToMany(targetEntity: OrganizationUser::class, mappedBy: 'organization', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $organizationUsers;

    /**
     * @var Collection<int, Subscription>
     */
    #[ORM\OneToMany(targetEntity: Subscription::class, mappedBy: 'Organization', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $subscriptions;

    /**
     * @var Collection<int, TrackingGroup>
     */
    #[ORM\OneToMany(targetEntity: TrackingGroup::class, mappedBy: 'ownerOrganization', orphanRemoval: true)]
    private Collection $socialTrackingGroups;

    /**
     * @var Collection<int, ProfileTracking>
     */
    #[ORM\OneToMany(targetEntity: ProfileTracking::class, mappedBy: 'ownerOrganization', orphanRemoval: true)]
    private Collection $profileTrackings;

    /**
     * @var Collection<int, CustomFieldTemplate>
     */
    #[ORM\OneToMany(targetEntity: CustomFieldTemplate::class, mappedBy: 'organization', orphanRemoval: true)]
    private Collection $customFieldTemplates;

    /**
     * @var Collection<int, CustomField>
     */
    #[ORM\OneToMany(targetEntity: CustomField::class, mappedBy: 'organization', orphanRemoval: true)]
    private Collection $customFields;

    public function __construct()
    {
        $this->organizationUsers = new ArrayCollection();
        $this->subscriptions = new ArrayCollection();
        $this->socialTrackingGroups = new ArrayCollection();
        $this->profileTrackings = new ArrayCollection();
        $this->customFieldTemplates = new ArrayCollection();
        $this->customFields = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getStripeCustomerId(): ?string
    {
        return $this->stripeCustomerId;
    }

    public function setStripeCustomerId(?string $stripeCustomerId): static
    {
        $this->stripeCustomerId = $stripeCustomerId;

        return $this;
    }

    public function getCredits(): ?int
    {
        return $this->credits;
    }

    public function setCredits(int $credits): static
    {
        $this->credits = $credits;

        return $this;
    }

    public function deductCredits(int $quantity): static {
        $this->credits -= $quantity;

        return $this;
    }

    /**
     * @return Collection<int, OrganizationUser>
     */
    public function getOrganizationUsers(): Collection
    {
        return $this->organizationUsers;
    }

    public function addOrganizationUser(OrganizationUser $organizationUser): static
    {
        if (!$this->organizationUsers->contains($organizationUser)) {
            $this->organizationUsers->add($organizationUser);
            $organizationUser->setOrganization($this);
        }

        return $this;
    }

    public function removeOrganizationUser(OrganizationUser $organizationUser): static
    {
        if ($this->organizationUsers->removeElement($organizationUser)) {
            // set the owning side to null (unless already changed)
            if ($organizationUser->getOrganization() === $this) {
                $organizationUser->setOrganization(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Subscription>
     */
    public function getSubscriptions(): Collection
    {
        return $this->subscriptions;
    }

    public function addSubscription(Subscription $subscription): static
    {
        if (!$this->subscriptions->contains($subscription)) {
            $this->subscriptions->add($subscription);
            $subscription->setOrganization($this);
        }

        return $this;
    }

    public function removeSubscription(Subscription $subscription): static
    {
        if ($this->subscriptions->removeElement($subscription)) {
            // set the owning side to null (unless already changed)
            if ($subscription->getOrganization() === $this) {
                $subscription->setOrganization(null);
            }
        }

        return $this;
    }

    use DateTrait;

    /**
     * @return Collection<int, TrackingGroup>
     */
    public function getSocialTrackingGroups(): Collection
    {
        return $this->socialTrackingGroups;
    }

    public function addSocialTrackingGroup(TrackingGroup $socialTrackingGroup): static
    {
        if (!$this->socialTrackingGroups->contains($socialTrackingGroup)) {
            $this->socialTrackingGroups->add($socialTrackingGroup);
            $socialTrackingGroup->setOwnerOrganization($this);
        }

        return $this;
    }

    public function removeSocialTrackingGroup(TrackingGroup $socialTrackingGroup): static
    {
        if ($this->socialTrackingGroups->removeElement($socialTrackingGroup)) {
            // set the owning side to null (unless already changed)
            if ($socialTrackingGroup->getOwnerOrganization() === $this) {
                $socialTrackingGroup->setOwnerOrganization(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProfileTracking>
     */
    public function getProfileTrackings(): Collection
    {
        return $this->profileTrackings;
    }

    public function addProfileTracking(ProfileTracking $profileTracking): static
    {
        if (!$this->profileTrackings->contains($profileTracking)) {
            $this->profileTrackings->add($profileTracking);
            $profileTracking->setOwnerOrganization($this);
        }

        return $this;
    }

    public function removeProfileTracking(ProfileTracking $profileTracking): static
    {
        if ($this->profileTrackings->removeElement($profileTracking)) {
            // set the owning side to null (unless already changed)
            if ($profileTracking->getOwnerOrganization() === $this) {
                $profileTracking->setOwnerOrganization(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, CustomFieldTemplate>
     */
    public function getCustomFieldTemplates(): Collection
    {
        return $this->customFieldTemplates;
    }

    public function addCustomFieldTemplate(CustomFieldTemplate $customFieldTemplate): static
    {
        if (!$this->customFieldTemplates->contains($customFieldTemplate)) {
            $this->customFieldTemplates->add($customFieldTemplate);
            $customFieldTemplate->setOrganization($this);
        }

        return $this;
    }

    public function removeCustomFieldTemplate(CustomFieldTemplate $customFieldTemplate): static
    {
        if ($this->customFieldTemplates->removeElement($customFieldTemplate)) {
            // set the owning side to null (unless already changed)
            if ($customFieldTemplate->getOrganization() === $this) {
                $customFieldTemplate->setOrganization(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, CustomField>
     */
    public function getCustomFields(): Collection
    {
        return $this->customFields;
    }

    public function addCustomField(CustomField $customField): static
    {
        if (!$this->customFields->contains($customField)) {
            $this->customFields->add($customField);
            $customField->setOrganization($this);
        }

        return $this;
    }

    public function removeCustomField(CustomField $customField): static
    {
        if ($this->customFields->removeElement($customField)) {
            // set the owning side to null (unless already changed)
            if ($customField->getOrganization() === $this) {
                $customField->setOrganization(null);
            }
        }

        return $this;
    }
}
