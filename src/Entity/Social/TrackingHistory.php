<?php
namespace App\Entity\Social;

use App\Entity\DateTrait;
use App\Entity\Meta\Profile;
use App\Repository\Social\TrackingHistoryRepository;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: TrackingHistoryRepository::class)]
#[ORM\Table(name: 'social_tracking_history')]
#[ORM\HasLifecycleCallbacks]
class TrackingHistory
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne(inversedBy: 'trackingHistory')]
    #[ORM\JoinColumn(nullable: false)]
    private ?TrackingGroup $trackingGroup = null;

    #[ORM\Column]
    private ?int $progress = 0;

    #[ORM\Column(nullable: true)]
    private ?int $totalProfiles = null;

    #[ORM\Column]
    private array $creditDetails = [];

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getTrackingGroup(): ?TrackingGroup
    {
        return $this->trackingGroup;
    }

    public function setTrackingGroup(?TrackingGroup $trackingGroup): static
    {
        $this->trackingGroup = $trackingGroup;

        return $this;
    }

    public function getProgress(): ?int
    {
        return $this->progress;
    }

    public function setProgress(int $progress): static
    {
        $this->progress = $progress;

        return $this;
    }

    public function getTotalProfiles(): ?int
    {
        return $this->totalProfiles;
    }

    public function setTotalProfiles(int $totalProfiles): static
    {
        $this->totalProfiles = $totalProfiles;

        return $this;
    }

    public function getCreditDetails(): array
    {
        return $this->creditDetails;
    }

    public function setCreditDetails(array $creditDetails): static
    {
        $this->creditDetails = $creditDetails;

        return $this;
    }

    use DateTrait;
}
