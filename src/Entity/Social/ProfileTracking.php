<?php
namespace App\Entity\Social;

use App\Entity\DateTrait;
use App\Entity\Organization;
use App\Entity\Task\Status;
use App\Entity\User;
use App\Repository\Social\ProfileTrackingRepository;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: ProfileTrackingRepository::class)]
#[ORM\Table(name: 'social_profile_tracking')]
#[ORM\HasLifecycleCallbacks]
class ProfileTracking
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $name = null;

    #[ORM\Column]
    private array $list = [];

    #[ORM\Column]
    private array $customFields = [];

    #[ORM\Column]
    private ?bool $runProfileAIAnalysis = false;

    #[ORM\Column]
    private ?bool $keepDataUpdated = false;

    #[ORM\ManyToOne(inversedBy: 'profileTrackings')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Organization $ownerOrganization = null;

    #[ORM\ManyToOne(inversedBy: 'profileTrackings')]
    #[ORM\JoinColumn(nullable: false)]
    private ?User $createdBy = null;

    #[ORM\Column]
    private ?int $currentProgress = null;

    #[ORM\Column(enumType: Status::class)]
    private ?Status $status = null;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $lastRunAt = null;

    use DateTrait;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getList(): array
    {
        return $this->list;
    }

    public function setList(array $list): static
    {
        $this->list = $list;

        return $this;
    }

    public function getCustomFields(): array
    {
        return $this->customFields;
    }

    public function setCustomFields(array $customFields): static
    {
        $this->customFields = $customFields;

        return $this;
    }

    public function getOwnerOrganization(): ?Organization
    {
        return $this->ownerOrganization;
    }

    public function setOwnerOrganization(?Organization $ownerOrganization): static
    {
        $this->ownerOrganization = $ownerOrganization;

        return $this;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy): static
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function isRunProfileAIAnalysis(): ?bool
    {
        return $this->runProfileAIAnalysis;
    }

    public function setRunProfileAIAnalysis(bool $runProfileAIAnalysis): static
    {
        $this->runProfileAIAnalysis = $runProfileAIAnalysis;

        return $this;
    }

    public function isKeepDataUpdated(): ?bool
    {
        return $this->keepDataUpdated;
    }

    public function setKeepDataUpdated(bool $keepDataUpdated): static
    {
        $this->keepDataUpdated = $keepDataUpdated;

        return $this;
    }

    public function getCurrentProgress(): ?int
    {
        return $this->currentProgress;
    }

    public function setCurrentProgress(int $currentProgress): static
    {
        $this->currentProgress = $currentProgress;

        return $this;
    }

    public function getStatus(): ?Status
    {
        return $this->status;
    }

    public function setStatus(Status $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getLastRunAt(): ?DateTimeImmutable
    {
        return $this->lastRunAt;
    }

    public function setLastRunAt(?DateTimeImmutable $lastRunAt): static
    {
        $this->lastRunAt = $lastRunAt;

        return $this;
    }
}
