<?php

namespace App\Entity\Social\TrackingGroup;

use App\Entity\DateTrait;
use App\Entity\Social\TrackingGroup;
use App\Repository\Social\TrackingGroup\CredentialRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: CredentialRepository::class)]
#[ORM\Table(name: 'social_tracking_group_credential')]
#[ORM\HasLifecycleCallbacks]
class Credential
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\OneToOne(inversedBy: 'credential', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?TrackingGroup $trackingGroup = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $token = null;

    public function __construct(TrackingGroup $trackingGroup) {
        $this->trackingGroup = $trackingGroup;
        $this->token = Uuid::v4();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    #[Ignore]
    public function getTrackingGroup(): ?TrackingGroup
    {
        return $this->trackingGroup;
    }

    public function setTrackingGroup(TrackingGroup $trackingGroup): static
    {
        $this->trackingGroup = $trackingGroup;

        return $this;
    }

    public function getToken(): ?string
    {
        return $this->token;
    }

    public function setToken(string $token): static
    {
        $this->token = $token;

        return $this;
    }

    use DateTrait;
}
