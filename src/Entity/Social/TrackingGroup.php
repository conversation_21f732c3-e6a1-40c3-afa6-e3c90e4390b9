<?php
namespace App\Entity\Social;

use App\Entity\Data\Filter;
use App\Entity\DateTrait;
use App\Entity\Organization;
use App\Entity\Social\TrackingGroup\Credential;
use App\Entity\Social\TrackingGroup\DataSource;
use App\Entity\Social\TrackingGroup\ListType;
use App\Entity\Social\TrackingGroup\Status;
use App\Entity\User;
use App\Repository\Social\TrackingGroupRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: TrackingGroupRepository::class)]
#[ORM\Table(name: 'social_tracking_group')]
#[ORM\HasLifecycleCallbacks]
class TrackingGroup
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $name = null;

    #[ORM\Column(length: 50)]
    private ?string $dataSource = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $listType = null;

    #[ORM\Column(length: 50)]
    private ?string $category = null;

    #[ORM\Column(length: 50, nullable: true)]
    private ?string $subCategory = null;

    #[ORM\Column]
    private array $list = [];

    #[ORM\Column]
    private ?int $maximumPostsPerProfile = 0;

    #[ORM\Column]
    private ?int $maximumCommentsPerPost = 0;

    #[ORM\Column]
    private ?bool $runProfileAIAnalysis = false;

    #[ORM\Column]
    private ?bool $keepDataUpdated = false;

    #[ORM\ManyToOne(inversedBy: 'socialTrackingGroups')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Organization $ownerOrganization = null;

    #[ORM\ManyToOne(inversedBy: 'socialTrackingGroups')]
    private ?User $createdBy = null;

    #[ORM\Column]
    private ?array $creditDetails = [];

    #[ORM\OneToOne(mappedBy: 'trackingGroup', cascade: ['persist', 'remove', 'detach'])]
    private ?TrackingStatus $status = null;

    /**
     * @var Collection<int, TrackingHistory>
     */
    #[ORM\OneToMany(targetEntity: TrackingHistory::class, mappedBy: 'trackingGroup', cascade: ['persist', 'remove', 'detach'], orphanRemoval: true)]
    private Collection $trackingHistory;

    #[ORM\OneToOne(mappedBy: 'trackingGroup', cascade: ['persist', 'remove', 'detach'])]
    private ?Credential $credential = null;

    #[ORM\Column(nullable: true)]
    private ?array $customFields = [];

    public function __construct()
    {
        $this->trackingHistory = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getDataSourceForDisplay(): ?string {
        return match ($this->dataSource) {
            DataSource::Facebook->value => 'Facebook',
            DataSource::Instagram->value => 'Instagram',
            default => 'Unknown'
        };
    }

    public function getDataSource(): ?string
    {
        return $this->dataSource;
    }

    public function setDataSource(string $dataSource): static
    {
        $this->dataSource = $dataSource;

        return $this;
    }

    public function getListType(): ?string
    {
        return $this->listType;
    }

    public function setListType(?string $listType): static
    {
        $this->listType = $listType;

        return $this;
    }

    public function getCategory(): ?string
    {
        return $this->category;
    }

    public function setCategory(string $category): static
    {
        $this->category = $category;

        return $this;
    }

    public function getSubCategory(): ?string
    {
        return $this->subCategory;
    }

    public function setSubCategory(?string $subCategory): static
    {
        $this->subCategory = $subCategory;

        return $this;
    }

    public function getList(): array
    {
        return $this->list;
    }

    public function setList(array $list): static
    {
        $this->list = $list;

        return $this;
    }

    public function updateList(array $newValues): static {
        $existingMap = [];
        foreach ($this->getList() as $item) {
            [$value, $id] = array_pad(explode(':', $item, 2), 2, null);
            $existingMap[$value] = $id; // Store value -> id mapping
        }

        $updatedList = [];
        foreach ($newValues as $value) {
            if (array_key_exists($value, $existingMap)) {
                // Keep the original value with ID
                $updatedList[] = $value . ($existingMap[$value] !== null ? ":{$existingMap[$value]}" : "");
            } else {
                // New value, added without an ID
                $updatedList[] = $value;
            }
        }

        // Update the entity list
        $this->setList($updatedList);

        return $this;
    }

    public function getListForDisplay(): array {
        return array_map(function (?string $item) {
            return str_contains($item, ':') ? explode(':', $item)[0] : $item;
        }, $this->getList());
    }

    public function getListIds(): array {
        return array_map(function (?string $item) {
            return str_contains($item, ':') ? explode(':', $item)[1] : $item;
        }, $this->getList());
    }

    public function getMaximumPostsPerProfile(): ?int
    {
        return $this->maximumPostsPerProfile;
    }

    public function setMaximumPostsPerProfile(int $maximumPostsPerProfile): static
    {
        $this->maximumPostsPerProfile = $maximumPostsPerProfile;

        return $this;
    }

    public function getMaximumCommentsPerPost(): ?int
    {
        return $this->maximumCommentsPerPost;
    }

    public function setMaximumCommentsPerPost(int $maximumCommentsPerPost): static
    {
        $this->maximumCommentsPerPost = $maximumCommentsPerPost;

        return $this;
    }

    public function runProfileAIAnalysis(): ?bool
    {
        return $this->runProfileAIAnalysis;
    }

    public function setRunProfileAIAnalysis(bool $runProfileAIAnalysis): static
    {
        $this->runProfileAIAnalysis = $runProfileAIAnalysis;

        return $this;
    }

    public function keepDataUpdated(): ?bool
    {
        return $this->keepDataUpdated;
    }

    public function setKeepDataUpdated(bool $keepDataUpdated): static
    {
        $this->keepDataUpdated = $keepDataUpdated;

        return $this;
    }

    public function getOwnerOrganization(): ?Organization
    {
        return $this->ownerOrganization;
    }

    public function setOwnerOrganization(?Organization $ownerOrganization): static
    {
        $this->ownerOrganization = $ownerOrganization;

        return $this;
    }

    public function getCreatedBy(): ?User
    {
        return $this->createdBy;
    }

    public function setCreatedBy(?User $createdBy): static
    {
        $this->createdBy = $createdBy;

        return $this;
    }

    public function getCreditDetails(): array
    {
        return $this->creditDetails;
    }

    public function setCreditDetails(array $creditDetails): static
    {
        $this->creditDetails = $creditDetails;

        return $this;
    }

    public function getStatus(): ?TrackingStatus
    {
        return $this->status;
    }

    public function setStatus(TrackingStatus $status): static
    {
        // set the owning side of the relation if necessary
        if ($status->getTrackingGroup() !== $this) {
            $status->setTrackingGroup($this);
        }

        $this->status = $status;

        return $this;
    }

    public function getStatusClass(): ?string {
        return match ($this->status->getStatus()) {
            Status::Finished, Status::WaitingNextUpdate => 'status-green',
            Status::Processing => 'status-yellow',
            Status::Queued, Status::UpdateRequested => 'status-orange',
            Status::Paused => 'status-blue',
            Status::PauseRequested => 'status-indigo',
            default => 'bg-warning'
        };
    }

    public function getProgressBarClass(): ?string {
        return match ($this->status->getStatus()) {
            Status::Processing => 'bg-yellow',
            Status::Queued, Status::UpdateRequested => 'bg-orange',
            Status::Paused => 'bg-blue',
            Status::PauseRequested => 'bg-indigo',
            default => 'bg-warning'
        };
    }

    public function getStatusLabel(): ?string {
        return match ($this->status->getStatus()) {
            Status::Finished, Status::WaitingNextUpdate => 'Finished',
            Status::Processing => 'Processing',
            Status::Queued, Status::UpdateRequested => 'Queued',
            Status::Paused => 'Paused',
            Status::PauseRequested => 'Pause Requested',
            default => 'Unknown'
        };
    }

    public function isStatusFinished(bool $considerWaitingNextUpdateAsFinished = true): bool {
        return $this->status->getStatus() === Status::Finished || ($considerWaitingNextUpdateAsFinished && $this->status->getStatus() === Status::WaitingNextUpdate);
    }

    /**
     * @return Collection<int, TrackingHistory>
     */
    public function getTrackingHistory(): Collection
    {
        return $this->trackingHistory;
    }

    public function addTrackingHistory(TrackingHistory $trackingHistory): static
    {
        if (!$this->trackingHistory->contains($trackingHistory)) {
            $this->trackingHistory->add($trackingHistory);
            $trackingHistory->setTrackingGroup($this);
        }

        return $this;
    }

    public function removeTrackingHistory(TrackingHistory $trackingHistory): static
    {
        if ($this->trackingHistory->removeElement($trackingHistory)) {
            // set the owning side to null (unless already changed)
            if ($trackingHistory->getTrackingGroup() === $this) {
                $trackingHistory->setTrackingGroup(null);
            }
        }

        return $this;
    }

    public function getCredential(): ?Credential
    {
        return $this->credential;
    }

    public function setCredential(Credential $credential): static
    {
        // set the owning side of the relation if necessary
        if ($credential->getTrackingGroup() !== $this) {
            $credential->setTrackingGroup($this);
        }

        $this->credential = $credential;

        return $this;
    }

    public function getCustomFields(): ?array
    {
        return $this->customFields;
    }

    public function setCustomFields(?array $customFields): static
    {
        $this->customFields = $customFields;

        return $this;
    }

    use DateTrait;
}
