<?php

namespace App\Entity\Social;

use App\Entity\DateTrait;
use App\Entity\Meta\Profile;
use App\Entity\Social\TrackingGroup\Status;
use App\Repository\Social\TrackingStatusRepository;
use DateTimeImmutable;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: TrackingStatusRepository::class)]
#[ORM\Table(name: 'social_tracking_status')]
#[ORM\HasLifecycleCallbacks]
class TrackingStatus
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\OneToOne(inversedBy: 'status', cascade: ['persist', 'remove'])]
    #[ORM\JoinColumn(nullable: false)]
    private ?TrackingGroup $trackingGroup = null;

    #[ORM\Column(nullable: true, enumType: Status::class)]
    private ?Status $status = null;

    #[ORM\ManyToOne]
    private ?Profile $currentProfile = null;

    #[ORM\Column]
    private ?int $progress = 0;

    #[ORM\Column(nullable: true)]
    private ?int $totalProfiles = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $lastError = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $lastProcessedProfile = null;

    #[ORM\Column(nullable: true)]
    private ?DateTimeImmutable $lastRunAt = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getTrackingGroup(): ?TrackingGroup
    {
        return $this->trackingGroup;
    }

    public function setTrackingGroup(TrackingGroup $trackingGroup): static
    {
        $this->trackingGroup = $trackingGroup;

        return $this;
    }

    public function getStatus(): ?Status
    {
        return $this->status;
    }

    public function setStatus(?Status $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getCurrentProfile(): ?Profile
    {
        return $this->currentProfile;
    }

    public function setCurrentProfile(?Profile $currentProfile): static
    {
        $this->currentProfile = $currentProfile;

        return $this;
    }

    public function getProgress(): ?int
    {
        return $this->progress;
    }

    public function setProgress(int $progress): static
    {
        $this->progress = $progress;

        return $this;
    }

    public function getTotalProfiles(): ?int
    {
        return $this->totalProfiles;
    }

    public function setTotalProfiles(int $totalProfiles): static
    {
        $this->totalProfiles = $totalProfiles;

        return $this;
    }

    public function getLastError(): ?string
    {
        return $this->lastError;
    }

    public function setLastError(?string $lastError): static
    {
        $this->lastError = $lastError;

        return $this;
    }

    public function getLastProcessedProfile(): ?string
    {
        return $this->lastProcessedProfile;
    }

    public function setLastProcessedProfile(?string $lastProcessedProfile): static
    {
        $this->lastProcessedProfile = $lastProcessedProfile;

        return $this;
    }

    public function getLastRunAt(): ?DateTimeImmutable
    {
        return $this->lastRunAt;
    }

    public function setLastRunAt(?DateTimeImmutable $lastRunAt): static
    {
        $this->lastRunAt = $lastRunAt;

        return $this;
    }

    use DateTrait;
}
