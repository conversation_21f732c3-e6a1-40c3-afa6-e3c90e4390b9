<?php

namespace App\Entity\Social;

use App\Entity\DateTrait;
use App\Entity\Meta\Profile;
use App\Entity\Social\TrackingGroup\Status;
use App\Repository\Social\PollRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: PollRepository::class)]
#[ORM\Table(name: 'social_poll')]
#[ORM\HasLifecycleCallbacks]
class Poll
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $name = null;

    #[ORM\Column]
    private array $list = [];

    #[ORM\Column]
    private array $tags = [];

    #[ORM\Column]
    private array $customFields = [];

    #[ORM\Column(nullable: true, enumType: Status::class)]
    private ?Status $status = null;

    #[ORM\ManyToOne]
    private ?Profile $currentProfile = null;

    #[ORM\Column]
    private ?int $progress = 0;

    #[ORM\Column(nullable: true)]
    private ?int $totalProfiles = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $lastError = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $lastProcessedProfile = null;

    use DateTrait;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getList(): array
    {
        return $this->list;
    }

    public function setList(array $list): static
    {
        $this->list = $list;

        return $this;
    }

    public function getTags(): array
    {
        return $this->tags;
    }

    public function setTags(array $tags): static
    {
        $this->tags = $tags;

        return $this;
    }

    public function getCustomFields(): array
    {
        return $this->customFields;
    }

    public function setCustomFields(array $customFields): static
    {
        $this->customFields = $customFields;

        return $this;
    }
}
