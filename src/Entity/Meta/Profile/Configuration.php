<?php

namespace App\Entity\Meta\Profile;

use App\Entity\DateTrait;
use App\Entity\Meta\Profile;
use App\Entity\Organization;
use App\Entity\Organization\CustomField;
use App\Repository\Meta\Profile\ConfigurationRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: ConfigurationRepository::class)]
#[ORM\Table(name: 'meta_profile_configuration')]
#[ORM\HasLifecycleCallbacks]
class Configuration
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Profile $profile = null;

    #[ORM\ManyToOne]
    #[ORM\JoinColumn(nullable: false)]
    private ?Organization $organization = null;

    #[ORM\Column(type: Types::JSON, options: ['jsonb' => true])]
    private array $customFields = [];

    /**
     * @var Collection<int, CustomField>
     */
    #[ORM\JoinTable(
        name: 'meta_profile_configuration_organization_custom_field',
        joinColumns: [
            new ORM\JoinColumn(
                name: 'configuration_id',
                referencedColumnName: 'id',
                onDelete: 'CASCADE'
            )
        ],
        inverseJoinColumns: [
            new ORM\JoinColumn(
                name: 'custom_field_id',
                referencedColumnName: 'id',
                onDelete: 'CASCADE'
            )
        ]
    )]
    #[ORM\ManyToMany(targetEntity: CustomField::class)]
    private Collection $organizationCustomFields;

    use DateTrait;

    public function __construct()
    {
        $this->organizationCustomFields = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getProfile(): ?Profile
    {
        return $this->profile;
    }

    public function setProfile(?Profile $profile): static
    {
        $this->profile = $profile;

        return $this;
    }

    public function getOrganization(): ?Organization
    {
        return $this->organization;
    }

    public function setOrganization(?Organization $organization): static
    {
        $this->organization = $organization;

        return $this;
    }

    public function getCustomFields(): array
    {
        return $this->customFields;
    }

    public function setCustomFields(array $customFields): static
    {
        $this->customFields = $customFields;

        return $this;
    }

    public function addOrUpdateCustomField(mixed $key, mixed $value): static {
        $updated = false;

        foreach ($this->customFields as &$_cf) {
            if ($_cf['key'] == $key) {
                $_cf['value'] = $value;
                $updated = true;
                break;
            }
        }

        if (!$updated) {
            $this->customFields[] = [
                'key' => $key,
                'value' => $value
            ];
        }

        return $this;
    }

    /**
     * @return Collection<int, CustomField>
     */
    public function getOrganizationCustomFields(): Collection
    {
        return $this->organizationCustomFields;
    }

    public function addOrganizationCustomField(CustomField $organizationCustomField): static
    {
        if (!$this->organizationCustomFields->contains($organizationCustomField)) {
            $this->organizationCustomFields->add($organizationCustomField);
        }

        return $this;
    }

    public function removeOrganizationCustomField(CustomField $organizationCustomField): static
    {
        $this->organizationCustomFields->removeElement($organizationCustomField);

        return $this;
    }
}
