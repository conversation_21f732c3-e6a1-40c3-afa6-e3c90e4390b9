<?php

namespace App\Entity\Meta;

use App\Entity\DateTrait;
use App\Repository\Meta\CommentRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Serializer\Attribute\Ignore;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: CommentRepository::class)]
#[ORM\Table(name: 'meta_comment')]
#[ORM\Index(name: 'meta_comment_idx', columns: ['meta_id'])]
class Comment
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(type: Types::TEXT, unique: true)]
    private ?string $metaId = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $userMetaId = null;

    #[ORM\Column(type: 'json_document')]
    private mixed $data = null;

    #[ORM\ManyToOne(inversedBy: 'comments')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Media $media = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getMetaId(): ?string
    {
        return $this->metaId;
    }

    public function setMetaId(string $metaId): static
    {
        $this->metaId = $metaId;

        return $this;
    }

    public function getUserMetaId(): ?string
    {
        return $this->userMetaId;
    }

    public function setUserMetaId(string $userMetaId): static
    {
        $this->userMetaId = $userMetaId;

        return $this;
    }

    public function getData(): mixed
    {
        return $this->data;
    }

    public function setData(mixed $data): static
    {
        $this->data = $data;

        return $this;
    }

    #[Ignore]
    public function getMedia(): ?Media
    {
        return $this->media;
    }

    public function setMedia(?Media $media): static
    {
        $this->media = $media;

        return $this;
    }

    use DateTrait;
}
