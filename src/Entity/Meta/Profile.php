<?php
namespace App\Entity\Meta;

use App\Entity\DateTrait;
use App\Entity\Meta\Profile\History;
use App\Repository\Meta\ProfileRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Du<PERSON>las\DoctrineJsonOdm\Type\JsonDocumentType;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: ProfileRepository::class)]
#[ORM\Table(name: 'meta_profile')]
#[ORM\Index(name: 'meta_profile_idx', columns: ['meta_id'])]
#[ORM\HasLifecycleCallbacks]
class Profile
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(type: Types::TEXT, unique: true)]
    private ?string $metaId = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $username = null;

    #[ORM\Column(length: 40)]
    private ?string $platform = null;

    #[ORM\Column(type: JsonDocumentType::NAME, options: ['jsonb' => true])]
    private mixed $data = null;

    /**
     * @var Collection<int, Media>
     */
    #[ORM\OneToMany(targetEntity: Media::class, mappedBy: 'profile', cascade: ['persist', 'remove', 'detach'], orphanRemoval: true)]
    private Collection $media;

    /**
     * @var Collection<int, History>
     */
    #[ORM\OneToMany(targetEntity: History::class, mappedBy: 'profile', cascade: ['persist', 'remove', 'detach'], orphanRemoval: true)]
    private Collection $history;

    public function __construct()
    {
        $this->media = new ArrayCollection();
        $this->history = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getMetaId(): ?string
    {
        return $this->metaId;
    }

    public function setMetaId(string $metaId): static
    {
        $this->metaId = $metaId;

        return $this;
    }

    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(string $username): static
    {
        $this->username = $username;

        return $this;
    }

    public function getPlatform(): ?string
    {
        return $this->platform;
    }

    public function setPlatform(?string $platform): static
    {
        $this->platform = $platform;

        return $this;
    }

    public function getData(): ?array
    {
        return $this->data;
    }

    public function setData(?array $data): static
    {
        $this->data = $data;

        return $this;
    }

    /**
     * @return Collection<int, Media>
     */
    public function getMedia(): Collection
    {
        return $this->media;
    }

    public function addMedia(Media $media): static
    {
        if (!$this->media->contains($media)) {
            $this->media->add($media);
            $media->setProfile($this);
        }

        return $this;
    }

    public function removeMedia(Media $media): static
    {
        if ($this->media->removeElement($media)) {
            // set the owning side to null (unless already changed)
            if ($media->getProfile() === $this) {
                $media->setProfile(null);
            }
        }

        return $this;
    }

    use DateTrait;

    /**
     * @return Collection<int, History>
     */
    public function getHistory(): Collection
    {
        return $this->history;
    }

    public function addHistory(History $history): static
    {
        if (!$this->history->contains($history)) {
            $this->history->add($history);
            $history->setProfile($this);
        }

        return $this;
    }

    public function removeHistory(History $history): static
    {
        if ($this->history->removeElement($history)) {
            // set the owning side to null (unless already changed)
            if ($history->getProfile() === $this) {
                $history->setProfile(null);
            }
        }

        return $this;
    }
}
