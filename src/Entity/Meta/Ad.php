<?php

namespace App\Entity\Meta;

use App\Entity\DateTrait;
use App\Repository\Meta\AdRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: AdRepository::class)]
#[ORM\Table(name: 'meta_ad')]
#[ORM\Index(columns: ['meta_id'])]
#[ORM\Index(columns: ['page_meta_id'])]
#[ORM\HasLifecycleCallbacks]
class Ad
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(type: Types::TEXT, unique: true)]
    private ?string $metaId = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $pageMetaId = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $type = null;

    #[ORM\Column]
    private array $platforms = [];

    #[ORM\Column]
    private array $data = [];

    use DateTrait;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getMetaId(): ?string
    {
        return $this->metaId;
    }

    public function setMetaId(string $metaId): static
    {
        $this->metaId = $metaId;

        return $this;
    }

    public function getPageMetaId(): ?string
    {
        return $this->pageMetaId;
    }

    public function setPageMetaId(?string $pageMetaId): static
    {
        $this->pageMetaId = $pageMetaId;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function setType(?string $type): static
    {
        $this->type = $type;

        return $this;
    }

    public function getPlatforms(): array
    {
        return $this->platforms;
    }

    public function setPlatforms(array $platforms): static
    {
        $this->platforms = $platforms;

        return $this;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }
}
