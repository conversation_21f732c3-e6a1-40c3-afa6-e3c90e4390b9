<?php
namespace App\Entity;

use App\Entity\Social\ProfileTracking;
use App\Entity\Social\TrackingGroup;
use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\Table(name: '`user`')]
#[ORM\UniqueConstraint(name: 'UNIQ_IDENTIFIER_EMAIL', fields: ['email'])]
#[ORM\HasLifecycleCallbacks]
#[UniqueEntity(fields: ['email'], message: 'There is already an account with this email')]
class User implements UserInterface, PasswordAuthenticatedUserInterface, \KevinPapst\TablerBundle\Model\UserInterface
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(length: 255)]
    private ?string $name = null;

    #[ORM\Column(length: 180)]
    private ?string $email = null;

    /**
     * @var list<string> The user roles
     */
    #[ORM\Column]
    private array $roles = [];

    /**
     * @var ?string The hashed password
     */
    #[ORM\Column]
    private ?string $password = null;

    private ?string $plainPassword = null;

    /**
     * @var Collection<int, OrganizationUser>
     */
    #[ORM\OneToMany(targetEntity: OrganizationUser::class, mappedBy: 'user', cascade: ['persist', 'remove'], orphanRemoval: true)]
    private Collection $organizationUsers;

    #[ORM\Column]
    private bool $isVerified = false;

    #[ORM\Column(options: ['default' => true])]
    private bool $isEnabled = true;

    /**
     * @var Collection<int, TrackingGroup>
     */
    #[ORM\OneToMany(targetEntity: TrackingGroup::class, mappedBy: 'createdBy')]
    private Collection $socialTrackingGroups;

    /**
     * @var Collection<int, ProfileTracking>
     */
    #[ORM\OneToMany(targetEntity: ProfileTracking::class, mappedBy: 'createdBy')]
    private Collection $profileTrackings;

    public function __construct()
    {
        $this->organizationUsers = new ArrayCollection();
        $this->socialTrackingGroups = new ArrayCollection();
        $this->profileTrackings = new ArrayCollection();
    }

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getName(): string
    {
        return $this->name ?? '';
    }

    public function setName(?string $name): static
    {
        $this->name = $name;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;

        return $this;
    }

    /**
     * A visual identifier that represents this user.
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    /**
     * @see UserInterface
     *
     * @return list<string>
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';

        return array_unique($roles);
    }

    /**
     * @param list<string> $roles
     */
    public function setRoles(array $roles): static
    {
        $this->roles = $roles;

        return $this;
    }

    /**
     * @see PasswordAuthenticatedUserInterface
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(string $password): static
    {
        $this->password = $password;

        return $this;
    }

    public function getPlainPassword(): ?string
    {
        return $this->plainPassword;
    }

    public function setPlainPassword(?string $plainPassword): static
    {
        $this->plainPassword = $plainPassword;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        $this->plainPassword = null;
    }

    /**
     * @return Collection<int, OrganizationUser>
     */
    public function getOrganizationUsers(): Collection
    {
        return $this->organizationUsers;
    }

    public function addOrganizationUser(OrganizationUser $organizationUser): static
    {
        if (!$this->organizationUsers->contains($organizationUser)) {
            $this->organizationUsers->add($organizationUser);
            $organizationUser->setUser($this);
        }

        return $this;
    }

    public function removeOrganizationUser(OrganizationUser $organizationUser): static
    {
        if ($this->organizationUsers->removeElement($organizationUser)) {
            // set the owning side to null (unless already changed)
            if ($organizationUser->getUser() === $this) {
                $organizationUser->setUser(null);
            }
        }

        return $this;
    }

    use DateTrait;

    public function isVerified(): bool
    {
        return $this->isVerified;
    }

    public function setIsVerified(bool $isVerified): static
    {
        $this->isVerified = $isVerified;

        return $this;
    }

    public function isEnabled(): bool
    {
        return $this->isEnabled;
    }

    public function setIsEnabled(bool $isEnabled): static
    {
        $this->isEnabled = $isEnabled;

        return $this;
    }

    public function toggleIsEnabled(): static
    {
        $this->isEnabled = !$this->isEnabled;

        return $this;
    }

    public function getTitle(): ?string
    {
        return '';
    }

    public function getAvatar(): ?string
    {
        return '';
    }

    /**
     * @return Collection<int, TrackingGroup>
     */
    public function getSocialTrackingGroups(): Collection
    {
        return $this->socialTrackingGroups;
    }

    public function addSocialTrackingGroup(TrackingGroup $socialTrackingGroup): static
    {
        if (!$this->socialTrackingGroups->contains($socialTrackingGroup)) {
            $this->socialTrackingGroups->add($socialTrackingGroup);
            $socialTrackingGroup->setCreatedBy($this);
        }

        return $this;
    }

    public function removeSocialTrackingGroup(TrackingGroup $socialTrackingGroup): static
    {
        if ($this->socialTrackingGroups->removeElement($socialTrackingGroup)) {
            // set the owning side to null (unless already changed)
            if ($socialTrackingGroup->getCreatedBy() === $this) {
                $socialTrackingGroup->setCreatedBy(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ProfileTracking>
     */
    public function getProfileTrackings(): Collection
    {
        return $this->profileTrackings;
    }

    public function addProfileTracking(ProfileTracking $profileTracking): static
    {
        if (!$this->profileTrackings->contains($profileTracking)) {
            $this->profileTrackings->add($profileTracking);
            $profileTracking->setCreatedBy($this);
        }

        return $this;
    }

    public function removeProfileTracking(ProfileTracking $profileTracking): static
    {
        if ($this->profileTrackings->removeElement($profileTracking)) {
            // set the owning side to null (unless already changed)
            if ($profileTracking->getCreatedBy() === $this) {
                $profileTracking->setCreatedBy(null);
            }
        }

        return $this;
    }
}
