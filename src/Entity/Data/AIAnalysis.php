<?php
namespace App\Entity\Data;

use App\Entity\DateTrait;
use App\Repository\Data\AIAnalysisRepository;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Types\UuidType;
use Symfony\Component\Uid\Uuid;

#[ORM\Entity(repositoryClass: AIAnalysisRepository::class)]
#[ORM\Index(columns: ['entity_id', 'entity_type'])]
#[ORM\Table(name: 'data_ai_analysis')]
#[ORM\HasLifecycleCallbacks]
class AIAnalysis
{
    #[ORM\Id]
    #[ORM\Column(type: UuidType::NAME, unique: true)]
    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: 'doctrine.uuid_generator')]
    private ?Uuid $id = null;

    #[ORM\Column(type: 'uuid')]
    private ?Uuid $entityId = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $entityType = null;

    #[ORM\Column(type: Types::TEXT)]
    private ?string $result = null;

    #[ORM\Column(nullable: true)]
    private ?array $data = null;

    public function getId(): ?Uuid
    {
        return $this->id;
    }

    public function getEntityId(): ?Uuid
    {
        return $this->entityId;
    }

    public function setEntityId(Uuid $entityId): static
    {
        $this->entityId = $entityId;

        return $this;
    }

    public function getEntityType(): ?string
    {
        return $this->entityType;
    }

    public function setEntityType(string $entityType): static
    {
        $this->entityType = $entityType;

        return $this;
    }

    public function getResult(): ?string
    {
        return $this->result;
    }

    public function setResult(string $result): static
    {
        $this->result = $result;

        return $this;
    }

    use DateTrait;

    public function getData(): ?array
    {
        return $this->data;
    }

    public function setData(?array $data): static
    {
        $this->data = $data;

        return $this;
    }
}
