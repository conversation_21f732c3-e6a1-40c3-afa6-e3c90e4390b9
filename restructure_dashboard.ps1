# PowerShell script to restructure the dashboard files
# This script will replace the old dashboard templates with the new modern ones

# Set the working directory to the project root
$projectRoot = Get-Location

# Create a backup directory for the old files
$backupDir = Join-Path $projectRoot "backup_old_dashboard"
if (-not (Test-Path $backupDir)) {
    New-Item -ItemType Directory -Path $backupDir | Out-Null
    Write-Host "Created backup directory: $backupDir"
}

# Function to backup a file before replacing it
function Backup-File {
    param (
        [string]$FilePath
    )
    
    if (Test-Path $FilePath) {
        $fileName = Split-Path $FilePath -Leaf
        $backupPath = Join-Path $backupDir $fileName
        
        # If the file already exists in the backup directory, append a timestamp
        if (Test-Path $backupPath) {
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $backupPath = Join-Path $backupDir "$($fileName)_$timestamp"
        }
        
        Copy-Item -Path $FilePath -Destination $backupPath
        Write-Host "Backed up $FilePath to $backupPath"
    }
}

# Step 1: Move the modern dashboard base template to replace the old one
$oldBaseTemplate = Join-Path $projectRoot "templates\app\dashboard\page.html.twig"
$newBaseTemplate = Join-Path $projectRoot "templates\app\modern_dashboard\base.html.twig"
$targetBaseTemplate = Join-Path $projectRoot "templates\app\dashboard\page.html.twig"

Backup-File -FilePath $oldBaseTemplate
Copy-Item -Path $newBaseTemplate -Destination $targetBaseTemplate -Force
Write-Host "Replaced base template: $oldBaseTemplate with $newBaseTemplate"

# Step 2: Move the modern dashboard index template to replace the old one
$oldIndexTemplate = Join-Path $projectRoot "templates\app\dashboard\index.html.twig"
$newIndexTemplate = Join-Path $projectRoot "templates\app\modern_dashboard\index.html.twig"
$targetIndexTemplate = Join-Path $projectRoot "templates\app\dashboard\index.html.twig"

Backup-File -FilePath $oldIndexTemplate
Copy-Item -Path $newIndexTemplate -Destination $targetIndexTemplate -Force
Write-Host "Replaced index template: $oldIndexTemplate with $newIndexTemplate"

# Step 3: Move the modern tracking groups template to replace the old one
$oldTrackingGroupsTemplate = Join-Path $projectRoot "templates\app\social\profile_tracking\index.html.twig"
$newTrackingGroupsTemplate = Join-Path $projectRoot "templates\app\modern_dashboard\tracking_groups.html.twig"
$targetTrackingGroupsTemplate = Join-Path $projectRoot "templates\app\social\profile_tracking\index.html.twig"

Backup-File -FilePath $oldTrackingGroupsTemplate
Copy-Item -Path $newTrackingGroupsTemplate -Destination $targetTrackingGroupsTemplate -Force
Write-Host "Replaced tracking groups template: $oldTrackingGroupsTemplate with $newTrackingGroupsTemplate"

# Step 4: Move the modern profiles template to replace the old one
$oldProfilesTemplate = Join-Path $projectRoot "templates\app\social\profile\index.html.twig"
$newProfilesTemplate = Join-Path $projectRoot "templates\app\modern_dashboard\profiles.html.twig"
$targetProfilesTemplate = Join-Path $projectRoot "templates\app\social\profile\index.html.twig"

# Create the directory if it doesn't exist
$targetProfilesDir = Split-Path $targetProfilesTemplate -Parent
if (-not (Test-Path $targetProfilesDir)) {
    New-Item -ItemType Directory -Path $targetProfilesDir | Out-Null
    Write-Host "Created directory: $targetProfilesDir"
}

Backup-File -FilePath $oldProfilesTemplate
Copy-Item -Path $newProfilesTemplate -Destination $targetProfilesTemplate -Force
Write-Host "Replaced profiles template: $oldProfilesTemplate with $newProfilesTemplate"

# Step 5: Move the modern tracking group wizard template to replace the old one
$oldWizardTemplate = Join-Path $projectRoot "templates\app\components\modal\add_tracking_group.html.twig"
$newWizardTemplate = Join-Path $projectRoot "templates\app\modern_dashboard\tracking_group\wizard.html.twig"
$targetWizardTemplate = Join-Path $projectRoot "templates\app\social\profile_tracking\wizard.html.twig"

# Create the directory if it doesn't exist
$targetWizardDir = Split-Path $targetWizardTemplate -Parent
if (-not (Test-Path $targetWizardDir)) {
    New-Item -ItemType Directory -Path $targetWizardDir | Out-Null
    Write-Host "Created directory: $targetWizardDir"
}

Backup-File -FilePath $oldWizardTemplate
Copy-Item -Path $newWizardTemplate -Destination $targetWizardTemplate -Force
Write-Host "Replaced wizard template: $oldWizardTemplate with $newWizardTemplate"

# Step 6: Copy the modern components to the components directory
$modernComponentsDir = Join-Path $projectRoot "templates\components\Modern"
$targetComponentsDir = Join-Path $projectRoot "templates\components"

if (Test-Path $modernComponentsDir) {
    $modernComponents = Get-ChildItem -Path $modernComponentsDir -File
    foreach ($component in $modernComponents) {
        $targetPath = Join-Path $targetComponentsDir $component.Name
        Copy-Item -Path $component.FullName -Destination $targetPath -Force
        Write-Host "Copied component: $($component.Name) to $targetPath"
    }
}

# Step 7: Update the controllers to use the new templates
# This will be done manually as it requires more complex changes

Write-Host "File restructuring completed successfully!"
Write-Host "Please update the controllers manually to use the new template paths."
