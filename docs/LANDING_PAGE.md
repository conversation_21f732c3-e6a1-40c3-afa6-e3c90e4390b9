# Landing Page Documentation

This document provides information about the landing page implementation using Tailwind CSS and the Symfony TailwindBundle.

## Overview

The landing page is built using:
- Tailwind CSS for styling
- Preline UI for components
- AOS (Animate On Scroll) for animations
- Custom JavaScript for interactive elements

## File Structure

```
templates/landing/
├── index.html.twig                # Main landing page template
├── partials/                      # Partial templates for each section
│   ├── _header.html.twig          # Header with navigation
│   ├── _hero.html.twig            # Hero section
│   ├── _clients.html.twig         # Client logos section
│   ├── _features.html.twig        # Features section
│   ├── _usecases.html.twig        # Use cases section
│   ├── _howitworks.html.twig      # How it works section
│   ├── _testimonials.html.twig    # Testimonials section
│   ├── _pricing.html.twig         # Pricing section
│   ├── _faq.html.twig             # FAQ section
│   ├── _cta.html.twig             # Call to action section
│   └── _footer.html.twig          # Footer section

assets/
├── landing.js                     # JavaScript for the landing page
├── styles/
│   └── landing.tailwind.css       # Tailwind CSS for the landing page
├── images/
│   ├── hero-pattern.svg           # Background pattern for hero section
│   ├── dashboard-illustration.svg # Dashboard illustration
│   ├── logo.svg                   # Logo
│   ├── logo-white.svg             # White logo for dark backgrounds
│   └── icons/                     # Icons for features and use cases
```

## Tailwind CSS Configuration

The landing page uses a separate Tailwind CSS entry point to avoid conflicts with the dashboard styles. The configuration is defined in `tailwind.config.js` and includes:

- Custom colors
- Custom fonts (Inter and Lexend)
- Custom animations
- Custom components
- Responsive design utilities

## JavaScript Components

The landing page includes several JavaScript components:

- **Preline UI**: For interactive components like accordions, dropdowns, etc.
- **AOS (Animate On Scroll)**: For scroll animations
- **Counter Animation**: For animated statistics
- **Navbar Scroll Behavior**: For changing the navbar appearance on scroll

## Customization

### Colors

The primary color scheme can be customized in the `tailwind.config.js` file:

```js
colors: {
  primary: {
    50: '#eef2ff',
    100: '#e0e7ff',
    // ...
  },
  // ...
}
```

### Fonts

The fonts can be customized in the `tailwind.config.js` file:

```js
fontFamily: {
  sans: ['Inter', 'ui-sans-serif', 'system-ui', ...],
  display: ['Lexend', 'ui-sans-serif', 'system-ui', ...],
}
```

### Sections

Each section is in its own partial template, making it easy to add, remove, or modify sections without affecting the rest of the page.

## Translations

All text content is stored in translation files:

```
translations/messages+intl-icu.{locale}.yaml
```

This allows for easy localization of the landing page.

## Responsive Design

The landing page is fully responsive and works well on all screen sizes:

- Mobile: < 640px
- Tablet: 640px - 1024px
- Desktop: > 1024px

## Performance Considerations

- Images are optimized SVGs for faster loading
- Tailwind CSS is purged in production to reduce file size
- JavaScript is modular and only loaded when needed
- AOS animations are configured to run only once

## Browser Compatibility

The landing page is compatible with all modern browsers:

- Chrome
- Firefox
- Safari
- Edge

## Maintenance

When updating the landing page:

1. Use the existing partial structure to maintain organization
2. Add new translations to the translation files
3. Follow the Tailwind CSS utility-first approach
4. Test on multiple screen sizes
5. Update this documentation if necessary

## Development

To work on the landing page:

1. Run the Tailwind CSS watcher:
   ```
   php bin/console tailwind:build --watch
   ```

2. Make changes to the templates and CSS
3. Refresh the browser to see changes
