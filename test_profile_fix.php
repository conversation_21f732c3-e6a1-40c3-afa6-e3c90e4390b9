<?php

// Simple test to verify the ProfileRepository fix
require_once __DIR__ . '/vendor/autoload.php';

use Symfony\Component\Dotenv\Dotenv;

$dotenv = new Dotenv();
$dotenv->load(__DIR__.'/.env');

// Create a simple test to check if the repository methods work
echo "Testing ProfileRepository fix...\n";

try {
    // Boot Symfony kernel
    $kernel = new \App\Kernel('dev', true);
    $kernel->boot();
    $container = $kernel->getContainer();
    
    // Get services
    $entityManager = $container->get('doctrine')->getManager();
    $profileRepository = $entityManager->getRepository(\App\Entity\Meta\Profile::class);
    $organizationRepository = $entityManager->getRepository(\App\Entity\Organization::class);
    
    // Get first organization for testing
    $organization = $organizationRepository->findOneBy([]);
    
    if (!$organization) {
        echo "❌ No organization found in database\n";
        exit(1);
    }
    
    echo "✅ Found organization: " . $organization->getName() . "\n";
    
    // Test 1: Basic pagination
    echo "\n=== Test 1: Basic Pagination ===\n";
    $result = $profileRepository->findProfilesWithPagination($organization, 1, 5);
    echo "✅ Basic pagination works - found " . $result['total'] . " profiles\n";
    
    // Test 2: Platform stats (this was causing GROUP BY error)
    echo "\n=== Test 2: Profile Stats ===\n";
    $stats = $profileRepository->getProfileStats($organization);
    echo "✅ Profile stats works - total: " . $stats['total'] . "\n";
    echo "Platform breakdown: " . json_encode($stats['by_platform']) . "\n";
    
    // Test 3: Available platforms (this was also causing GROUP BY error)
    echo "\n=== Test 3: Available Platforms ===\n";
    $platforms = $profileRepository->getAvailablePlatforms($organization);
    echo "✅ Available platforms works - platforms: " . json_encode($platforms) . "\n";
    
    // Test 4: Pagination with search
    echo "\n=== Test 4: Pagination with Search ===\n";
    $result = $profileRepository->findProfilesWithPagination($organization, 1, 5, 'test');
    echo "✅ Search pagination works - found " . $result['total'] . " profiles\n";
    
    // Test 5: Pagination with platform filter
    echo "\n=== Test 5: Pagination with Platform Filter ===\n";
    $result = $profileRepository->findProfilesWithPagination($organization, 1, 5, null, 'instagram');
    echo "✅ Platform filter pagination works - found " . $result['total'] . " profiles\n";
    
    // Test 6: Custom filters (most complex case)
    echo "\n=== Test 6: Custom Filters ===\n";
    $customFilters = [
        [
            'key' => 'test_field',
            'operator' => 'equals',
            'value' => 'test_value'
        ]
    ];
    $result = $profileRepository->findProfilesWithPagination($organization, 1, 5, null, null, null, $customFilters);
    echo "✅ Custom filters pagination works - found " . $result['total'] . " profiles\n";
    
    echo "\n🎉 All tests passed! The GROUP BY error has been fixed.\n";
    
} catch (\Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
