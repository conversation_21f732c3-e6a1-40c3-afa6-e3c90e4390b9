<?php

namespace App\Tests\Repository\Meta;

use App\Entity\Meta\Profile;
use App\Entity\Organization;
use App\Entity\Social\TrackingGroup;
use App\Repository\Meta\ProfileRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;
use Symfony\Component\Uid\Uuid;

class ProfileRepositoryTest extends KernelTestCase
{
    private EntityManagerInterface $entityManager;
    private ProfileRepository $profileRepository;
    private Organization $testOrganization;

    protected function setUp(): void
    {
        $kernel = self::bootKernel();
        $this->entityManager = $kernel->getContainer()->get('doctrine')->getManager();
        $this->profileRepository = $this->entityManager->getRepository(Profile::class);
        
        // Create test organization
        $this->testOrganization = new Organization();
        $this->testOrganization->setName('Test Organization');
        $this->testOrganization->setSlug('test-org');
        $this->entityManager->persist($this->testOrganization);
        $this->entityManager->flush();
    }

    public function testFindProfilesWithPaginationBasic(): void
    {
        echo "\n=== Testing Basic Pagination ===\n";
        
        try {
            $result = $this->profileRepository->findProfilesWithPagination(
                $this->testOrganization,
                1,
                20
            );
            
            echo "✅ Basic pagination test passed\n";
            echo "Result structure: " . json_encode(array_keys($result)) . "\n";
            echo "Total profiles: " . $result['total'] . "\n";
            
        } catch (\Exception $e) {
            echo "❌ Basic pagination test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    public function testFindProfilesWithPlatformFilter(): void
    {
        echo "\n=== Testing Platform Filter ===\n";
        
        try {
            $result = $this->profileRepository->findProfilesWithPagination(
                $this->testOrganization,
                1,
                20,
                null,
                'instagram'
            );
            
            echo "✅ Platform filter test passed\n";
            echo "Total profiles with platform filter: " . $result['total'] . "\n";
            
        } catch (\Exception $e) {
            echo "❌ Platform filter test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    public function testFindProfilesWithSearch(): void
    {
        echo "\n=== Testing Search Filter ===\n";
        
        try {
            $result = $this->profileRepository->findProfilesWithPagination(
                $this->testOrganization,
                1,
                20,
                'test'
            );
            
            echo "✅ Search filter test passed\n";
            echo "Total profiles with search: " . $result['total'] . "\n";
            
        } catch (\Exception $e) {
            echo "❌ Search filter test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    public function testFindProfilesWithCustomFilters(): void
    {
        echo "\n=== Testing Custom Filters ===\n";
        
        try {
            $customFilters = [
                [
                    'key' => 'test_field',
                    'operator' => 'equals',
                    'value' => 'test_value'
                ]
            ];
            
            $result = $this->profileRepository->findProfilesWithPagination(
                $this->testOrganization,
                1,
                20,
                null,
                null,
                null,
                $customFilters
            );
            
            echo "✅ Custom filters test passed\n";
            echo "Total profiles with custom filters: " . $result['total'] . "\n";
            
        } catch (\Exception $e) {
            echo "❌ Custom filters test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    public function testGetAvailablePlatforms(): void
    {
        echo "\n=== Testing Get Available Platforms ===\n";
        
        try {
            $platforms = $this->profileRepository->getAvailablePlatforms($this->testOrganization);
            
            echo "✅ Get available platforms test passed\n";
            echo "Available platforms: " . json_encode($platforms) . "\n";
            
        } catch (\Exception $e) {
            echo "❌ Get available platforms test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    public function testGetProfileStats(): void
    {
        echo "\n=== Testing Get Profile Stats ===\n";
        
        try {
            $stats = $this->profileRepository->getProfileStats($this->testOrganization);
            
            echo "✅ Get profile stats test passed\n";
            echo "Profile stats: " . json_encode($stats) . "\n";
            
        } catch (\Exception $e) {
            echo "❌ Get profile stats test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    public function testGetMetaIdsForOrganization(): void
    {
        echo "\n=== Testing Get Meta IDs for Organization ===\n";
        
        try {
            // Use reflection to access private method
            $reflection = new \ReflectionClass($this->profileRepository);
            $method = $reflection->getMethod('getMetaIdsForOrganization');
            $method->setAccessible(true);
            
            $metaIds = $method->invoke($this->profileRepository, $this->testOrganization);
            
            echo "✅ Get meta IDs test passed\n";
            echo "Meta IDs count: " . count($metaIds) . "\n";
            echo "Sample meta IDs: " . json_encode(array_slice($metaIds, 0, 5)) . "\n";
            
        } catch (\Exception $e) {
            echo "❌ Get meta IDs test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    public function testDirectDatabaseQuery(): void
    {
        echo "\n=== Testing Direct Database Query ===\n";
        
        try {
            // Test the exact query that might be causing issues
            $sql = "SELECT p.platform, COUNT(p.id) as count FROM meta_profile p GROUP BY p.platform ORDER BY count DESC LIMIT 5";
            
            $stmt = $this->entityManager->getConnection()->prepare($sql);
            $result = $stmt->executeQuery()->fetchAllAssociative();
            
            echo "✅ Direct database query test passed\n";
            echo "Direct query result: " . json_encode($result) . "\n";
            
        } catch (\Exception $e) {
            echo "❌ Direct database query test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    public function testProfileEntityQuery(): void
    {
        echo "\n=== Testing Profile Entity Query ===\n";
        
        try {
            // Test basic entity query
            $qb = $this->profileRepository->createQueryBuilder('p')
                ->select('p')
                ->setMaxResults(5);
            
            $profiles = $qb->getQuery()->getResult();
            
            echo "✅ Profile entity query test passed\n";
            echo "Profiles found: " . count($profiles) . "\n";
            
        } catch (\Exception $e) {
            echo "❌ Profile entity query test failed: " . $e->getMessage() . "\n";
            echo "Stack trace: " . $e->getTraceAsString() . "\n";
        }
    }

    protected function tearDown(): void
    {
        // Clean up test data
        if ($this->testOrganization && $this->entityManager) {
            try {
                $this->entityManager->remove($this->testOrganization);
                $this->entityManager->flush();
            } catch (\Exception $e) {
                // Ignore cleanup errors
            }
        }
        
        parent::tearDown();
    }
}
