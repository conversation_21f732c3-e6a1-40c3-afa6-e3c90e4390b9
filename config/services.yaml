# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
    Symfony\Component\Uid\Command\GenerateUuidCommand: ~

    App\Service\MetaService:
        arguments:
            $appId: '%env(APP_META_APP_ID)%'
            $appSecret: '%env(APP_META_APP_SECRET)%'
            $alternativeAccessToken: '%env(APP_META_ALTERNATIVE_TOKEN)%'

    App\Command\S3MigrateCommand:
        arguments:
            $bucket: '%env(AWS_DEFAULT_BUCKET)%'

    App\Controller\API\Social\TestController:
        arguments:
            $bucket: '%env(AWS_DEFAULT_BUCKET)%'

    App\Service\AWS\S3ProfileUploadService:
        arguments:
            $bucket: '%env(AWS_DEFAULT_BUCKET)%'