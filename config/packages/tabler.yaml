#
# Configuration file for the Tabler bundle
#
# For more information about the bundle settings visit:
# https://github.com/kevinpapst/TablerBundle/blob/master/docs/configurations.md
#
tabler:
    options:
        # true = dark mode (light colors on dark background), false = normal mode (dark colors on light background)
        dark_mode: true
        # for cache busting, can be dynamically changed with the ContextHelper
        asset_version: "beta19"
        # condensed = one nav-bar, false = two nav-bars
        navbar_condensed: true
        # true = dark mode, false = light mode (on header)
        header_dark: false
        # true = dark mode, false = light mode (on menu)
        navbar_dark: false
        # overlap = https://preview.tabler.io/layout-navbar-overlap.html
        navbar_overlap: false
        # true = boxed layout, false = full-screen
        boxed_layout: true
        # false = left to right, true = right to left
        rtl_mode: false
        # true = only avatar, false = show username and title as well
        user_menu_condensed: false
        # url to your company logo (if not with a protocol, the asset() helper will be used to create the url
        logo_url: https://terra.marketing/assets/img/logo.svg

    knp_menu:
        enable: false
        main_menu: tabler_main
        breadcrumb_menu: false

    routes:
        tabler_welcome: app_app_dashboard
        tabler_login: app_login
        tabler_login_check: app_login
        tabler_registration: app_register
        tabler_registration_register: app_register
        tabler_password_reset: app_forgot_password_request
        tabler_password_reset_sent: app_check_email

    icons:
        # used for the action_collapse button components
        collapse: fas fa-chevron-down
        # used for the password-reset template
        mail: far fa-envelope
        # used for the 404/500/... error page
        back: fas fa-long-arrow-alt-left
        # the following are only ideas to be used in your own templates
        about: fas fa-info-circle
        admin: fas fa-wrench
        bookmark: far fa-star
        calendar: far fa-calendar-alt
        clock: far fa-clock
        comment: far fa-comment
        copy: far fa-copy
        create: far fa-plus-square
        dashboard: fas fa-tachometer-alt
        delete: far fa-trash-alt
        download: fas fa-download
        edit: far fa-edit
        email: fa fa-at
        export: fas fa-file-export
        filter: fas fa-filter
        help: far fa-question-circle
        home: fas fa-home
        password: fas fa-key
        phone: fas fa-phone
        print: fas fa-print
        search: fas fa-search
        settings: fas fa-cog
        start: fas fa-play
        stop: fas fa-stop
        upload: fas fa-upload
        user: fas fa-user
