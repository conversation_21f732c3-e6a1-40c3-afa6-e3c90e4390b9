/* Dashboard Improvements */

/* Card hover effects */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-indicator.active {
    background-color: #2fb344;
    box-shadow: 0 0 0 3px rgba(47, 179, 68, 0.2);
}

.status-indicator.pending {
    background-color: #f59f00;
    box-shadow: 0 0 0 3px rgba(245, 159, 0, 0.2);
}

.status-indicator.completed {
    background-color: #206bc4;
    box-shadow: 0 0 0 3px rgba(32, 107, 196, 0.2);
}

.status-indicator.paused {
    background-color: #6e7680;
    box-shadow: 0 0 0 3px rgba(110, 118, 128, 0.2);
}

/* Improved progress bars */
.progress {
    height: 0.5rem;
    background-color: rgba(32, 107, 196, 0.1);
    margin-bottom: 1rem;
}

.progress-bar {
    border-radius: 0.25rem;
}

/* Card animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.row-cards .col-md-6,
.row-cards .col-lg-4 {
    animation: fadeIn 0.5s ease-out forwards;
}

.row-cards .col-md-6:nth-child(2),
.row-cards .col-lg-4:nth-child(2) {
    animation-delay: 0.1s;
}

.row-cards .col-md-6:nth-child(3),
.row-cards .col-lg-4:nth-child(3) {
    animation-delay: 0.2s;
}

/* Stats cards */
.stats-card {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.stats-card .card-body {
    padding: 1.5rem;
}

.stats-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.5rem;
    margin-right: 1rem;
}

/* Filter buttons */
.filter-buttons {
    margin-bottom: 1.5rem;
}

.filter-buttons .btn {
    border-radius: 0.25rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

/* Empty state improvements */
.empty {
    padding: 3rem 0;
}

.empty-img img {
    max-width: 200px;
    margin-bottom: 1.5rem;
}

.empty-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-subtitle {
    max-width: 400px;
    margin: 0 auto 1.5rem;
}

/* Accessibility improvements */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .stats-card .card-body {
        padding: 1rem;
    }
    
    .stats-icon {
        width: 40px;
        height: 40px;
    }
}
