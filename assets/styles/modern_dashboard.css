@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base styles */
@layer base {
  [data-theme="dark"] {
    color-scheme: dark;
  }
  
  [data-theme="light"] {
    color-scheme: light;
  }
  
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply font-sans antialiased;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium;
  }
}

/* Custom components */
@layer components {
  /* Button styles */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply text-white bg-primary-600 hover:bg-primary-700 focus:ring-primary-500 dark:bg-primary-600 dark:hover:bg-primary-700 dark:focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply text-primary-700 bg-primary-100 hover:bg-primary-200 focus:ring-primary-500 dark:text-primary-300 dark:bg-primary-900 dark:hover:bg-primary-800 dark:focus:ring-primary-500;
  }
  
  .btn-outline {
    @apply text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-primary-500 dark:text-gray-300 dark:bg-gray-800 dark:border-gray-600 dark:hover:bg-gray-700 dark:focus:ring-primary-500;
  }
  
  .btn-danger {
    @apply text-white bg-red-600 hover:bg-red-700 focus:ring-red-500 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-500;
  }
  
  /* Card styles */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 transition-all duration-200 hover:shadow-md;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700;
  }
  
  .card-body {
    @apply p-6;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700;
  }
  
  /* Form styles */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  }
  
  .form-select {
    @apply block w-full pl-3 pr-10 py-2 text-base border border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white;
  }
  
  .form-checkbox {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700;
  }
  
  .form-radio {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300;
  }
  
  .badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300;
  }
  
  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300;
  }
  
  .badge-danger {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300;
  }
  
  .badge-info {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300;
  }
  
  /* Table styles */
  .table {
    @apply min-w-full divide-y divide-gray-200 dark:divide-gray-700;
  }
  
  .table-header {
    @apply bg-gray-50 dark:bg-gray-700;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700;
  }
  
  .table-row {
    @apply hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors duration-150;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400;
  }
}

/* Custom utilities */
@layer utilities {
  .text-shadow {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-none {
    text-shadow: none;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Dark mode specific styles */
[data-theme="dark"] .bg-gray-750 {
  background-color: #2d3748;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .card-header, .card-body, .card-footer {
    @apply px-4 py-3;
  }
}
