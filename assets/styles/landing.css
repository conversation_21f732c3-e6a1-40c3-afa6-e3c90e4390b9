@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the landing page */
body {
  @apply bg-white text-gray-900;
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply font-sans text-secondary-800 antialiased;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-bold text-secondary-900;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-full font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-white text-primary-600 border border-primary-200 hover:bg-primary-50 focus:ring-primary-500;
  }

  .btn-white {
    @apply bg-white text-secondary-900 hover:bg-secondary-50 focus:ring-secondary-200;
  }

  .btn-lg {
    @apply px-8 py-4 text-lg;
  }

  .btn-sm {
    @apply px-4 py-2 text-sm;
  }

  .section {
    @apply py-16 md:py-24;
  }

  .section-title {
    @apply text-3xl md:text-4xl lg:text-5xl font-bold mb-4;
  }

  .section-subtitle {
    @apply text-xl text-secondary-600 max-w-3xl mx-auto mb-12;
  }

  .card {
    @apply bg-white rounded-2xl shadow-soft-xl transition-all duration-300;
  }

  .card-hover {
    @apply hover:shadow-soft-2xl hover:-translate-y-2;
  }

  .feature-icon {
    @apply w-16 h-16 flex items-center justify-center rounded-2xl text-2xl mb-6;
  }

  .gradient-text {
    @apply bg-clip-text text-transparent bg-gradient-to-r;
  }
}

/* Custom animations */
[data-aos="fade-up-sm"] {
  transform: translate3d(0, 20px, 0);
  opacity: 0;
  transition-property: transform, opacity;
}

[data-aos="fade-up-sm"].aos-animate {
  transform: translate3d(0, 0, 0);
  opacity: 1;
}

/* Hero section */
.hero-bg {
  background: linear-gradient(to bottom right, rgba(99, 102, 241, 0.8), rgba(168, 85, 247, 0.8)), url('/assets/images/hero-pattern.svg');
  background-size: cover;
  background-position: center;
}

/* Increase hero height */
.hero-section {
  min-height: 100vh;
  padding-top: 8rem;
  padding-bottom: 8rem;
}

/* Fix header contrast issues */
.header-scrolled .nav-link-light {
  @apply text-gray-700;
}

.header-scrolled .btn-login {
  @apply text-primary-600 border border-primary-200 bg-white hover:bg-primary-50;
}

/* Blob animations */
.blob {
  animation: blob-animation 25s infinite alternate;
  filter: blur(40px);
}

.blob-1 {
  animation-delay: -5s;
}

.blob-2 {
  animation-delay: -10s;
}

.blob-3 {
  animation-delay: -15s;
}

/* Adjust CTA section height and position */
.cta-section {
  padding-top: 6rem;
  padding-bottom: 6rem;
  transform: skew(0deg, -2deg) translateY(-2rem);
}

.cta-content {
  transform: skew(0deg, 2deg);
}

@keyframes blob-animation {
  0% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
  100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
}
