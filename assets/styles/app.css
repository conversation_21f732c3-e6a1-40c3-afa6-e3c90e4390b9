/* Language Switcher Styles */
.language-switcher img {
    display: inline-block;
    vertical-align: middle;
    margin-right: 0.5rem;
}

/* Ensure the dropdown is above other elements */
.language-switcher .absolute {
    z-index: 100;
}

/* Language Switcher Styles for Landing Page */
.language-switcher-landing button {
    background-color: transparent;
    border-color: rgba(255, 255, 255, 0.3);
    color: white;
    min-width: 120px; /* Ensure consistent width */
}

.language-switcher-landing button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Header Scroll Effect */
#header.header-scrolled .language-switcher-landing button,
#header.bg-white .language-switcher-landing button {
    color: #1f2937;
    border-color: rgba(31, 41, 55, 0.3);
    background-color: white; /* Ensure text is visible on white background */
}

#header.header-scrolled .language-switcher-landing button:hover,
#header.bg-white .language-switcher-landing button:hover {
    background-color: rgba(31, 41, 55, 0.05);
}

/* Fix menu overflow issues */
@media (min-width: 768px) {
    .md\:space-x-10 > * {
        margin-left: 2.5rem;
    }

    .md\:flex-1 {
        flex: 1 1 0%;
    }

    /* Ensure language switcher has enough space */
    .language-switcher-landing {
        min-width: 120px;
        margin-right: 0.5rem;
    }
}

/* Ensure language name is always visible */
.language-name {
    display: inline-block;
    min-width: 60px;
}

/* Fix for header when scrolled */
#header.header-scrolled .nav-link-light {
    color: #1f2937;
}