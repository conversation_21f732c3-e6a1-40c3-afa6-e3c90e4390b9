/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Ma<PERSON>ry Pro', sans-serif;
  color: #333;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Power Grotesk', sans-serif;
  font-weight: 700;
}

/* Custom components */
.btn-primary {
  background-color: #ff0000;
  color: white;
  font-weight: 700;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s;
}

.btn-primary:hover {
  background-color: #cc0000;
}

.btn-secondary {
  background-color: white;
  color: #0F051D;
  border: 1px solid #ff0000;
  font-weight: 700;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s;
}

.btn-secondary:hover {
  background-color: #f8f8f8;
}

.section-title {
  font-size: 2.5rem;
  font-family: 'Power Grotesk', sans-serif;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
}

@media (min-width: 768px) {
  .section-title {
    font-size: 3rem;
  }
}

.section-subtitle {
  font-size: 1.25rem;
  color: #666;
  margin-bottom: 2.5rem;
  max-width: 48rem;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

@media (min-width: 768px) {
  .section-subtitle {
    font-size: 1.5rem;
  }
}

.feature-card {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.feature-card:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.container-custom {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 768px) {
  .container-custom {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container-custom {
    max-width: 1280px;
  }
}

.feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: 9999px;
  background-color: #ffeeee;
  color: #ff0000;
  margin-bottom: 1rem;
}

.testimonial-card {
  background-color: white;
  padding: 1.5rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ff0000;
}

.stats-card {
  background-color: white;
  padding: 2rem;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s;
}

.stats-card:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.stats-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ff0000;
  margin-bottom: 0.5rem;
}

@media (min-width: 768px) {
  .stats-number {
    font-size: 3rem;
  }
}

.process-step {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.process-step::before {
  content: '';
  position: absolute;
  top: 2rem;
  left: 50%;
  width: 100%;
  height: 0.125rem;
  background-color: #e5e5e5;
  transform: translateX(50%);
}

.process-step:last-child::before {
  display: none;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 4rem;
  height: 4rem;
  border-radius: 9999px;
  background-color: #ff0000;
  color: white;
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  z-index: 10;
}

.gradient-bg {
  background: linear-gradient(135deg, rgba(255,0,0,0.05) 0%, rgba(255,255,255,0) 100%);
}

/* Utility classes */
.hidden {
  display: none;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

.space-x-6 > * + * {
  margin-left: 1.5rem;
}

.space-x-8 > * + * {
  margin-left: 2rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.space-y-8 > * + * {
  margin-top: 2rem;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-white {
  color: white;
}

.text-gray-600 {
  color: #666;
}

.text-gray-500 {
  color: #888;
}

.text-postchat-red {
  color: #ff0000;
}

.bg-white {
  background-color: white;
}

.bg-postchat-red {
  background-color: #ff0000;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-900 {
  background-color: #111827;
}

.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.pt-32 {
  padding-top: 8rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.mt-16 {
  margin-top: 4rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mr-4 {
  margin-right: 1rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-12 {
  gap: 3rem;
}

.gap-16 {
  gap: 4rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-full {
  border-radius: 9999px;
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.transition-colors {
  transition-property: color, background-color, border-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.rotate-180 {
  transform: rotate(180deg);
}

.transform {
  transform: translateZ(0);
}

.hover\:bg-red-700:hover {
  background-color: #b91c1c;
}

.hover\:bg-gray-100:hover {
  background-color: #f3f4f6;
}

.hover\:bg-gray-900:hover {
  background-color: #111827;
}

.hover\:text-white:hover {
  color: white;
}

.hover\:text-postchat-red:hover {
  color: #ff0000;
}

.hover\:shadow-xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.text-xl {
  font-size: 1.25rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.text-3xl {
  font-size: 1.875rem;
}

.text-4xl {
  font-size: 2.25rem;
}

.text-5xl {
  font-size: 3rem;
}

.text-6xl {
  font-size: 3.75rem;
}

.text-xs {
  font-size: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
}

.leading-tight {
  line-height: 1.25;
}

.font-power-grotesk {
  font-family: 'Power Grotesk', sans-serif;
}

.font-mabry-pro {
  font-family: 'Mabry Pro', sans-serif;
}

.w-full {
  width: 100%;
}

.h-auto {
  height: auto;
}

.w-10 {
  width: 2.5rem;
}

.h-10 {
  height: 2.5rem;
}

.w-16 {
  width: 4rem;
}

.h-16 {
  height: 4rem;
}

.w-8 {
  width: 2rem;
}

.h-8 {
  height: 2rem;
}

.h-12 {
  height: 3rem;
}

.h-20 {
  height: 5rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-lg {
  max-width: 32rem;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.z-10 {
  z-index: 10;
}

.z-50 {
  z-index: 50;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.top-0 {
  top: 0;
}

.right-0 {
  right: 0;
}

.bottom-0 {
  bottom: 0;
}

.left-0 {
  left: 0;
}

.top-8 {
  top: 2rem;
}

.left-1\/2 {
  left: 50%;
}

.-top-2 {
  top: -0.5rem;
}

.-right-2 {
  right: -0.5rem;
}

.overflow-hidden {
  overflow: hidden;
}

.border {
  border-width: 1px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-t {
  border-top-width: 1px;
}

.border-postchat-red {
  border-color: #ff0000;
}

.border-gray-300 {
  border-color: #d1d5db;
}

.border-gray-800 {
  border-color: #1f2937;
}

/* Grid system */
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:pt-40 {
    padding-top: 10rem;
  }

  .md\:pb-24 {
    padding-bottom: 6rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
  }

  .md\:mt-0 {
    margin-top: 0;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
  }
}
