@font-face {
  font-family: 'Unicons';
  src:  url('../fonts/unicons/Unicons.eot?lkolxg');
  src:  url('../fonts/unicons/Unicons.eot?lkolxg#iefix') format('embedded-opentype'),
    url('../fonts/unicons/Unicons.ttf?lkolxg') format('truetype'),
    url('../fonts/unicons/Unicons.woff?lkolxg') format('woff'),
    url('../fonts/unicons/Unicons.svg?lkolxg#Unicons') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="unicon-"], [class*=" unicon-"] {
  font-family: 'Unicons' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.unicon-account:before {
  content: "\e900";
}
.unicon-activity:before {
  content: "\e901";
}
.unicon-add-alt:before {
  content: "\e902";
}
.unicon-add:before {
  content: "\e903";
}
.unicon-airplay-filled:before {
  content: "\e904";
}
.unicon-airplay:before {
  content: "\e905";
}
.unicon-airport-location:before {
  content: "\e906";
}
.unicon-analytics:before {
  content: "\e907";
}
.unicon-api:before {
  content: "\e908";
}
.unicon-archive:before {
  content: "\e909";
}
.unicon-area:before {
  content: "\e90a";
}
.unicon-arrow-down:before {
  content: "\e90b";
}
.unicon-arrow-left:before {
  content: "\e90c";
}
.unicon-arrow-right:before {
  content: "\e90d";
}
.unicon-arrow-up-right:before {
  content: "\e90e";
}
.unicon-arrow-up:before {
  content: "\e90f";
}
.unicon-asleep:before {
  content: "\e910";
}
.unicon-attachment:before {
  content: "\e911";
}
.unicon-audio-console:before {
  content: "\e912";
}
.unicon-augmented-reality:before {
  content: "\e913";
}
.unicon-auto-scroll:before {
  content: "\e914";
}
.unicon-awake:before {
  content: "\e915";
}
.unicon-back-to-top:before {
  content: "\e916";
}
.unicon-bar:before {
  content: "\e917";
}
.unicon-barrier:before {
  content: "\e918";
}
.unicon-bastion-host:before {
  content: "\e919";
}
.unicon-blog:before {
  content: "\e91a";
}
.unicon-book:before {
  content: "\e91b";
}
.unicon-bookmark-add:before {
  content: "\e91c";
}
.unicon-bookmark:before {
  content: "\e91d";
}
.unicon-box:before {
  content: "\e91e";
}
.unicon-brightness-contrast:before {
  content: "\e91f";
}
.unicon-brush-freehand:before {
  content: "\e920";
}
.unicon-building:before {
  content: "\e921";
}
.unicon-calendar:before {
  content: "\e922";
}
.unicon-camera:before {
  content: "\e923";
}
.unicon-carbon:before {
  content: "\e924";
}
.unicon-caret-down:before {
  content: "\e925";
}
.unicon-caret-left:before {
  content: "\e926";
}
.unicon-caret-right:before {
  content: "\e927";
}
.unicon-caret-sort:before {
  content: "\e928";
}
.unicon-caret-up:before {
  content: "\e929";
}
.unicon-carousel-horizontal:before {
  content: "\e92a";
}
.unicon-carousel-vertical:before {
  content: "\e92b";
}
.unicon-categories:before {
  content: "\e92c";
}
.unicon-cd-archive:before {
  content: "\e92d";
}
.unicon-center-circle:before {
  content: "\e92e";
}
.unicon-center-to-fit:before {
  content: "\e92f";
}
.unicon-certificate:before {
  content: "\e930";
}
.unicon-chart-column:before {
  content: "\e931";
}
.unicon-chart-line-data:before {
  content: "\e932";
}
.unicon-chart-pie:before {
  content: "\e933";
}
.unicon-chart-ring:before {
  content: "\e934";
}
.unicon-chart-stacked:before {
  content: "\e935";
}
.unicon-chart-treemap:before {
  content: "\e936";
}
.unicon-chart-venn-diagram:before {
  content: "\e937";
}
.unicon-chat-bot:before {
  content: "\e938";
}
.unicon-chat-launch:before {
  content: "\e939";
}
.unicon-chat:before {
  content: "\e93a";
}
.unicon-checkbox-checked:before {
  content: "\e93b";
}
.unicon-checkbox:before {
  content: "\e93c";
}
.unicon-checkmark-outline:before {
  content: "\e93d";
}
.unicon-checkmark:before {
  content: "\e93e";
}
.unicon-chevron-down:before {
  content: "\e93f";
}
.unicon-chevron-left:before {
  content: "\e940";
}
.unicon-chevron-mini:before {
  content: "\e941";
}
.unicon-chevron-right:before {
  content: "\e942";
}
.unicon-chevron-sort-down:before {
  content: "\e943";
}
.unicon-chevron-sort-up:before {
  content: "\e944";
}
.unicon-chevron-sort:before {
  content: "\e945";
}
.unicon-chevron-up:before {
  content: "\e946";
}
.unicon-circle-dash:before {
  content: "\e947";
}
.unicon-circle-measurement:before {
  content: "\e948";
}
.unicon-clean:before {
  content: "\e949";
}
.unicon-close-outline:before {
  content: "\e94a";
}
.unicon-close:before {
  content: "\e94b";
}
.unicon-cloud-download:before {
  content: "\e94c";
}
.unicon-cloud-lightning:before {
  content: "\e94d";
}
.unicon-cloud-satellite:before {
  content: "\e94e";
}
.unicon-cloud-upload:before {
  content: "\e94f";
}
.unicon-cobb-angle:before {
  content: "\e950";
}
.unicon-code:before {
  content: "\e951";
}
.unicon-collaborate:before {
  content: "\e952";
}
.unicon-collapse-all:before {
  content: "\e953";
}
.unicon-color-palette:before {
  content: "\e954";
}
.unicon-color-switch:before {
  content: "\e955";
}
.unicon-column:before {
  content: "\e956";
}
.unicon-compare:before {
  content: "\e957";
}
.unicon-condition-point:before {
  content: "\e958";
}
.unicon-condition-wait-point:before {
  content: "\e959";
}
.unicon-container-software:before {
  content: "\e95a";
}
.unicon-contour-finding:before {
  content: "\e95b";
}
.unicon-contrast:before {
  content: "\e95c";
}
.unicon-copy-file:before {
  content: "\e95d";
}
.unicon-copy:before {
  content: "\e95e";
}
.unicon-course:before {
  content: "\e95f";
}
.unicon-credentials:before {
  content: "\e960";
}
.unicon-crop:before {
  content: "\e961";
}
.unicon-cube-view:before {
  content: "\e962";
}
.unicon-cube:before {
  content: "\e963";
}
.unicon-currency-dollar:before {
  content: "\e964";
}
.unicon-currency:before {
  content: "\e965";
}
.unicon-cursor-1:before {
  content: "\e966";
}
.unicon-cursor-2:before {
  content: "\e967";
}
.unicon-cursor-alt:before {
  content: "\e968";
}
.unicon-cursor:before {
  content: "\e969";
}
.unicon-curve-auto-colon:before {
  content: "\e96a";
}
.unicon-curve-manual:before {
  content: "\e96b";
}
.unicon-cut-in-half:before {
  content: "\e96c";
}
.unicon-cut:before {
  content: "\e96d";
}
.unicon-dashboard-reference:before {
  content: "\e96e";
}
.unicon-dashboard:before {
  content: "\e96f";
}
.unicon-data-1:before {
  content: "\e970";
}
.unicon-data-base-alt:before {
  content: "\e971";
}
.unicon-data-base:before {
  content: "\e972";
}
.unicon-debug:before {
  content: "\e973";
}
.unicon-delete:before {
  content: "\e974";
}
.unicon-delivery-parcel:before {
  content: "\e975";
}
.unicon-delivery-truck:before {
  content: "\e976";
}
.unicon-delivery:before {
  content: "\e977";
}
.unicon-departure:before {
  content: "\e978";
}
.unicon-devices:before {
  content: "\e979";
}
.unicon-diagram:before {
  content: "\e97a";
}
.unicon-dicom-overlay:before {
  content: "\e97b";
}
.unicon-direct-link:before {
  content: "\e97c";
}
.unicon-direction-right-01:before {
  content: "\e97d";
}
.unicon-direction-straight-right:before {
  content: "\e97e";
}
.unicon-direction-straight:before {
  content: "\e97f";
}
.unicon-document-add:before {
  content: "\e980";
}
.unicon-document-attachment:before {
  content: "\e981";
}
.unicon-document-blank:before {
  content: "\e982";
}
.unicon-document-download:before {
  content: "\e983";
}
.unicon-document:before {
  content: "\e984";
}
.unicon-dot-mark:before {
  content: "\e985";
}
.unicon-down-to-bottom:before {
  content: "\e986";
}
.unicon-download-study:before {
  content: "\e987";
}
.unicon-download:before {
  content: "\e988";
}
.unicon-drag-horizontal:before {
  content: "\e989";
}
.unicon-drag-vertical:before {
  content: "\e98a";
}
.unicon-draggable:before {
  content: "\e98b";
}
.unicon-draw:before {
  content: "\e98c";
}
.unicon-drop-photo-filled:before {
  content: "\e98d";
}
.unicon-drop-photo:before {
  content: "\e98e";
}
.unicon-earth-americas:before {
  content: "\e98f";
}
.unicon-earth-europe-africa:before {
  content: "\e990";
}
.unicon-earth-filled:before {
  content: "\e991";
}
.unicon-earth:before {
  content: "\e992";
}
.unicon-edge-enhancement:before {
  content: "\e993";
}
.unicon-edit-off:before {
  content: "\e994";
}
.unicon-edit:before {
  content: "\e995";
}
.unicon-edt-loop:before {
  content: "\e996";
}
.unicon-email-new:before {
  content: "\e997";
}
.unicon-email:before {
  content: "\e998";
}
.unicon-enterprise:before {
  content: "\e999";
}
.unicon-erase:before {
  content: "\e99a";
}
.unicon-error-outline:before {
  content: "\e99b";
}
.unicon-error:before {
  content: "\e99c";
}
.unicon-event-schedule:before {
  content: "\e99d";
}
.unicon-event:before {
  content: "\e99e";
}
.unicon-events-alt:before {
  content: "\e99f";
}
.unicon-events:before {
  content: "\e9a0";
}
.unicon-explore:before {
  content: "\e9a1";
}
.unicon-export:before {
  content: "\e9a2";
}
.unicon-eyedropper:before {
  content: "\e9a3";
}
.unicon-face-dissatisfied:before {
  content: "\e9a4";
}
.unicon-face-satisfied:before {
  content: "\e9a5";
}
.unicon-fade:before {
  content: "\e9a6";
}
.unicon-favorite-filled:before {
  content: "\e9a7";
}
.unicon-favorite:before {
  content: "\e9a8";
}
.unicon-file-storage:before {
  content: "\e9a9";
}
.unicon-filter-edit:before {
  content: "\e9aa";
}
.unicon-filter:before {
  content: "\e9ab";
}
.unicon-finance:before {
  content: "\e9ac";
}
.unicon-fingerprint-recognition:before {
  content: "\e9ad";
}
.unicon-fire:before {
  content: "\e9ae";
}
.unicon-flag-filled:before {
  content: "\e9af";
}
.unicon-flag:before {
  content: "\e9b0";
}
.unicon-flash-filled:before {
  content: "\e9b1";
}
.unicon-flash:before {
  content: "\e9b2";
}
.unicon-flow-connection:before {
  content: "\e9b3";
}
.unicon-folder-add:before {
  content: "\e9b4";
}
.unicon-folder-shared:before {
  content: "\e9b5";
}
.unicon-folder:before {
  content: "\e9b6";
}
.unicon-folders:before {
  content: "\e9b7";
}
.unicon-forum:before {
  content: "\e9b8";
}
.unicon-game-console:before {
  content: "\e9b9";
}
.unicon-gamification:before {
  content: "\e9ba";
}
.unicon-gift:before {
  content: "\e9bb";
}
.unicon-globe:before {
  content: "\e9bc";
}
.unicon-glyph-caution-inverted:before {
  content: "\e9bd";
}
.unicon-glyph-caution:before {
  content: "\e9be";
}
.unicon-glyph-circle-fill:before {
  content: "\e9bf";
}
.unicon-glyph-square-fill:before {
  content: "\e9c0";
}
.unicon-glyph-undefined:before {
  content: "\e9c1";
}
.unicon-gradient:before {
  content: "\e9c2";
}
.unicon-grid:before {
  content: "\e9c3";
}
.unicon-group-objects-new:before {
  content: "\e9c4";
}
.unicon-group-objects-save:before {
  content: "\e9c5";
}
.unicon-group:before {
  content: "\e9c6";
}
.unicon-growth:before {
  content: "\e9c7";
}
.unicon-gui-management:before {
  content: "\e9c8";
}
.unicon-gui:before {
  content: "\e9c9";
}
.unicon-hashtag:before {
  content: "\e9ca";
}
.unicon-headphones:before {
  content: "\e9cb";
}
.unicon-headset:before {
  content: "\e9cc";
}
.unicon-help-filled:before {
  content: "\e9cd";
}
.unicon-help:before {
  content: "\e9ce";
}
.unicon-hole-filling:before {
  content: "\e9cf";
}
.unicon-home:before {
  content: "\e9d0";
}
.unicon-ibm-cloud-pak-security:before {
  content: "\e9d1";
}
.unicon-iCA-2D:before {
  content: "\e9d2";
}
.unicon-idea:before {
  content: "\e9d3";
}
.unicon-identification:before {
  content: "\e9d4";
}
.unicon-image-copy:before {
  content: "\e9d5";
}
.unicon-image-search-alt:before {
  content: "\e9d6";
}
.unicon-image-search:before {
  content: "\e9d7";
}
.unicon-image:before {
  content: "\e9d8";
}
.unicon-in-progress:before {
  content: "\e9d9";
}
.unicon-incomplete:before {
  content: "\e9da";
}
.unicon-increase-level:before {
  content: "\e9db";
}
.unicon-industry:before {
  content: "\e9dc";
}
.unicon-information-filled:before {
  content: "\e9dd";
}
.unicon-information:before {
  content: "\e9de";
}
.unicon-insert-page:before {
  content: "\e9df";
}
.unicon-insert-syntax:before {
  content: "\e9e0";
}
.unicon-integration:before {
  content: "\e9e1";
}
.unicon-interactive-segmentation-cursor:before {
  content: "\e9e2";
}
.unicon-intersect:before {
  content: "\e9e3";
}
.unicon-inventory-management:before {
  content: "\e9e4";
}
.unicon-keyboard:before {
  content: "\e9e5";
}
.unicon-language:before {
  content: "\e9e6";
}
.unicon-laptop:before {
  content: "\e9e7";
}
.unicon-lasso-polygon:before {
  content: "\e9e8";
}
.unicon-lasso:before {
  content: "\e9e9";
}
.unicon-launch-study-1:before {
  content: "\e9ea";
}
.unicon-launch-study-2:before {
  content: "\e9eb";
}
.unicon-launch:before {
  content: "\e9ec";
}
.unicon-layers:before {
  content: "\e9ed";
}
.unicon-legend:before {
  content: "\e9ee";
}
.unicon-license-draft:before {
  content: "\e9ef";
}
.unicon-lifesaver:before {
  content: "\e9f0";
}
.unicon-light-filled:before {
  content: "\e9f1";
}
.unicon-light:before {
  content: "\e9f2";
}
.unicon-lightning:before {
  content: "\e9f3";
}
.unicon-link:before {
  content: "\e9f4";
}
.unicon-list-boxes:before {
  content: "\e9f5";
}
.unicon-list-bulleted:before {
  content: "\e9f6";
}
.unicon-list-checked:before {
  content: "\e9f7";
}
.unicon-list-dropdown:before {
  content: "\e9f8";
}
.unicon-list-numbered:before {
  content: "\e9f9";
}
.unicon-list:before {
  content: "\e9fa";
}
.unicon-location-current:before {
  content: "\e9fb";
}
.unicon-location:before {
  content: "\e9fc";
}
.unicon-locked:before {
  content: "\e9fd";
}
.unicon-login:before {
  content: "\e9fe";
}
.unicon-logo-delicious:before {
  content: "\e9ff";
}
.unicon-logo-digg:before {
  content: "\ea00";
}
.unicon-logo-discord:before {
  content: "\ea01";
}
.unicon-logo-facebook:before {
  content: "\ea02";
}
.unicon-logo-flickr:before {
  content: "\ea03";
}
.unicon-logo-github:before {
  content: "\ea04";
}
.unicon-logo-google:before {
  content: "\ea05";
}
.unicon-logo-instagram:before {
  content: "\ea06";
}
.unicon-logo-linkedin:before {
  content: "\ea07";
}
.unicon-logo-livestream:before {
  content: "\ea08";
}
.unicon-logo-medium:before {
  content: "\ea09";
}
.unicon-logo-pinterest:before {
  content: "\ea0a";
}
.unicon-logo-quora:before {
  content: "\ea0b";
}
.unicon-logo-skype:before {
  content: "\ea0c";
}
.unicon-logo-slack:before {
  content: "\ea0d";
}
.unicon-logo-snapchat:before {
  content: "\ea0e";
}
.unicon-logo-stumbleupon:before {
  content: "\ea0f";
}
.unicon-logo-tumblr:before {
  content: "\ea10";
}
.unicon-logo-twitter:before {
  content: "\ea11";
}
.unicon-logo-vmware:before {
  content: "\ea12";
}
.unicon-logo-xing:before {
  content: "\ea13";
}
.unicon-logo-youtube:before {
  content: "\ea14";
}
.unicon-logout:before {
  content: "\ea15";
}
.unicon-mac-command:before {
  content: "\ea16";
}
.unicon-mac-option:before {
  content: "\ea17";
}
.unicon-mac-shift:before {
  content: "\ea18";
}
.unicon-machine-learning:before {
  content: "\ea19";
}
.unicon-magic-wand-filled:before {
  content: "\ea1a";
}
.unicon-magic-wand:before {
  content: "\ea1b";
}
.unicon-magnify:before {
  content: "\ea1c";
}
.unicon-manage-protection:before {
  content: "\ea1d";
}
.unicon-map-center:before {
  content: "\ea1e";
}
.unicon-map-identify:before {
  content: "\ea1f";
}
.unicon-map:before {
  content: "\ea20";
}
.unicon-maximize:before {
  content: "\ea21";
}
.unicon-media-cast:before {
  content: "\ea22";
}
.unicon-media-library:before {
  content: "\ea23";
}
.unicon-menu:before {
  content: "\ea24";
}
.unicon-meter-alt:before {
  content: "\ea25";
}
.unicon-meter:before {
  content: "\ea26";
}
.unicon-microphone-filled:before {
  content: "\ea27";
}
.unicon-microphone:before {
  content: "\ea28";
}
.unicon-microscope:before {
  content: "\ea29";
}
.unicon-migrate-alt:before {
  content: "\ea2a";
}
.unicon-military-camp:before {
  content: "\ea2b";
}
.unicon-minimize:before {
  content: "\ea2c";
}
.unicon-misuse-alt:before {
  content: "\ea2d";
}
.unicon-misuse-outline:before {
  content: "\ea2e";
}
.unicon-misuse:before {
  content: "\ea2f";
}
.unicon-mobile-add:before {
  content: "\ea30";
}
.unicon-mobile:before {
  content: "\ea31";
}
.unicon-model-alt:before {
  content: "\ea32";
}
.unicon-model:before {
  content: "\ea33";
}
.unicon-money:before {
  content: "\ea34";
}
.unicon-move:before {
  content: "\ea35";
}
.unicon-mpr-toggle:before {
  content: "\ea36";
}
.unicon-music:before {
  content: "\ea37";
}
.unicon-name-space:before {
  content: "\ea38";
}
.unicon-navaid-military:before {
  content: "\ea39";
}
.unicon-new-tab:before {
  content: "\ea3a";
}
.unicon-nominal:before {
  content: "\ea3b";
}
.unicon-not-available:before {
  content: "\ea3c";
}
.unicon-notebook-reference:before {
  content: "\ea3d";
}
.unicon-notebook:before {
  content: "\ea3e";
}
.unicon-notification-filled:before {
  content: "\ea3f";
}
.unicon-notification-new:before {
  content: "\ea40";
}
.unicon-notification:before {
  content: "\ea41";
}
.unicon-opacity:before {
  content: "\ea42";
}
.unicon-open-panel-left:before {
  content: "\ea43";
}
.unicon-open-panel-top:before {
  content: "\ea44";
}
.unicon-ordinal:before {
  content: "\ea45";
}
.unicon-overflow-menu-horizontal:before {
  content: "\ea46";
}
.unicon-overflow-menu-vertical:before {
  content: "\ea47";
}
.unicon-overlay:before {
  content: "\ea48";
}
.unicon-package:before {
  content: "\ea49";
}
.unicon-page-break:before {
  content: "\ea4a";
}
.unicon-paint-brush-alt:before {
  content: "\ea4b";
}
.unicon-paint-brush:before {
  content: "\ea4c";
}
.unicon-pan-horizontal:before {
  content: "\ea4d";
}
.unicon-pan-vertical:before {
  content: "\ea4e";
}
.unicon-panel-expansion:before {
  content: "\ea4f";
}
.unicon-partnership:before {
  content: "\ea50";
}
.unicon-password:before {
  content: "\ea51";
}
.unicon-pause:before {
  content: "\ea52";
}
.unicon-pedestrian-child:before {
  content: "\ea53";
}
.unicon-pedestrian-family:before {
  content: "\ea54";
}
.unicon-pen-fountain:before {
  content: "\ea55";
}
.unicon-pen:before {
  content: "\ea56";
}
.unicon-pending:before {
  content: "\ea57";
}
.unicon-percentage-filled:before {
  content: "\ea58";
}
.unicon-percentage:before {
  content: "\ea59";
}
.unicon-phone-ip:before {
  content: "\ea5a";
}
.unicon-phone:before {
  content: "\ea5b";
}
.unicon-piggy-bank-slot:before {
  content: "\ea5c";
}
.unicon-piggy-bank:before {
  content: "\ea5d";
}
.unicon-pin-filled:before {
  content: "\ea5e";
}
.unicon-pin:before {
  content: "\ea5f";
}
.unicon-plane:before {
  content: "\ea60";
}
.unicon-play-filled-alt:before {
  content: "\ea61";
}
.unicon-play-filled:before {
  content: "\ea62";
}
.unicon-play-outline:before {
  content: "\ea63";
}
.unicon-play:before {
  content: "\ea64";
}
.unicon-playlist:before {
  content: "\ea65";
}
.unicon-policy:before {
  content: "\ea66";
}
.unicon-popup:before {
  content: "\ea67";
}
.unicon-portfolio:before {
  content: "\ea68";
}
.unicon-power:before {
  content: "\ea69";
}
.unicon-presentation-file:before {
  content: "\ea6a";
}
.unicon-printer:before {
  content: "\ea6b";
}
.unicon-product:before {
  content: "\ea6c";
}
.unicon-progress-bar:before {
  content: "\ea6d";
}
.unicon-purchase:before {
  content: "\ea6e";
}
.unicon-query:before {
  content: "\ea6f";
}
.unicon-quotes:before {
  content: "\ea70";
}
.unicon-radio-button-checked:before {
  content: "\ea71";
}
.unicon-radio-button:before {
  content: "\ea72";
}
.unicon-rain-drop:before {
  content: "\ea73";
}
.unicon-receipt:before {
  content: "\ea74";
}
.unicon-recently-viewed:before {
  content: "\ea75";
}
.unicon-recommend:before {
  content: "\ea76";
}
.unicon-recording-filled-alt:before {
  content: "\ea77";
}
.unicon-recording-filled:before {
  content: "\ea78";
}
.unicon-recording:before {
  content: "\ea79";
}
.unicon-redo:before {
  content: "\ea7a";
}
.unicon-registration:before {
  content: "\ea7b";
}
.unicon-reminder:before {
  content: "\ea7c";
}
.unicon-renew:before {
  content: "\ea7d";
}
.unicon-repeat:before {
  content: "\ea7e";
}
.unicon-reply-all:before {
  content: "\ea7f";
}
.unicon-reply:before {
  content: "\ea80";
}
.unicon-report-data:before {
  content: "\ea81";
}
.unicon-report:before {
  content: "\ea82";
}
.unicon-request-quote:before {
  content: "\ea83";
}
.unicon-reset-alt:before {
  content: "\ea84";
}
.unicon-reset:before {
  content: "\ea85";
}
.unicon-restart:before {
  content: "\ea86";
}
.unicon-result:before {
  content: "\ea87";
}
.unicon-roadmap:before {
  content: "\ea88";
}
.unicon-rocket:before {
  content: "\ea89";
}
.unicon-rotate-180:before {
  content: "\ea8a";
}
.unicon-rotate-360:before {
  content: "\ea8b";
}
.unicon-row:before {
  content: "\ea8c";
}
.unicon-rss:before {
  content: "\ea8d";
}
.unicon-rule:before {
  content: "\ea8e";
}
.unicon-ruler-alt:before {
  content: "\ea8f";
}
.unicon-ruler:before {
  content: "\ea90";
}
.unicon-run:before {
  content: "\ea91";
}
.unicon-save:before {
  content: "\ea92";
}
.unicon-scale:before {
  content: "\ea93";
}
.unicon-scales:before {
  content: "\ea94";
}
.unicon-scalpel-cursor:before {
  content: "\ea95";
}
.unicon-scalpel-lasso:before {
  content: "\ea96";
}
.unicon-scalpel:before {
  content: "\ea97";
}
.unicon-scan:before {
  content: "\ea98";
}
.unicon-screen:before {
  content: "\ea99";
}
.unicon-script:before {
  content: "\ea9a";
}
.unicon-search-locate:before {
  content: "\ea9b";
}
.unicon-search:before {
  content: "\ea9c";
}
.unicon-security:before {
  content: "\ea9d";
}
.unicon-select-01:before {
  content: "\ea9e";
}
.unicon-select-02:before {
  content: "\ea9f";
}
.unicon-select-window:before {
  content: "\eaa0";
}
.unicon-send-alt-filled:before {
  content: "\eaa1";
}
.unicon-send-alt:before {
  content: "\eaa2";
}
.unicon-send-filled:before {
  content: "\eaa3";
}
.unicon-send-to-back:before {
  content: "\eaa4";
}
.unicon-send:before {
  content: "\eaa5";
}
.unicon-server-time:before {
  content: "\eaa6";
}
.unicon-settings-adjust:before {
  content: "\eaa7";
}
.unicon-settings:before {
  content: "\eaa8";
}
.unicon-shape-except:before {
  content: "\eaa9";
}
.unicon-share:before {
  content: "\eaaa";
}
.unicon-shopping-bag:before {
  content: "\eaab";
}
.unicon-shopping-cart:before {
  content: "\eaac";
}
.unicon-shopping-catalog:before {
  content: "\eaad";
}
.unicon-shrink-screen:before {
  content: "\eaae";
}
.unicon-shuffle:before {
  content: "\eaaf";
}
.unicon-signal-strength:before {
  content: "\eab0";
}
.unicon-skill-level:before {
  content: "\eab1";
}
.unicon-smoothing-cursor:before {
  content: "\eab2";
}
.unicon-smoothing:before {
  content: "\eab3";
}
.unicon-soccer:before {
  content: "\eab4";
}
.unicon-software:before {
  content: "\eab5";
}
.unicon-spell-check:before {
  content: "\eab6";
}
.unicon-split-discard:before {
  content: "\eab7";
}
.unicon-split-screen:before {
  content: "\eab8";
}
.unicon-split:before {
  content: "\eab9";
}
.unicon-spray-paint:before {
  content: "\eaba";
}
.unicon-stacked-scrolling-1:before {
  content: "\eabb";
}
.unicon-stamp:before {
  content: "\eabc";
}
.unicon-star-filled:before {
  content: "\eabd";
}
.unicon-star:before {
  content: "\eabe";
}
.unicon-stay-inside:before {
  content: "\eabf";
}
.unicon-stop-filled-alt:before {
  content: "\eac0";
}
.unicon-stop:before {
  content: "\eac1";
}
.unicon-store:before {
  content: "\eac2";
}
.unicon-string-integer:before {
  content: "\eac3";
}
.unicon-string-text:before {
  content: "\eac4";
}
.unicon-sub-volume:before {
  content: "\eac5";
}
.unicon-subtract-alt:before {
  content: "\eac6";
}
.unicon-subtract:before {
  content: "\eac7";
}
.unicon-table:before {
  content: "\eac8";
}
.unicon-tablet:before {
  content: "\eac9";
}
.unicon-tag-group:before {
  content: "\eaca";
}
.unicon-tag:before {
  content: "\eacb";
}
.unicon-task-add:before {
  content: "\eacc";
}
.unicon-task-approved:before {
  content: "\eacd";
}
.unicon-task-view:before {
  content: "\eace";
}
.unicon-task:before {
  content: "\eacf";
}
.unicon-template:before {
  content: "\ead0";
}
.unicon-term:before {
  content: "\ead1";
}
.unicon-terminal:before {
  content: "\ead2";
}
.unicon-text-annotation-toggle:before {
  content: "\ead3";
}
.unicon-text-bold:before {
  content: "\ead4";
}
.unicon-text-color:before {
  content: "\ead5";
}
.unicon-text-creation:before {
  content: "\ead6";
}
.unicon-text-fill:before {
  content: "\ead7";
}
.unicon-text-font:before {
  content: "\ead8";
}
.unicon-text-line-spacing:before {
  content: "\ead9";
}
.unicon-text-mining-applier:before {
  content: "\eada";
}
.unicon-text-mining:before {
  content: "\eadb";
}
.unicon-text-underline:before {
  content: "\eadc";
}
.unicon-theater:before {
  content: "\eadd";
}
.unicon-thumbnail-1:before {
  content: "\eade";
}
.unicon-thumbnail-2:before {
  content: "\eadf";
}
.unicon-thumbs-down:before {
  content: "\eae0";
}
.unicon-thumbs-up:before {
  content: "\eae1";
}
.unicon-ticket:before {
  content: "\eae2";
}
.unicon-time:before {
  content: "\eae3";
}
.unicon-tool-box:before {
  content: "\eae4";
}
.unicon-tools-alt:before {
  content: "\eae5";
}
.unicon-tools:before {
  content: "\eae6";
}
.unicon-touch-1:before {
  content: "\eae7";
}
.unicon-touch-interaction:before {
  content: "\eae8";
}
.unicon-translate:before {
  content: "\eae9";
}
.unicon-trash-can:before {
  content: "\eaea";
}
.unicon-trophy:before {
  content: "\eaeb";
}
.unicon-types:before {
  content: "\eaec";
}
.unicon-umbrella:before {
  content: "\eaed";
}
.unicon-unlink:before {
  content: "\eaee";
}
.unicon-unlocked:before {
  content: "\eaef";
}
.unicon-upload:before {
  content: "\eaf0";
}
.unicon-user-avatar-filled-alt:before {
  content: "\eaf1";
}
.unicon-user-avatar-filled:before {
  content: "\eaf2";
}
.unicon-user-avatar:before {
  content: "\eaf3";
}
.unicon-user-multiple:before {
  content: "\eaf4";
}
.unicon-user:before {
  content: "\eaf5";
}
.unicon-uv-index:before {
  content: "\eaf6";
}
.unicon-video-add:before {
  content: "\eaf7";
}
.unicon-video-chat:before {
  content: "\eaf8";
}
.unicon-video:before {
  content: "\eaf9";
}
.unicon-view-filled:before {
  content: "\eafa";
}
.unicon-view-mode-1:before {
  content: "\eafb";
}
.unicon-view-mode-2:before {
  content: "\eafc";
}
.unicon-view-next:before {
  content: "\eafd";
}
.unicon-view-off:before {
  content: "\eafe";
}
.unicon-view:before {
  content: "\eaff";
}
.unicon-virtual-column-key:before {
  content: "\eb00";
}
.unicon-virtual-private-cloud-alt:before {
  content: "\eb01";
}
.unicon-visual-recognition:before {
  content: "\eb02";
}
.unicon-volume-block-storage:before {
  content: "\eb03";
}
.unicon-volume-up:before {
  content: "\eb04";
}
.unicon-wallet:before {
  content: "\eb05";
}
.unicon-warning-alt-filled:before {
  content: "\eb06";
}
.unicon-warning-alt:before {
  content: "\eb07";
}
.unicon-warning-filled:before {
  content: "\eb08";
}
.unicon-warning:before {
  content: "\eb09";
}
.unicon-wheat:before {
  content: "\eb0a";
}
.unicon-wifi:before {
  content: "\eb0b";
}
.unicon-wikis:before {
  content: "\eb0c";
}
.unicon-word-cloud:before {
  content: "\eb0d";
}
.unicon-workspace:before {
  content: "\eb0e";
}
.unicon-zoom-area:before {
  content: "\eb0f";
}
.unicon-zoom-in-area:before {
  content: "\eb10";
}
.unicon-zoom-in:before {
  content: "\eb11";
}
.unicon-zoom-out-area:before {
  content: "\eb12";
}
.unicon-zoom-out:before {
  content: "\eb13";
}
.unicon-zoom-reset:before {
  content: "\eb14";
}
