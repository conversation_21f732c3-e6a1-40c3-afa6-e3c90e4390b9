/*
 * Dashboard JavaScript file
 */

import './styles/dashboard.css';
import ApexCharts from 'apexcharts';

// Make ApexCharts available globally
window.ApexCharts = ApexCharts;

document.addEventListener('DOMContentLoaded', () => {
  console.log('Dashboard JS loaded');
  console.log('ApexCharts available:', typeof ApexCharts !== 'undefined');

  // Add smooth transitions for cards
  const cards = document.querySelectorAll('.card');
  cards.forEach(card => {
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-5px)';
      card.style.boxShadow = '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
    });

    card.addEventListener('mouseleave', () => {
      card.style.transform = 'translateY(0)';
      card.style.boxShadow = '';
    });
  });
});
