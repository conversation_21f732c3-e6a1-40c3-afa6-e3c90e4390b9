@use '../utils' as *;

/*=============================
	1. Button style
===============================*/
.btn {
    user-select: none;
    -moz-user-select: none;
    background: var(--tg-theme-primary) none repeat scroll 0 0;
    border: none;
    @include border-radius(50px);
    color: var(--tg-common-color-white);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    font-size: 16px;
    font-weight: var(--tg-fw-bold);
    letter-spacing: 0;
    line-height: 1.2;
    margin-bottom: 0;
    padding: 14px 24px;
    text-align: center;
    text-transform: inherit;
    touch-action: manipulation;
    @include transition(.3s);
    vertical-align: middle;
    white-space: nowrap;
    &:hover {
        background: var(--tg-theme-secondary);
        color: var(--tg-common-color-white);
    }
    & i {
        font-size: 24px;
        line-height: 1;
        margin-left: 8px;
        font-weight: var(--tg-fw-bold);
    }
}
.gradient-btn {
    background: transparent;
    position: relative;
    overflow: hidden;
    z-index: 1;
    &::after {
        content: "";
        position: absolute;
        @include border-radius(50px);
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        transition: .3s ease;
        background-color: var(--tg-theme-primary);
        background-image: linear-gradient(25deg, var(--tg-gradient-1), var(--tg-gradient-2));
    }
    &-2 {
        font-size: 18px;
        padding: 21px 40px;
    }
    &:hover {
        &::after {
            opacity: .75;
        }
    }
}
.border-btn {
    background: transparent;
    position: relative;
    border: 2px solid rgba(0, 0, 0, 0.15);
    color: var(--tg-heading-color);
    padding: 12px 24px;
    z-index: 1;
    &::after {
        content: "";
        position: absolute;
        @include border-radius(50px);
        left: -2px;
        top: -2px;
        right: -2px;
        bottom: -2px;
        z-index: -1;
        transition: .3s ease;
        background-color: var(--tg-theme-primary);
        background-image: linear-gradient(25deg, var(--tg-gradient-1), var(--tg-gradient-2));
        opacity: 0;
    }
    &:hover {
        border-color: transparent;
        &::after {
            opacity: 1;
        }
    }
}