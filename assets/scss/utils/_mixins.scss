
    // bg color
    @mixin bg-color($color, $opacity) {
        background-color: rgba($color, $opacity);
    }

    // placeholder input
    @mixin tg-placeholder {
        &::-webkit-input-placeholder {
            @content;
        }
        &:-moz-placeholder {
            @content;
        }
        &::-moz-placeholder {
            @content;
        }
        &:-ms-input-placeholder {
            @content;
        }
    }

    // filter
    @mixin filter($value) {
        -webkit-filter: $value;
        filter: $value;
    }

    // appearance for select
    @mixin appearance($value) {
        -webkit-appearance: $value;
        -moz-appearance: $value;
        -ms-appearance: $value;
        -o-appearance: $value;
        appearance: $value;
    }

    // keyframes
    @mixin keyframes($name) {
        @-webkit-keyframes #{$name} {
            @content;
        }
        @-moz-keyframes #{$name} {
            @content;
        }
        @-ms-keyframes #{$name} {
            @content;
        }
        @keyframes #{$name} {
            @content;
        }
    }

    //Background
    @mixin background {
        background-position: center;
        background-size: cover;
        background-repeat: no-repeat;
    }

    //Transition
    @mixin transition($time) {
        -webkit-transition: all $time ease-out 0s;
        -moz-transition: all $time ease-out 0s;
        -ms-transition: all $time ease-out 0s;
        -o-transition: all $time ease-out 0s;
        transition: all $time ease-out 0s;
    }

    // Transform
    @mixin transform($transforms) {
        -webkit-transform: $transforms;
        -moz-transform: $transforms;
        -ms-transform: $transforms;
        -o-transform: $transforms;
        transform: $transforms;
    }

    //Border
    @mixin border-radius($man) {
        -webkit-border-radius: $man;
        -moz-border-radius: $man;
        -o-border-radius: $man;
        -ms-border-radius: $man;
        border-radius: $man;
    }

    // Sentence case
    @mixin sentence-case() {
        text-transform: lowercase;
        &:first-letter {
            text-transform: uppercase;
        }
    }

    // Flexbox display
    @mixin flexbox() {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
    }

    // Flexbox display
    @mixin flex-center() {
        display: -webkit-box;
        display: -moz-box;
        display: -ms-flexbox;
        display: -webkit-flex;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    // Object Position
    @mixin object-fit() {
        -o-object-fit: cover;
        object-fit: cover;
        -o-object-position: center;
        object-position: center
    }

    // Box shadows
    @mixin box-shadow($shadow) {
        -webkit-box-shadow: $shadow;
        -moz-box-shadow: $shadow;
        -ms-box-shadow: $shadow;
        -o-box-shadow: $shadow;
        box-shadow: $shadow;
    }

    // Radius Box
	@mixin square-box($size, $circle: false) {
	    width: $size;
	    height: $size;
	    line-height: $size;
	    text-align: center;

	    @if $circle {
	        border-radius: 50%;
	    }
	}