@use 'colors' as *;
@use 'typography' as *;

:root {

    // Font Family Declaration
    @each $font-family, $shades in $theme-fonts {
        @each $shade, $value in $shades {
            --tg-#{$font-family}-#{$shade}: #{$value};
        }
    }

    // Font Size Declaration
    @each $body, $shades in $font-size {
        @each $shade, $value in $shades {
            --tg-#{$body}-#{$shade}: #{$value};
        }
    }

   // Line Height Declaration
    @each $line-height, $shades in $theme-lh {
        @each $shade, $value in $shades {
            --tg-#{$line-height}-#{$shade}: #{$value};
        }
    }

    // Color Declaration
    @each $color, $shades in $colors {
        @each $shade, $value in $shades {
            --tg-#{$color}-#{$shade}: #{$value};
        }
    }


    // Font Weight Declaration
    @each $fw, $shades in $font-weight {
        @each $shade, $value in $shades {
            --tg-#{$fw}-#{$shade}: #{$value};
        }
    }

}
