@use '../utils' as *;

/*=============================
	02. Header
===============================*/
.transparent-header {
	position: absolute;
	left: 0;
	top: 0px;
	width: 100%;
	z-index: 9;
    padding: 10px 0;
	height: auto;
    @media #{$xs} {
        top: 0;
    }
}
.tg-header {
    &__area {
        & .mobile-nav-toggler {
            position: relative;
            float: right;
            font-size: 30px;
            cursor: pointer;
            line-height: 1;
            color: var(--tg-theme-primary);
            display: none;
            margin-top: 2px;
            @media #{$md} {
                display: block;
            }
        }
        @media #{$md} {
            padding: 25px 0;
        }
    }
}
.tgmenu {
    &__nav {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: flex-start;
        @media #{$md} {
            justify-content: space-between;
        }
    }
    &__navbar-wrap {
        display: flex;
	    flex-grow: 1;
        & ul {
            display: flex;
            padding: 0;
            flex-direction: row;
            flex-wrap: wrap;
            margin: 0 0 0 auto;
            & li {
                display: block;
                position: relative;
                list-style: none;
                & a {
                    font-size: 20px;
                    font-weight: var(--tg-fw-bold);
                    text-transform: capitalize;
                    color: var(--tg-heading-color);
                    padding: 30px 16px;
                    display: block;
                    line-height: 1;
                    position: relative;
                    z-index: 1;
                }
                & .sub-menu {
                    position: absolute;
                    left: 0;
                    top: 100%;
                    min-width: 230px;
                    border: 1px solid #f5f5f5;
                    background: var(--tg-common-color-white);
                    margin: 0;
                    @include transform(scale(1, 0));
                    transform-origin: 0 0;
                    @include transition(0.3s);
                    -webkit-box-shadow: 0px 30px 70px 0px rgba(137, 139, 142, 0.15);
                    -moz-box-shadow: 0px 30px 70px 0px rgba(137, 139, 142, 0.15);
                    box-shadow: 0px 30px 70px 0px rgba(137, 139, 142, 0.15);
                    border-radius: 14px;
                    padding: 18px 0;
                    display: block;
                    visibility: hidden;
                    opacity: 0;
                    z-index: 9;
                    & .sub-menu {
                        right: auto;
                        left: 100%;
                        top: 0;
                    }
                    & li {
                        margin-left: 0;
                        text-align: left;
                        display: block;
                        & a {
                            padding: 7px 15px 7px 25px;
                            line-height: 1.4;
                            color: var(--tg-heading-color);
                            font-size: 18px;
                            text-transform: capitalize;
                        }
                        &.active > {
                            & a {
                                color: var(--tg-theme-primary);
                            }
                        }
                    }
                }
                &:hover > .sub-menu {
                    opacity: 1;
                    visibility: visible;
                    transform: scale(1);
                }
            }
        }
        & > ul {
            & > li {
                &.active,
                &:hover {
                    & a {
                        color: var(--tg-theme-primary);
                    }
                }
            }
        }
    }
    &__main-menu {
        & li.menu-item-has-children {
            & .dropdown-btn {
                display: none;
            }
        }
    }
    &__action {
        & > ul {
            display: flex;
            align-items: center;
            margin-left: 40px;
            & .header-social {
                position: relative;
                @include flexbox();
                align-items: center;
                gap: 10px 20px;
                & a {
                    color: var(--tg-heading-color);
                    font-size: 18px;
                    @media #{$md} {
                        font-size: 22px;
                        line-height: 1;
                    }
                    & svg {
                        display: block;
                        height: 1em;
                        overflow: visible;
                    }
                    &:hover {
                        color: var(--tg-theme-primary);
                    }
                }
            }
            & .header-btn {
                margin-left: 30px;
                @media #{$lg} {
                    display: none;
                }
            }
        }
        @media #{$md} {
            margin-right: 40px;
        }
        @media #{$xs} {
            margin-right: 30px;
        }
    }
}
.nav-logo,
.logo {
    & a {
        @include transition(0s);
    }
	& img {
        max-width: 120px;
        @include transition(0s);
    }
    & .dark-logo {
        display: none;
    }
}
.sticky-menu {
	position: fixed;
	left: 0;
	margin: auto;
	top: 0;
	width: 100%;
	z-index: 99;
	background: var(--tg-common-color-white);
	-webkit-animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
	animation: 500ms ease-in-out 0s normal none 1 running fadeInDown;
	-webkit-box-shadow: 0 10px 15px rgba(25, 25, 25, 0.05);
	box-shadow: 0 10px 15px rgba(25, 25, 25, 0.05);
	border-radius: 0;
    &.transparent-header {
        padding: 0 0;
        @media #{$md} {
            padding: 20px 0;
        }
    }
    & .logo {
        & img {
            max-width: 90px;
        }
    }
    &.tg-header__area .mobile-nav-toggler {
        @media #{$md} {
            font-size: 28px;
            margin-top: 1px;
        }
    }
    & .tgmenu__navbar-wrap ul li .sub-menu {
        border-radius: 0 0 14px 14px;
    }
}
.header {
    &-style-two {
        & .tgmenu__navbar-wrap > ul.navigation li:last-child > a {
            padding-right: 0;
        }
    }
    &-style-three {
        & .tgmenu__navbar-wrap > ul.navigation {
            margin: 0 auto;
        }
        & .tgmenu__action > ul .header-social a {
            font-size: 24px;
            line-height: 1;
            @media #{$lg} {
                font-size: 20px;
            }
            & svg {
                display: block;
            }
        }
        @media #{$lg} {
            .tgmenu__action > ul {
                margin-left: 0;
            }
        }
    }
}