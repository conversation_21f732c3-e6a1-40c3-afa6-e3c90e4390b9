@use '../utils' as *;

/*=============================
    00. Wallet Connect
===============================*/
.connect__modal {
    & .modal-dialog {
        max-width: 440px;
        margin: auto;
        margin-top: 50px;
        @media #{$xs} {
            margin-top: 20px;
        }
    }
    & .modal-content {
        background: var(--tg-common-color-white);
        backdrop-filter: blur(5px);
        border-radius: 15px;
        overflow: hidden;
        border: none;
        @media #{$xs} {
            margin: 0 15px;
        }
    }
}
.modal {
    &__wrapper {
        height: 100%;
        width: 100%;
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        padding: 45px;
        padding-bottom: 50px;
        position: relative;
        overflow: hidden;
        @media #{$xs} {
            padding: 40px 25px;
        }
        &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-image: url(../img/others/gradient-circle.svg);
            background-position: center;
            background-size: contain;
            background-repeat: no-repeat;
            -webkit-filter: blur(80px);
            filter: blur(80px);
            mix-blend-mode: overlay;
            z-index: -1;
        }
    }
    &__header {
        & button {
            background: transparent;
            border: none;
            outline: none;
            height: 45px;
            width: 45px;
            position: absolute;
            right: 0px;
            top: 0px;
            &::before {
                content: "";
                background: rgba(0, 0, 0, 0.1);
                height: 130%;
                width: 240%;
                position: absolute;
                right: -40px;
                top: -14px;
                transform: rotate(45deg);
            }
            & i {
                position: absolute;
                bottom: 16px;
                right: 11px;
                color: var(--tg-heading-color);
                font-size: 14px;
                @include transition(.3s);
            }
            &:hover {
                & i {
                    color: var(--tg-theme-primary);
                }
            }
        }
        & .title {
            font-style: normal;
            font-weight: 400;
            font-size: 24px;
            line-height: 28px;
            text-align: center;
            text-transform: uppercase;
            max-width: 280px;
            margin: auto;
            margin-bottom: 26px;
            @media #{$xs} {
                margin-bottom: 15px;
            }
        }
    }
    &__body {
        padding: 0 0;
        & p:not(.privacy-text) {
            font-style: normal;
            font-weight: 500;
            font-size: 16px;
            text-align: center;
            margin: 0 0 35px;
        }
        & .privacy-text {
            font-style: normal;
            font-weight: 500;
            font-size: 15px;
            text-align: center;
            margin: 0 0;
            & a {
                color: var(--tg-heading-color);
            }
        }
    }
}
.connect {
    &__section {
        & .list-wrap {
            & li {
                @include flexbox();
                align-items: center;
                background: rgba(0, 0, 0, 0.05);
                backdrop-filter: blur(10px);
                padding: 15px 30px;
                margin-bottom: 20px;
                position: relative;
                cursor: pointer;
                text-align: left;
                @include border-radius(10px);
                @media #{$xs} {
                    padding: 13px 25px;
                }
                & span {
                    width: 35px;
                    display: inline-block;
                    margin-right: 20px;
                }
                &::before {
                    content: "\f105";
                    position: absolute;
                    right: 30px;
                    color: rgba(0, 0, 0, 0.7);
                    font-family: var(--tg-fa-icon-font-family);
                    font-weight: var(--tg-fw-bold);
                    @media #{$xs} {
                        right: 25px;
                    }
                }
                & a {
                    color: var(--tg-heading-color);
                    @include flexbox();
                    align-items: center;
                    gap: 10px 15px;
                    font-weight: var(--tg-fw-bold);
                    width: 100%;
                    & img {
                        max-width: 30px;
                        max-height: 28px;
                    }
                }
            }
        }
    }
}