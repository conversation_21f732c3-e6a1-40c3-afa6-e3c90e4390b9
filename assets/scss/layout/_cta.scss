@use '../utils' as *;

/*=============================
    00. CTA
===============================*/
.cta {
    &-area {
        padding: 128px 0;
        position: relative;
        z-index: 1;
        @media #{$lg} {
            padding: 80px 0;
        }
        &.style-two {
            padding: 30px 0 32px;
            & img {
                @include transition(0s);
            }
        }
    }
    &-bg {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-position: center;
        background-size: cover;
        z-index: -1;
        opacity: .1;
    }
    &__content {
        position: relative;
        margin-top: 20px;
        @media #{$xs} {
            margin-top: 0;
        }
        & > img {
            position: absolute;
        }
        & .title {
            font-size: 96px;
            line-height: 1;
            margin: 0 0 60px;
            @media #{$lg} {
                font-size: 80px;
            }
            @media #{$xs} {
                font-size: 46px;
                margin: 0 0 40px;
            }
        }
        & .btn {
            font-size: 18px;
            padding: 21px 40px;
            @media #{$xs} {
                font-size: 16px;
                padding: 19px 35px;
            }
        }
        &.style-two {
            & .title {
                font-size: 80px;
                line-height: 1;
                margin: 0 0 23px;
                @media #{$lg} {
                    font-size: 68px;
                }
                @media #{$md} {
                    font-size: 55px;
                }
                @media #{$xs} {
                    font-size: 36px;
                }
                @media #{$sm} {
                    font-size: 50px;
                }
            }
            & p {
                font-size: 24px;
                line-height: 1.4;
                margin: 0 0 65px;
                @media #{$lg} {
                    margin: 0 0 50px;
                }
                @media #{$xs} {
                    margin: 0 0 35px;
                    font-size: 20px;
                }
            }
        }
        &.style-three {
            & .title {
                margin: 0 0 25px;
            }
            & p {
                font-size: 24px;
                line-height: 1.4;
                margin: 0 0 45px;
                @media #{$lg} {
                    margin: 0 0 40px;
                }
                @media #{$xs} {
                    margin: 0 0 35px;
                    font-size: 20px;
                }
            }
            & .btn {
                padding: 19px 40px;
            }
        }
    }
}