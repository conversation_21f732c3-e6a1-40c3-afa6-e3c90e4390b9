@use '../utils' as *;

/*=============================
    00. Team
===============================*/
.team {
    &__grid-wrapper {
        display: -ms-grid;
        display: grid;
        -ms-grid-columns: repeat(5, 1fr);
        grid-template-columns: repeat(5, 1fr);
        align-items: start;
        gap: 32px 32px;
        @media #{$lg} {
            gap: 30px 30px;
        }
        @media #{$md} {
            grid-template-columns: repeat(3, 1fr);
        }
        @media #{$xs} {
            grid-template-columns: repeat(2, 1fr);
            gap: 20px 20px;
        }
        @media #{$sm} {
            gap: 30px 30px;
        }
    }
    &__item {
        padding: 8px;
        @include box-shadow(0 3px 8px -1px rgba(0, 0, 0, 0.08));
        @include border-radius(32px);
        @media #{$xs} {
            @include border-radius(15px);
        }
        &:nth-child(even) {
            margin-top: 32px;
            @media #{$md} {
                margin-top: 0;
            }
        }
        &:nth-child(10n+6),
        &:nth-child(10n+8),
        &:nth-child(10n+10) {
            margin-top: -32px;
            @media #{$md} {
                margin-top: 0;
            }
        }
        &:nth-child(10n+7),
        &:nth-child(10n+9) {
            margin-top: 0;
        }
        &-thumb {
            & img {
                @include border-radius(24px);
                @media #{$xs} {
                    @include border-radius(12px);
                }
            }
        }
        &-content {
            padding: 24px;
            @media #{$lg} {
                padding: 24px 15px;
            }
            @media #{$xs} {
                padding: 20px 10px;
            }
            @media #{$sm} {
                padding: 24px;
            }
            & .name {
                font-size: 18px;
                line-height: 1;
                text-transform: capitalize;
                margin: 0 0 15px;
                @media #{$lg} {
                    font-size: 16px;
                    margin: 0 0 10px;
                }
                @media #{$md} {
                    font-size: 18px;
                    margin: 0 0 15px;
                }
                @media #{$xs} {
                    font-size: 17px;
                    margin: 0 0 15px;
                }
                @media #{$sm} {
                    font-size: 18px;
                    margin: 0 0 15px;
                }
            }
            & .designation {
                font-size: 14px;
                display: block;
                line-height: 1;
            }
        }
    }
    &__social-list {
        @include flexbox();
        align-items: center;
        justify-content: center;
        gap: 18px;
        margin: 30px 0 0;
        @media #{$lg} {
            margin: 20px 0 0;
        }
        @media #{$md} {
            margin: 30px 0 0;
        }
        @media #{$xs} {
            margin: 20px 0 0;
            gap: 14px;
        }
        & li {
            & a {
                display: block;
                line-height: 1;
                color: var(--tg-heading-color);
                font-size: 15px;
                &:hover {
                    color: var(--tg-theme-primary);
                }
            }
        }
    }
    &__inner {
        &-wrap {
            position: relative;
            z-index: 1;
            & .row {
                &>* {
                    &:nth-child(1),
                    &:nth-child(3) {
                        & .team__item-two {
                            margin-top: 30px;
                            @media #{$xs} {
                                margin: 0 0 30px;
                            }
                        }
                    }
                }
            }
        }
        &-bg {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-position: center;
            background-repeat: no-repeat;
            background-size: contain;
            opacity: .1;
            z-index: -1;
        }
    }
    &__item {
        &-two {
            margin: 0 0 50px;
            @media #{$xs} {
                margin: 0 0 30px;
            }
            &-thumb {
                max-width: 160px;
                background: var(--tg-common-color-white);
                padding: 8px;
                box-shadow: 0 3px 8px -1px rgba(0, 0, 0, 0.08);
                @include border-radius(50%);
                margin: 0 auto;
                & img {
                    @include border-radius(50%);
                }
            }
            &-content {
                padding: 24px;
                @media #{$xs} {
                    padding: 20px 0;
                }
                & .name {
                    font-size: 22px;
                    line-height: 1;
                    margin: 0 0 15px;
                    @media #{$xs} {
                        font-size: 20px;
                    }
                    @media #{$sm} {
                        font-size: 22px;
                    }
                }
                & .designation {
                    font-size: 16px;
                    display: block;
                    line-height: 1;
                }
            }
        }
    }
    &-style-three {
        & .team__item-two {
            margin: 0 0 30px;
        }
        & .team__item-two-thumb {
            border: 1px solid rgba(131, 131, 131, 0.25);
            @include box-shadow(none);
            @include border-radius(32px);
            & img {
                @include border-radius(24px);
            }
        }
    }
}