@use '../utils' as *;

/*=============================
    00. Blog
===============================*/
.blog {
    &-area {
        padding-bottom: 50px;
    }
    &-post {
        &-item {
            margin: 0 0 60px;
        }
        &-thumb {
            margin: 0 0 30px;
            & img {
                @include border-radius(8px);
            }
        }
        &-meta {
            & .list-wrap {
                @include flexbox();
                flex-wrap: wrap;
                margin: 0 0 10px;
                & li {
                    @include flexbox();
                    align-items: center;
                    font-size: 16px;
                    &::after {
                        content: "|";
                        display: block;
                        padding: 0 15px;
                        font-size: 14px;
                        opacity: .2;
                    }
                    &:last-child {
                        &::after {
                            display: none;
                        }
                    }
                    & i {
                        margin-right: 10px;
                        font-size: 14px;
                        color: var(--tg-theme-primary);
                    }
                    & a {
                        display: block;
                        color: var(--tg-body-color);
                        &:hover {
                            color: var(--tg-theme-primary);
                        }
                    }
                }
            }
        }
        &-content {
            & .title {
                margin: 0 0 20px;
                font-size: 38px;
                @media #{$lg} {
                    font-size: 34px;
                }
                @media #{$xs} {
                    font-size: 26px;
                }
                @media #{$sm} {
                    font-size: 30px;
                }
            }
            & p {
                margin: 0 0;
            }
        }
    }
    &-sidebar {
        margin-left: 30px;
        @media #{$lg} {
            margin-left: 0;
        }
        @media #{$md} {
            margin: 60px 0 0;
        }
        & .widget {
            border: 1px solid rgba(131, 131, 131, 0.2);
            background: transparent;
            padding: 40px 30px;
            margin-bottom: 40px;
            @include border-radius(8px);
            &:last-child {
                margin-bottom: 0;
            }
            @media #{$lg} {
                padding: 30px 25px;
            }
            &-title {
                font-size: 22px;
                margin: 0 0 20px;
            }
            &_categories {
                & ul {
                    margin: 0;
                    padding: 0;
                    & li {
                        list-style: none;
                        position: relative;
                        margin: 0 0 14px;
                        &:last-child {
                            margin: 0 0;
                        }
                        & a {
                            letter-spacing: 0;
                            text-transform: uppercase;
                            color: var(--tg-body-color);
                            font-weight: 600;
                            font-size: 14px;
                            display: flex;
                            align-items: center;
                            border: 1px solid rgba(131, 131, 131, 0.2);
                            background: transparent;
                            padding: 11px 18px;
                            @include border-radius(8px);
                            &:hover {
                                color: var(--tg-heading-color);
                            }
                        }
                        & .float-right {
                            margin-left: auto;
                            position: absolute;
                            top: 0;
                            right: 18px;
                            line-height: 1;
                            min-height: 45px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 14px;
                            color: var(--tg-heading-color);
                            font-weight: 600;
                        }
                    }
                }
            }
        }
    }
    &-details {
        &-content {
            & p {
                margin-bottom: 15px;
            }
        }
        &-bottom {
            padding: 33px 0;
            border-top: 1px solid rgba(131, 131, 131, 0.2);
            border-bottom: 1px solid rgba(131, 131, 131, 0.2);
            margin: 40px 0 0;
            clear: both;
            overflow: hidden;
        }
    }
    &-inner {
        &-wrapper {
            margin: 40px 0 40px;
            & .gx-4 {
                --bs-gutter-x: 20px;
            }
        }
        &-title {
            margin: 0 0 15px;
        }
        &-content {
            & ul {
                & li {
                    margin: 0 0 7px;
                    font-size: 17px;
                    &:last-child {
                        margin: 0 0;
                    }
                    & i {
                        font-size: 14px;
                        margin-right: 10px;
                        color: var(--tg-theme-primary);
                    }
                }
            }
        }
        &-images,
        &-img {
            & img {
                height: 260px;
                object-fit: cover;
                @include border-radius(8px);
                @media #{$xs} {
                    height: auto;
                }
            }
        }
        &-images {
            @media #{$xs} {
                margin-top: 20px;
            }
        }
        &-img {
            margin: 0 0 20px;
        }
    }
}
.tg-post-tag {
    display: flex;
    align-items: flex-start;
    @media #{$xs} {
        display: block;
        margin: 0 0 20px;
    }
    & ul {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 10px;
        & li {
            & a {
                font-weight: 700;
                font-size: 13px;
                text-transform: uppercase;
                color: var(--tg-body-color);
                display: block;
                padding: 5px 18px;
                border: 1px solid rgba(131, 131, 131, 0.2);
                @include border-radius(4px);
                &:hover {
                    border-color: var(--tg-theme-primary);
                    background: var(--tg-theme-primary);
                    color: var(--tg-common-color-white);
                }
            }
        }
    }
}
.blog-details-social {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    @media #{$xs} {
        justify-content: flex-start;
    }
    & ul {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        & li {
            margin-right: 18px;
            &:last-child {
                margin-right: 0;
            }
            & a {
                font-size: 16px;
                color: var(--tg-body-color);
                &:hover {
                    color: var(--tg-theme-primary);
                }
            }
        }
    }
}
.blog-details-social .social-title,
.tg-post-tag .tag-title {
    font-size: 18px;
    margin-right: 20px;
    margin-top: 5px;
    margin-bottom: 5px;
    flex: 0 0 auto;
}
.tg-post-tag .tag-title {
    @media #{$xs} {
        margin: 0 0 10px;
    }
}
.sidebar-search-form {
    & input {
        background: #f8f4ff;
        border: none;
        color: var(--tg-heading-color);
        width: 100%;
        font-size: 16px;
        @include border-radius(8px);
        letter-spacing: 0;
        text-transform: capitalize;
        padding: 19px 50px 19px 20px;
        height: 60px;
    }
    & button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: 20px;
        border: none;
        background: transparent;
        padding: 0;
        font-size: 16px;
        color: var(--tg-theme-primary);
    }
}
.rc-post {
    &-item {
        @include flexbox();
        align-items: center;
        margin-bottom: 20px;
        @media #{$lg} {
            display: block;
            margin: 0 0 25px;
        }
        &:last-child {
            margin: 0 0;
        }
    }
    &-thumb {
        width: 97px;
        flex: 0 0 97px;
        margin-right: 20px;
        @media #{$lg} {
            width: 100%;
            flex: 0 0 100%;
            margin: 0 0 15px;
        }
        & img {
            width: 100%;
            height: 85px;
            object-fit: cover;
            @include border-radius(8px);
            @media #{$lg} {
                height: 120px;
            }
        }
    }
    &-content {
        & .date {
            display: block;
            font-weight: 700;
            font-size: 12px;
            letter-spacing: 0.05em;
            text-transform: uppercase;
            color: var(--tg-body-color);
            margin-bottom: 5px;
            & i {
                color: var(--tg-theme-primary);
            }
        }
        & .title {
            font-size: 16px;
            line-height: 1.4;
            margin: 0 0;
            @media #{$md} {
                font-size: 17px;
            }
        }
    }
}
.tagcloud {
    @include flexbox();
    flex-wrap: wrap;
    gap: 10px 10px;
    & a {
        display: block;
        font-size: 13px !important;
        padding: 5px 18px;
        border-radius: 5px;
        -webkit-transition: 0.3s;
        -o-transition: 0.3s;
        transition: 0.3s;
        margin: 0 !important;
        background: transparent;
        font-weight: var(--tg-fw-bold);
        color: var(--tg-body-color);
        text-transform: uppercase;
        line-height: 1.5;
        border: 1px solid rgba(131, 131, 131, 0.2);
        &:hover {
            border-color: var(--tg-theme-primary);
            background: var(--tg-theme-primary);
            color: var(--tg-common-color-white);
        }
    }
}
.single.single-post {
    & .breadcrumb__content .title {
        font-size: 60px;
        @media #{$xl} {
            font-size: 50px;
        }
        @media #{$lg} {
            font-size: 46px;
        }
        @media #{$xs} {
            font-size: 36px;
            line-height: 1.05;
        }
        @media #{$sm} {
            font-size: 40px;
        }
    }
}