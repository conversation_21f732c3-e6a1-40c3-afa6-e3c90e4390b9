<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background elements -->
  <circle cx="400" cy="300" r="250" fill="#f1f5f9" opacity="0.5" />
  <circle cx="400" cy="300" r="200" fill="#e2e8f0" opacity="0.5" />
  
  <!-- Dashboard frame -->
  <rect x="150" y="100" width="500" height="400" rx="20" fill="white" stroke="#d1d5db" stroke-width="2" filter="drop-shadow(0 20px 30px rgba(0, 0, 0, 0.2))"/>
  
  <!-- Header -->
  <rect x="150" y="100" width="500" height="60" rx="20" fill="#206bc4" />
  <rect x="150" y="100" width="500" height="60" rx="20" fill="url(#header-gradient)" />
  <circle cx="185" cy="130" r="12" fill="white" opacity="0.9" />
  <rect x="210" y="124" width="100" height="12" rx="6" fill="white" opacity="0.7" />
  <rect x="500" y="124" width="120" height="12" rx="6" fill="white" opacity="0.5" />
  
  <!-- Sidebar -->
  <rect x="150" y="160" width="100" height="340" fill="#f8fafc" />
  <rect x="165" y="190" width="70" height="12" rx="6" fill="#94a3b8" />
  <rect x="165" y="220" width="70" height="12" rx="6" fill="#94a3b8" />
  <rect x="165" y="250" width="70" height="12" rx="6" fill="#94a3b8" />
  <rect x="165" y="280" width="70" height="12" rx="6" fill="#94a3b8" />
  <rect x="165" y="310" width="70" height="12" rx="6" fill="#94a3b8" />
  <rect x="165" y="340" width="70" height="12" rx="6" fill="#94a3b8" />
  
  <!-- Main content -->
  <rect x="270" y="180" width="360" height="50" rx="10" fill="#f1f5f9" />
  <rect x="285" y="199" width="220" height="12" rx="6" fill="#64748b" />
  <rect x="550" y="193" width="60" height="24" rx="12" fill="#206bc4" />
  <rect x="565" y="199" width="30" height="12" rx="6" fill="white" />
  
  <!-- Stats cards -->
  <rect x="270" y="250" width="110" height="100" rx="10" fill="#dbeafe" />
  <rect x="285" y="270" width="80" height="12" rx="6" fill="#3b82f6" />
  <rect x="285" y="295" width="50" height="25" rx="6" fill="#3b82f6" />
  <rect x="285" y="330" width="40" height="8" rx="4" fill="#3b82f6" opacity="0.7" />
  
  <rect x="395" y="250" width="110" height="100" rx="10" fill="#fef3c7" />
  <rect x="410" y="270" width="80" height="12" rx="6" fill="#f59e0b" />
  <rect x="410" y="295" width="50" height="25" rx="6" fill="#f59e0b" />
  <rect x="410" y="330" width="40" height="8" rx="4" fill="#f59e0b" opacity="0.7" />
  
  <rect x="520" y="250" width="110" height="100" rx="10" fill="#dcfce7" />
  <rect x="535" y="270" width="80" height="12" rx="6" fill="#22c55e" />
  <rect x="535" y="295" width="50" height="25" rx="6" fill="#22c55e" />
  <rect x="535" y="330" width="40" height="8" rx="4" fill="#22c55e" opacity="0.7" />
  
  <!-- Chart -->
  <rect x="270" y="370" width="360" height="110" rx="10" fill="#f1f5f9" />
  <rect x="285" y="390" width="100" height="12" rx="6" fill="#64748b" />
  <path d="M285 450 L315 420 L345 435 L375 405 L405 425 L435 395 L465 410 L495 380 L525 400 L555 370 L585 390 L615 360" stroke="#206bc4" stroke-width="3" stroke-linecap="round" />
  <circle cx="315" cy="420" r="4" fill="#206bc4" />
  <circle cx="345" cy="435" r="4" fill="#206bc4" />
  <circle cx="375" cy="405" r="4" fill="#206bc4" />
  <circle cx="405" cy="425" r="4" fill="#206bc4" />
  <circle cx="435" cy="395" r="4" fill="#206bc4" />
  <circle cx="465" cy="410" r="4" fill="#206bc4" />
  <circle cx="495" cy="380" r="4" fill="#206bc4" />
  <circle cx="525" cy="400" r="4" fill="#206bc4" />
  <circle cx="555" cy="370" r="4" fill="#206bc4" />
  <circle cx="585" cy="390" r="4" fill="#206bc4" />
  <circle cx="615" cy="360" r="4" fill="#206bc4" />
  
  <!-- Social media icons -->
  <circle cx="600" cy="500" r="20" fill="#1DA1F2" />
  <path d="M590 500 L598 508 L615 491" stroke="white" stroke-width="3" stroke-linecap="round" />
  
  <circle cx="550" cy="500" r="20" fill="#4267B2" />
  <rect x="542" y="492" width="16" height="16" rx="3" fill="white" />
  
  <!-- Floating elements -->
  <circle cx="100" cy="150" r="30" fill="#206bc4" opacity="0.2" />
  <circle cx="700" cy="450" r="40" fill="#206bc4" opacity="0.2" />
  <circle cx="650" cy="150" r="25" fill="#206bc4" opacity="0.2" />
  <circle cx="150" cy="450" r="35" fill="#206bc4" opacity="0.2" />
  
  <!-- Animated elements -->
  <circle cx="400" cy="50" r="15" fill="#f59e0b" opacity="0.7">
    <animate attributeName="cy" values="50;70;50" dur="4s" repeatCount="indefinite" />
  </circle>
  
  <circle cx="450" cy="550" r="20" fill="#22c55e" opacity="0.7">
    <animate attributeName="cy" values="550;530;550" dur="5s" repeatCount="indefinite" />
  </circle>
  
  <circle cx="100" cy="300" r="25" fill="#3b82f6" opacity="0.5">
    <animate attributeName="cx" values="100;120;100" dur="6s" repeatCount="indefinite" />
  </circle>
  
  <circle cx="700" cy="300" r="25" fill="#ec4899" opacity="0.5">
    <animate attributeName="cx" values="700;680;700" dur="7s" repeatCount="indefinite" />
  </circle>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="header-gradient" x1="0" y1="0" x2="1" y2="0">
      <stop offset="0%" stop-color="#206bc4" />
      <stop offset="100%" stop-color="#3b82f6" />
    </linearGradient>
  </defs>
</svg>
