/**
 * Tracking Group Wizard
 *
 * A JavaScript-based wizard for creating tracking groups without relying on server-side sessions.
 * This approach ensures data integrity and provides a better user experience.
 */

class TrackingGroupWizard {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.wizardData = {
            name: '',
            dataSource: 'instagram',
            listType: 'meta:profiles',
            category: 'general',
            subCategory: '',
            list: [],
            maximumPostsPerProfile: 10,
            maximumCommentsPerPost: 0,
            runProfileAIAnalysis: false,
            keepDataUpdated: true,
            customFields: []
        };

        this.init();
    }

    init() {
        // Initialize the wizard
        this.setupEventListeners();
        this.updateProgressIndicator();
        this.loadStepContent(this.currentStep);

        // Check if there's saved data in localStorage
        const savedData = localStorage.getItem('tracking_group_wizard_data');
        if (savedData) {
            try {
                this.wizardData = JSON.parse(savedData);
                this.populateFormFields();
            } catch (e) {
                console.error('Error parsing saved wizard data', e);
                localStorage.removeItem('tracking_group_wizard_data');
            }
        }
    }

    setupEventListeners() {
        // Navigation buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('#next-step')) {
                this.nextStep();
            } else if (e.target.matches('#prev-step')) {
                this.prevStep();
            } else if (e.target.matches('#create-tracking-group')) {
                this.createTrackingGroup();
            } else if (e.target.matches('.step-indicator')) {
                const step = parseInt(e.target.dataset.step);
                if (step <= this.currentStep) {
                    this.goToStep(step);
                }
            } else if (e.target.matches('.edit-step')) {
                const step = parseInt(e.target.dataset.step);
                this.goToStep(step);
            }
        });

        // Form inputs
        document.addEventListener('change', (e) => {
            if (e.target.matches('input, select, textarea')) {
                this.saveFormData();
            }
        });

        // Profile list counter
        const profileListInput = document.getElementById('list');
        if (profileListInput) {
            profileListInput.addEventListener('input', () => {
                this.updateProfileCount();
            });
        }
    }

    saveFormData() {
        const form = document.querySelector('.wizard-form');
        if (!form) return;

        const formData = new FormData(form);

        // Update wizardData based on current step
        switch (this.currentStep) {
            case 1: // Basic Information
                this.wizardData.name = formData.get('name') || '';
                this.wizardData.dataSource = formData.get('dataSource') || 'instagram';
                this.wizardData.listType = formData.get('listType') || 'meta:profiles';
                break;

            case 2: // Profiles List
                const listText = formData.get('list') || '';
                this.wizardData.list = this.parseProfileList(listText);
                break;

            case 3: // Tracking Options
                this.wizardData.category = formData.get('category') || 'general';
                this.wizardData.subCategory = formData.get('subCategory') || '';
                this.wizardData.maximumPostsPerProfile = parseInt(formData.get('maximumPostsPerProfile') || 10);
                this.wizardData.maximumCommentsPerPost = parseInt(formData.get('maximumCommentsPerPost') || 0);
                this.wizardData.runProfileAIAnalysis = formData.has('runProfileAIAnalysis');
                this.wizardData.keepDataUpdated = formData.has('keepDataUpdated');
                break;
        }

        // Save to localStorage
        localStorage.setItem('tracking_group_wizard_data', JSON.stringify(this.wizardData));
    }

    parseProfileList(text) {
        if (!text) return [];

        // Split by newlines or commas
        const items = text.split(/[\n,]+/).map(item => item.trim()).filter(item => item);
        return [...new Set(items)]; // Remove duplicates
    }

    updateProfileCount() {
        const profileListInput = document.getElementById('list');
        const profileCountElement = document.getElementById('profile-count');

        if (profileListInput && profileCountElement) {
            const profiles = this.parseProfileList(profileListInput.value);
            profileCountElement.textContent = `${profiles.length} items`;

            // Update wizardData
            this.wizardData.list = profiles;
            localStorage.setItem('tracking_group_wizard_data', JSON.stringify(this.wizardData));
        }
    }

    populateFormFields() {
        const form = document.querySelector('.wizard-form');
        if (!form) return;

        // Populate form fields based on current step
        switch (this.currentStep) {
            case 1: // Basic Information
                this.setFormValue('name', this.wizardData.name);
                this.setFormValue('dataSource', this.wizardData.dataSource);
                this.setFormValue('listType', this.wizardData.listType);
                break;

            case 2: // Profiles List
                this.setFormValue('list', this.wizardData.list.join('\n'));
                this.updateProfileCount();
                break;

            case 3: // Tracking Options
                this.setFormValue('category', this.wizardData.category);
                this.setFormValue('subCategory', this.wizardData.subCategory);
                this.setFormValue('maximumPostsPerProfile', this.wizardData.maximumPostsPerProfile);
                this.setFormValue('maximumCommentsPerPost', this.wizardData.maximumCommentsPerPost);
                this.setFormValue('runProfileAIAnalysis', this.wizardData.runProfileAIAnalysis);
                this.setFormValue('keepDataUpdated', this.wizardData.keepDataUpdated);
                break;

            case 4: // Review
                this.updateReviewPage();
                break;
        }
    }

    setFormValue(name, value) {
        const element = document.querySelector(`[name="${name}"]`);
        if (!element) return;

        if (element.type === 'checkbox') {
            element.checked = !!value;
        } else {
            element.value = value;
        }
    }

    updateProgressIndicator() {
        const indicators = document.querySelectorAll('.step-indicator');
        indicators.forEach(indicator => {
            const step = parseInt(indicator.dataset.step);

            // Reset classes
            indicator.classList.remove('bg-primary-600', 'text-white', 'border-primary-600', 'text-primary-600', 'bg-gray-200', 'text-gray-700');

            if (step === this.currentStep) {
                // Current step
                indicator.classList.add('bg-primary-600', 'text-white', 'border-primary-600');
            } else if (step < this.currentStep) {
                // Completed step
                indicator.classList.add('text-primary-600', 'border-primary-600');
            } else {
                // Future step
                indicator.classList.add('bg-gray-200', 'text-gray-700');
            }
        });
    }

    loadStepContent(step) {
        // This would typically fetch the step content via AJAX
        // For now, we'll just show/hide the appropriate step content
        document.querySelectorAll('.wizard-step').forEach(stepElement => {
            stepElement.classList.add('hidden');
        });

        const currentStepElement = document.querySelector(`.wizard-step[data-step="${step}"]`);
        if (currentStepElement) {
            currentStepElement.classList.remove('hidden');
        }

        // Update buttons
        const nextButton = document.getElementById('next-step');
        const prevButton = document.getElementById('prev-step');
        const createButton = document.getElementById('create-tracking-group');

        if (nextButton) nextButton.classList.toggle('hidden', step === this.totalSteps);
        if (prevButton) prevButton.classList.toggle('hidden', step === 1);
        if (createButton) createButton.classList.toggle('hidden', step !== this.totalSteps);

        // Populate form fields
        this.populateFormFields();
    }

    nextStep() {
        this.saveFormData();

        if (this.currentStep < this.totalSteps) {
            this.currentStep++;
            this.updateProgressIndicator();
            this.loadStepContent(this.currentStep);
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateProgressIndicator();
            this.loadStepContent(this.currentStep);
        }
    }

    goToStep(step) {
        if (step >= 1 && step <= this.totalSteps) {
            this.saveFormData();
            this.currentStep = step;
            this.updateProgressIndicator();
            this.loadStepContent(this.currentStep);
        }
    }

    updateReviewPage() {
        // Update the review page with the current data
        const nameElement = document.getElementById('review-name');
        const dataSourceElement = document.getElementById('review-data-source');
        const listTypeElement = document.getElementById('review-list-type');
        const profileCountElement = document.getElementById('review-profile-count');
        const categoryElement = document.getElementById('review-category');
        const postsPerProfileElement = document.getElementById('review-posts-per-profile');
        const commentsPerPostElement = document.getElementById('review-comments-per-post');
        const aiAnalysisElement = document.getElementById('review-ai-analysis');
        const keepUpdatedElement = document.getElementById('review-keep-updated');

        if (nameElement) nameElement.textContent = this.wizardData.name || 'Unnamed Group';
        if (dataSourceElement) dataSourceElement.textContent = this.getDataSourceLabel(this.wizardData.dataSource);
        if (listTypeElement) listTypeElement.textContent = this.getListTypeLabel(this.wizardData.listType);
        if (profileCountElement) profileCountElement.textContent = `${this.wizardData.list.length} profiles`;
        if (categoryElement) categoryElement.textContent = this.wizardData.category;
        if (postsPerProfileElement) postsPerProfileElement.textContent = this.wizardData.maximumPostsPerProfile;
        if (commentsPerPostElement) commentsPerPostElement.textContent = this.wizardData.maximumCommentsPerPost;
        if (aiAnalysisElement) aiAnalysisElement.textContent = this.wizardData.runProfileAIAnalysis ? 'Yes' : 'No';
        if (keepUpdatedElement) keepUpdatedElement.textContent = this.wizardData.keepDataUpdated ? 'Yes' : 'No';
    }

    getDataSourceLabel(value) {
        const sources = {
            'instagram': 'Instagram',
            'facebook': 'Facebook',
            'twitter': 'Twitter'
        };
        return sources[value] || value;
    }

    getListTypeLabel(value) {
        const types = {
            'meta:profiles': 'Profiles',
            'meta:pages': 'Pages',
            'meta:groups': 'Groups'
        };
        return types[value] || value;
    }

    createTrackingGroup() {
        // Send the data to the server
        fetch('/app/social/profile/tracking/create-ajax', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(this.wizardData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear the saved data
                localStorage.removeItem('tracking_group_wizard_data');

                // Show success message
                const successMessage = document.createElement('div');
                successMessage.className = 'bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4';
                successMessage.innerHTML = `
                    <strong class="font-bold">Success!</strong>
                    <span class="block sm:inline">${data.message}</span>
                `;

                const wizardContent = document.querySelector('.wizard-content');
                wizardContent.prepend(successMessage);

                // Redirect after a delay
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            } else {
                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4';
                errorMessage.innerHTML = `
                    <strong class="font-bold">Error!</strong>
                    <span class="block sm:inline">${data.message}</span>
                `;

                const wizardContent = document.querySelector('.wizard-content');
                wizardContent.prepend(errorMessage);
            }
        })
        .catch(error => {
            console.error('Error creating tracking group', error);
        });
    }
}

// Initialize the wizard when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new TrackingGroupWizard();
});

export default TrackingGroupWizard;
