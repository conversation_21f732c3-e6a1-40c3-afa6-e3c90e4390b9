/*
 * Landing page JavaScript file
 */

// Import Preline UI
import 'preline';

// Import AOS (Animate On Scroll)
import AOS from 'aos';
import 'aos/dist/aos.css';

// Import our custom styles
import './styles/app.css';

// Initialize components
document.addEventListener('DOMContentLoaded', () => {
  // Initialize AOS
  AOS.init({
    duration: 800,
    easing: 'ease-in-out',
    once: true,
    mirror: false
  });

  // Initialize Preline
  if (typeof HSStaticMethods !== 'undefined') {
    HSStaticMethods.autoInit();
  }

  // Navbar scroll behavior
  const header = document.querySelector('#header');
  if (header) {
    window.addEventListener('scroll', () => {
      if (window.scrollY > 50) {
        header.classList.add('bg-white', 'shadow-md', 'header-scrolled');
        header.classList.remove('bg-transparent');
      } else {
        header.classList.remove('bg-white', 'shadow-md', 'header-scrolled');
        header.classList.add('bg-transparent');
      }
    });
  }

  // Counter animation
  const counters = document.querySelectorAll('.counter');
  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-target'));
    const duration = 2000; // 2 seconds
    const step = target / (duration / 16); // 60fps
    let current = 0;

    const updateCounter = () => {
      current += step;
      if (current < target) {
        counter.textContent = Math.ceil(current).toLocaleString();
        requestAnimationFrame(updateCounter);
      } else {
        counter.textContent = target.toLocaleString();
      }
    };

    const observer = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting) {
        updateCounter();
        observer.disconnect();
      }
    }, { threshold: 0.5 });

    observer.observe(counter);
  });
});
