// Modern Dashboard JavaScript

// Theme management
class ThemeManager {
  constructor() {
    this.themeToggle = document.getElementById('theme-toggle');
    this.html = document.documentElement;
    this.initTheme();
    this.setupListeners();
  }

  initTheme() {
    // Get theme from cookie or default to system preference
    const savedTheme = this.getCookie('theme');
    if (savedTheme) {
      this.setTheme(savedTheme);
    } else {
      // Check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      this.setTheme(prefersDark ? 'dark' : 'light');
    }
  }

  setupListeners() {
    if (this.themeToggle) {
      this.themeToggle.addEventListener('click', () => {
        const currentTheme = this.html.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
        this.setCookie('theme', newTheme, 365);
      });
    }

    // Listen for system preference changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
      if (!this.getCookie('theme')) {
        this.setTheme(e.matches ? 'dark' : 'light');
      }
    });
  }

  setTheme(theme) {
    this.html.setAttribute('data-theme', theme);
    // Dispatch event for other components to react to theme change
    window.dispatchEvent(new CustomEvent('themechange', { detail: { theme } }));
  }

  getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
  }

  setCookie(name, value, days) {
    const date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value};path=/;expires=${date.toUTCString()}`;
  }
}

// Sidebar management
class SidebarManager {
  constructor() {
    this.sidebar = document.getElementById('sidebar');
    this.openButton = document.getElementById('open-sidebar');
    this.closeButton = document.getElementById('close-sidebar');
    this.setupListeners();
  }

  setupListeners() {
    if (this.openButton) {
      this.openButton.addEventListener('click', () => {
        this.sidebar.classList.remove('-translate-x-full');
      });
    }

    if (this.closeButton) {
      this.closeButton.addEventListener('click', () => {
        this.sidebar.classList.add('-translate-x-full');
      });
    }

    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', (e) => {
      if (window.innerWidth < 1024 && 
          this.sidebar && 
          !this.sidebar.contains(e.target) && 
          this.openButton && 
          !this.openButton.contains(e.target)) {
        this.sidebar.classList.add('-translate-x-full');
      }
    });
  }
}

// User menu management
class UserMenuManager {
  constructor() {
    this.menuButton = document.getElementById('user-menu-button');
    this.menu = document.getElementById('user-menu');
    this.setupListeners();
  }

  setupListeners() {
    if (this.menuButton && this.menu) {
      this.menuButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.menu.classList.toggle('hidden');
      });

      // Close when clicking outside
      document.addEventListener('click', (e) => {
        if (!this.menuButton.contains(e.target) && !this.menu.contains(e.target)) {
          this.menu.classList.add('hidden');
        }
      });
    }
  }
}

// Organization switcher
class OrganizationSwitcher {
  constructor() {
    this.switcher = document.getElementById('switch-org');
    this.setupListeners();
  }

  setupListeners() {
    if (this.switcher) {
      this.switcher.addEventListener('change', () => {
        window.location.href = `/?switch-to-org=${this.switcher.value}`;
      });
    }
  }
}

// Auto-refresh functionality
class AutoRefresh {
  constructor(interval = 10000) {
    this.interval = interval;
    this.refreshableElements = [
      { selector: '#social-tracking-group-cards-controller', method: 'render' },
      { selector: '[data-live-id="Dashboard:StatsSummary"]', method: 'render' },
      { selector: '[data-live-id="Dashboard:ActivityChart"]', method: 'render' },
      { selector: '[data-live-id="Dashboard:RecentActivity"]', method: 'render' }
    ];
    this.setupRefresh();
  }

  setupRefresh() {
    setInterval(() => {
      this.refreshableElements.forEach(element => {
        const el = document.querySelector(element.selector);
        if (el && el.__component) {
          el.__component[element.method]();
        }
      });
    }, this.interval);
  }
}

// Initialize all managers when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ThemeManager();
  new SidebarManager();
  new UserMenuManager();
  new OrganizationSwitcher();
  
  // Only initialize auto-refresh on dashboard pages
  if (document.getElementById('activity-chart')) {
    new AutoRefresh();
  }
});

// Export for potential use in other modules
export { ThemeManager, SidebarManager, UserMenuManager, OrganizationSwitcher, AutoRefresh };
