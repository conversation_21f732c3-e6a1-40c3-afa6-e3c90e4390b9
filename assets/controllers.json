{"controllers": {"@arkounay/ux-collection": {"collection": {"enabled": true, "fetch": "eager", "autoimport": {"@arkounay/ux-collection/src/style.css": true, "@arkounay/ux-collection/src/style-when-not-using-bootstrap-5.css": false}}, "tabbed_collection": {"enabled": true, "fetch": "eager", "autoimport": {"@arkounay/ux-collection/src/tabbed-style.css": true}}}, "@symfony/ux-live-component": {"live": {"enabled": true, "fetch": "eager", "autoimport": {"@symfony/ux-live-component/dist/live.min.css": true}}}, "@symfony/ux-turbo": {"turbo-core": {"enabled": false, "fetch": "eager"}, "mercure-turbo-stream": {"enabled": false, "fetch": "eager"}}}, "entrypoints": []}