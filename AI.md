# Magnora System Guide for AI Agents

This document provides essential information about the Magnora platform architecture, data flows, and key concepts to help AI agents understand and work with the codebase efficiently.

## System Overview

Magnora is a data intelligence platform that collects, processes, and analyzes social media data from various sources (currently Facebook and Instagram). The platform is built on Symfony 7.2 and follows a credit-based SaaS model.

## Core Entities

### Organization Structure

- **Organization**: Top-level entity that owns tracking groups and has credits
- **OrganizationUser**: Links users to organizations with specific roles
- **User**: Individual user accounts with authentication

### Data Collection

- **TrackingGroup**: Groups profiles for tracking with specific settings
  - Has a DataSource (Facebook/Instagram)
  - Contains configuration for maximum posts/comments
  - Can be set for automatic updates
  - Consumes credits when fetching data
  - Belongs to an Organization

- **Profile**: Represents a social media profile
  - Contains metadata about the profile
  - Linked to Media entities

- **Media**: Represents posts/content from profiles
  - Contains content, engagement metrics
  - May have associated Comments

- **Comment**: Represents comments on Media

### Custom Fields

- **CustomField**: Organization-level custom fields (global)
- **CustomFieldTemplate**: Templates for entity-specific custom fields

### AI Analysis

- **AIAnalysis**: Stores AI-generated analysis for various entities
  - Links to the analyzed entity via entityId and entityType
  - Contains analysis results and metadata

## Key Services

### Data Collection

- **MetaService**: Handles API communication with Facebook/Instagram
- **ProfileUpdateService**: Manages the process of updating profile data
  - Fetches profile data
  - Creates/updates Media and Comments
  - Triggers AI analysis if configured
  - Tracks credit usage

### AI Analysis

- **ProfileAIAnalysisService**: Analyzes profile data
  - Processes profile information, images
  - Generates insights about the profile

- **MediaAIAnalysisService**: Analyzes media content
  - Processes text content (MODE_TEXT_ONLY)
  - Processes media content (MODE_MEDIA_ONLY)
  - Can process both (MODE_ALL)

### Storage

- **S3ProfileUploadService**: Uploads profile data to AWS S3
  - Stores complete profile data as JSON
  - Used for efficient retrieval later

## Data Flow

1. **Tracking Group Creation**:
   - User creates a tracking group in an organization
   - Specifies data source, profiles to track, and settings

2. **Data Collection**:
   - System fetches data from Meta APIs (Facebook/Instagram)
   - Data is processed and stored in the database
   - Complete data is also stored in S3 for efficient retrieval

3. **AI Analysis** (if enabled):
   - Profile data is analyzed for insights
   - Media content is analyzed for context and themes
   - Analysis results are stored in AIAnalysis entities

4. **Credit Deduction**:
   - Credits are deducted from the organization based on usage
   - Tracked per profile, post, and comment

5. **Data Presentation**:
   - Users can view profiles, media, and analysis results
   - Custom fields can be used to add additional information

## Command Line Tools

- **SocialProfileInputCommand**: Manually process a profile
- **SocialTrackingGroupUpdateCommand**: Update all profiles in a tracking group

## Important Considerations

1. **Credit System**: Operations consume credits from the organization's balance
2. **Meta Terms Compliance**: Raw data cannot be exposed via API to end users
3. **Language Support**: AI analysis can be configured for different languages
4. **Automatic Updates**: Tracking groups can be set to update automatically

## Common Workflows

### Adding a New Profile to Track

1. Create or select a TrackingGroup
2. Add profile username to the group
3. System fetches initial data
4. AI analysis runs if enabled
5. Credits are deducted based on data volume

### Updating Profiles

1. Manual update via command or controller
2. Automatic update via scheduled task if configured
3. New data is fetched and stored
4. AI analysis runs on new data if enabled
5. Credits are deducted for new data

### Viewing Profile Data

1. User navigates to profiles section
2. Filters or searches for specific profiles
3. Views profile details, media, and analysis
4. Can add custom field data if configured
