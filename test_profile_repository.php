<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Tests\Repository\Meta\ProfileRepositoryTest;
use Symfony\Bundle\FrameworkBundle\Test\KernelTestCase;

echo "=== Profile Repository Test Runner ===\n";
echo "Testing all ProfileRepository methods to identify GROUP BY error...\n\n";

try {
    $test = new ProfileRepositoryTest();
    $test->setUp();
    
    // Run all tests
    $test->testDirectDatabaseQuery();
    $test->testProfileEntityQuery();
    $test->testGetMetaIdsForOrganization();
    $test->testFindProfilesWithPaginationBasic();
    $test->testFindProfilesWithPlatformFilter();
    $test->testFindProfilesWithSearch();
    $test->testGetAvailablePlatforms();
    $test->testGetProfileStats();
    $test->testFindProfilesWithCustomFilters();
    
    $test->tearDown();
    
} catch (\Exception $e) {
    echo "❌ Test runner failed: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
